import React, { useState, useEffect } from "react";

const MO<PERSON>LE_BREAKPOINT = 768;

export function useIsMobile() {
  const [isMobile, setIsMobile] = useState<boolean | undefined>(undefined);

  useEffect(() => {
    // Check if window is defined (not in SSR)
    if (typeof window !== "undefined") {
      const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
      const onChange = () => {
        setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
      };

      // Use the newer event listener pattern for better browser compatibility
      mql.addEventListener("change", onChange);
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);

      return () => mql.removeEventListener("change", onChange);
    } else {
      // Default to desktop view in SSR
      setIsMobile(false);
    }
  }, []);

  // Return false as default when undefined (during SSR)
  return isMobile === undefined ? false : isMobile;
}

export default useIsMobile;
