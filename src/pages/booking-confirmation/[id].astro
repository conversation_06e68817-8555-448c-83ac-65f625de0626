---
import Layout from "../../components/layout/Layout.astro";

// Get booking ID from URL parameters
const { id } = Astro.params;
const searchParams = Astro.url.searchParams;
const email = searchParams.get("email") || "";
---

<Layout
  title="Booking Confirmation - Perfect Piste"
  description="Your booking has been confirmed. Thank you for choosing Perfect Piste."
>
  <div class="bg-white min-h-[calc(100vh-80px)]">
    <div class="container-custom py-16">
      <div class="max-w-3xl mx-auto text-center">
        <!-- Success Icon -->
        <div
          class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="40"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="text-green-600"
          >
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        </div>

        <!-- Confirmation Message -->
        <h1 class="font-baskervville text-4xl mb-6">Booking Confirmed!</h1>
        <p class="text-lg mb-8">
          Thank you for your booking. We've sent a confirmation email to {email}
          with all the details.
        </p>

        <!-- Booking Reference -->
        <div class="bg-[#285DA6]/5 p-6 rounded-lg mb-10 inline-block">
          <h2 class="text-xl font-medium mb-2">Booking Reference</h2>
          <p
            class="text-2xl font-baskervville text-[#285DA6]"
            id="booking-reference"
          >
            {id || ""}
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row justify-center gap-4">
          <a
            href="/"
            class="px-6 py-3 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 font-karla uppercase tracking-wider transition-all duration-300"
          >
            Return to Home
          </a>
          <a
            href="/stays"
            class="px-6 py-3 border border-[#285DA6] text-[#285DA6] rounded-lg hover:bg-[#285DA6]/5 font-karla uppercase tracking-wider transition-all duration-300"
          >
            Browse More Stays
          </a>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Generate a random booking reference if none is provided
  document.addEventListener("DOMContentLoaded", () => {
    const bookingRefEl = document.getElementById("booking-reference");
    if (bookingRefEl && !bookingRefEl.textContent.trim()) {
      const randomRef = `PP-${Math.floor(Math.random() * 10000)
        .toString()
        .padStart(4, "0")}`;
      bookingRefEl.textContent = randomRef;
    }
  });
</script>
