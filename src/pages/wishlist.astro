---
import Layout from "../components/layout/Layout.astro";
import HotelCard from "../components/cards/HotelCard";
---

<Layout
  title="My Wishlist - Perfect Piste"
  description="View and manage your saved properties in your Perfect Piste wishlist."
>
  <div class="min-h-[calc(100vh-80px)] bg-white">
    <div class="container mx-auto mt-16 mb-16">
      <div class="w-full">
        <!-- <PERSON>er -->
        <div class="mb-12">
          <h2
            class="font-baskervville text-3xl uppercase tracking-[0.1em] text-[#000000] mb-4"
          >
            MY WISHLIST
          </h2>
          <p class="font-baskervville text-base text-[#000000]/80 max-w-3xl">
            Your saved properties and experiences. Explore your favorites and
            plan your perfect ski holiday.
          </p>
        </div>

        <!-- Wishlist Content - Empty State -->
        <div
          class="flex flex-col items-center justify-center py-16 px-4 text-center"
        >
          <div class="w-20 h-20 mb-6 text-[#C3C3C3]">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="80"
              height="80"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path
                d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
              ></path>
            </svg>
          </div>

          <h3 class="font-baskervville text-2xl text-[#000000] mb-3">
            Your wishlist is empty
          </h3>

          <p
            class="font-baskervville text-base text-[#000000]/70 max-w-md mb-8"
          >
            Save your favorite properties and experiences by clicking the heart
            icon while browsing our collection.
          </p>

          <a
            href="/stays"
            class="inline-flex items-center justify-center px-6 py-3 bg-[#285DA6] text-white rounded-md font-karla font-bold text-xs uppercase tracking-[0.05em] hover:bg-[#285DA6]/90 transition-colors"
          >
            EXPLORE PROPERTIES
          </a>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // This script will run on the client side to display wishlist items
  document.addEventListener("DOMContentLoaded", () => {
    try {
      // Get wishlist from localStorage
      const wishlistData = localStorage.getItem("wishlist");
      if (wishlistData) {
        const wishlist = JSON.parse(wishlistData);

        // If wishlist has items, replace the empty state with actual items
        if (wishlist && wishlist.length > 0) {
          const wishlistContainer = document.querySelector(
            ".container > .w-full"
          );

          // Create header with item count and clear button
          const headerHTML = `
            <div class="mb-12">
              <h2 class="font-baskervville text-3xl uppercase tracking-[0.1em] text-[#000000] mb-4">MY WISHLIST</h2>
              <p class="font-baskervville text-base text-[#000000]/80 max-w-3xl">
                Your saved properties and experiences. Explore your favorites and plan your perfect ski holiday.
              </p>
            </div>
            <div class="flex justify-between items-center mb-8">
              <p class="font-baskervville text-base text-[#000000]/80">
                ${wishlist.length} ${wishlist.length === 1 ? "item" : "items"} saved
              </p>
              <button
                id="clear-wishlist"
                class="font-karla font-bold text-xs uppercase tracking-[0.05em] text-[#285DA6] hover:underline"
              >
                CLEAR ALL
              </button>
            </div>
          `;

          // Create grid for wishlist items
          let itemsHTML =
            '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">';

          // Add each wishlist item
          wishlist.forEach((item) => {
            const date = new Date(item.addedAt).toLocaleDateString();
            itemsHTML += `
              <div class="relative">
                <!-- Remove button -->
                <button
                  data-id="${item.id}"
                  class="remove-item absolute top-3 right-3 bg-white rounded-full p-2 shadow-md hover:bg-[#285DA6] hover:text-white transition-colors duration-300 z-10"
                  aria-label="Remove from wishlist"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="text-[#285DA6] hover:text-white transition-colors duration-300"
                  >
                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                  </svg>
                </button>

                <!-- Date added -->
                <div class="absolute bottom-3 right-3 bg-white/80 backdrop-blur-sm px-2 py-1 rounded text-xs text-gray-600 z-10">
                  Added ${date}
                </div>

                <!-- HotelCard -->
                <a href="/stays/${item.id}" class="block">
                  <div class="hotel-card-container"
                    data-id="${item.id}"
                    data-name="${item.name}"
                    data-location="${item.location}"
                    data-image="${item.image}">
                  </div>
                </a>
              </div>
            `;
          });

          itemsHTML += "</div>";

          // Replace the content
          if (wishlistContainer) {
            wishlistContainer.innerHTML = headerHTML + itemsHTML;

            // Add event listener for clear all button
            document
              .getElementById("clear-wishlist")
              ?.addEventListener("click", () => {
                localStorage.removeItem("wishlist");
                // Dispatch custom event to notify other components
                window.dispatchEvent(new CustomEvent("wishlistUpdated"));
                window.location.reload();
              });

            // Add event listeners for remove buttons
            document.querySelectorAll(".remove-item").forEach((button) => {
              button.addEventListener("click", (e) => {
                const id = button.getAttribute("data-id");
                if (id) {
                  const wishlist = JSON.parse(
                    localStorage.getItem("wishlist") || "[]"
                  );
                  const updatedWishlist = wishlist.filter(
                    (item) => item.id !== id
                  );
                  localStorage.setItem(
                    "wishlist",
                    JSON.stringify(updatedWishlist)
                  );
                  // Dispatch custom event to notify other components
                  window.dispatchEvent(new CustomEvent("wishlistUpdated"));
                  window.location.reload();
                }
              });
            });
          }
        }
      }
    } catch (error) {
      console.error("Error loading wishlist:", error);
    }
  });

  // Function to render HotelCard components
  function renderHotelCards() {
    // Find all hotel card containers
    const containers = document.querySelectorAll(".hotel-card-container");

    containers.forEach((container) => {
      const id = container.getAttribute("data-id");
      const name = container.getAttribute("data-name");
      const location = container.getAttribute("data-location");
      const imageUrl = container.getAttribute("data-image");

      if (id && name && location && imageUrl) {
        // Create the HotelCard structure directly
        container.innerHTML = `
          <div class="group">
            <div class="overflow-hidden bg-white border border-[#285DA6]/10 rounded-lg shadow-sm transition-all duration-300 hover:shadow-lg hover:border-[#285DA6]/30 group-hover:translate-y-[-4px]">
              <div class="relative">
                <div class="h-64 overflow-hidden">
                  <img
                    src="${imageUrl}"
                    alt="${name}"
                    class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                </div>
              </div>

              <div class="p-6">
                <div class="flex items-center mb-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    stroke="none"
                    class="text-[#285DA6] mr-1"
                  >
                    <polygon
                      points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                    ></polygon>
                  </svg>
                  <span class="text-sm font-medium">4.5</span>
                  <span class="mx-2 text-foreground/30">•</span>
                  <span class="text-sm text-foreground/70">${location}</span>
                </div>

                <h3
                  class="text-xl font-baskervville mb-3 group-hover:text-[#285DA6] transition-colors"
                >
                  ${name}
                </h3>

                <p class="text-foreground/70 text-sm mb-4 line-clamp-2">
                  Experience luxury and comfort in this exceptional accommodation, offering the perfect blend of elegance and mountain charm.
                </p>

                <div class="flex flex-wrap gap-2 mb-4">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-karla bg-[#285DA6]/5 text-[#285DA6]">
                    Mountain View
                  </span>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-karla bg-[#285DA6]/5 text-[#285DA6]">
                    Luxury
                  </span>
                </div>

                <div
                  class="flex items-center text-[#285DA6] font-karla uppercase tracking-wider text-xs border-t border-[#285DA6]/10 pt-4 mt-4"
                >
                  <span class="mr-1">View Details</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="group-hover:translate-x-1 transition-transform duration-300"
                  >
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        `;
      }
    });
  }

  // Call the function to render HotelCard components after the DOM is loaded
  document.addEventListener("DOMContentLoaded", () => {
    setTimeout(renderHotelCards, 100); // Small delay to ensure DOM is ready
  });
</script>
