---
import Layout from "../components/layout/Layout.astro";
import PartnershipHero from "../components/partnership/PartnershipHero.astro";
import PartnershipOverview from "../components/partnership/PartnershipOverview.astro";
import PartnershipTestimonials from "../components/partnership/PartnershipTestimonials.astro";
import PartnershipApplication from "../components/partnership/PartnershipApplication.astro";

// Page metadata
const title = "Partnership Opportunities - Perfect Piste - Luxury Ski Holidays";
const description = "Join <PERSON> Piste's exclusive Preferred Partnership Programme. Offer your clients exceptional luxury ski experiences while earning competitive rewards through our trusted network.";
---

<Layout
  title={title}
  description={description}
>
  <main class="min-h-screen bg-background">
    <!-- Hero Section -->
    <PartnershipHero
      title="Partnership Opportunities"
      subtitle="Join our exclusive network of partners and offer your clients the ultimate luxury ski experience"
      imageUrl="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
    />

    <div class="px-0 md:px-8">
    <!-- Partnership Overview -->
    <PartnershipOverview />
    
    <!-- Partner Testimonials -->
    <PartnershipTestimonials />
    
    <!-- Application Process -->
    <PartnershipApplication />
  </div>
  </main>
</Layout>
