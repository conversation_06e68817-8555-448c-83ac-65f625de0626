---
import Layout from "../components/layout/Layout.astro";
import VideoHeroSection from "../components/home/<USER>";
import CategorySection from "../components/home/<USER>";
import WebStoriesSection from "../components/home/<USER>";
import FeaturedStaysSection from "../components/home/<USER>";

import CTASection from "../components/home/<USER>";
import WhyPerfectPisteSection from "../components/home/<USER>";
import PerfectPisteAmenitiesSection from "../components/home/<USER>";

// Define video sources for different destinations
const videoSources = [
  {
    src: "/videos/hero/skiing-1.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-2.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-3.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-4.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-5.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-6.mp4",
    type: "video/mp4",
  },
];

// Define ski destinations to showcase
const skiDestinations = [
  "Zermatt",
  "St. Moritz",
  "Verbier",
  "Courchevel",
  "Aspen",
];
---

<Layout
  title="Perfect Piste - Luxury Ski Holidays"
  description="Experience the ultimate luxury ski holidays with Perfect Piste. Exclusive destinations, personalized service, and unforgettable experiences."
>
  <main>
    <VideoHeroSection
      client:load
      videoSources={videoSources}
      destinations={skiDestinations}
    />
    <div class="py-16 mt-16">
      <WhyPerfectPisteSection />
    </div>
    <div class="py-16">
      <CategorySection />
    </div>
    <div class="py-16" id="how-to-use-ai">
      <PerfectPisteAmenitiesSection />
    </div>
    <div class="py-16">
      <FeaturedStaysSection />
    </div>
    <!-- <div class="py-16">
      <WebStoriesSection />
    </div> -->
  </main>

  <div class="pt-16 pb-32">
    <CTASection />
  </div>
</Layout>
