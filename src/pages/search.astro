---
import Layout from "../components/layout/Layout.astro";
import FeaturedHotelsWithModal from "../components/home/<USER>";
import ShareModalController from "../components/share/ShareModalController";
import { fetchFeaturedDestinations } from "../utils/store/destinations";
import DestinationCard from "../components/destinations/DestinationCard.astro";

// Define page metadata
const title = "Search - Perfect Piste - Luxury Ski Holidays";
const description =
  "Find your perfect ski holiday accommodation. Browse our curated selection of luxury ski hotels and chalets.";

// Define sharing metadata
const ogImage = "/images/search-thumbnail.jpg";
const canonicalUrl = new URL(
  Astro.url.pathname,
  Astro.site || "https://perfectpiste.com"
).toString();
const ogType = "website";

// Get search parameters from URL
const checkIn = Astro.url.searchParams.get("check_in") || undefined;
const checkOut = Astro.url.searchParams.get("check_out") || undefined;
const adults = parseInt(Astro.url.searchParams.get("adults") || "2");
const children = parseInt(Astro.url.searchParams.get("children") || "0");
const infants = parseInt(Astro.url.searchParams.get("infants") || "0");
const currencyCode = Astro.url.searchParams.get("currency_code") || "USD";
const destinationId = Astro.url.searchParams.get("destination_id") || undefined;
const isPetsAllowed = Astro.url.searchParams.get("is_pets_allowed") === "true";

// Fetch featured destinations
const featuredDestinations = await fetchFeaturedDestinations(6);
console.log("featuredDestinations", featuredDestinations);
---

<Layout
  title={title}
  description={description}
  ogImage={ogImage}
  canonicalUrl={canonicalUrl}
  ogType={ogType}
>
  <div class="pt-4">
    <!-- Featured Destinations Section -->
    {
      !checkIn && !checkOut && (
        <section class="py-12 bg-accent/10">
          <div class="container-custom">
            <div class="mb-8">
              <h2 class="text-3xl font-baskervville">Featured Destinations</h2>
              <p class="text-muted-foreground mt-2">
                Explore our handpicked luxury ski destinations
              </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredDestinations.map((destination) => (
                <DestinationCard
                  id={destination.id}
                  name={destination.name}
                  propertyCount={destination.property_count || 0}
                  imageUrl={destination.images?.[0]?.url || ""}
                  displayCategory={destination.category || "Luxury Destination"}
                  searchPrompt={`Luxury ski accommodations in ${destination.name}`}
                />
              ))}
            </div>
          </div>
        </section>
      )
    }

    <!-- Hotels Section -->
    <div class="py-12">
      <div class="container-custom">
        <div class="mb-8">
          <h2 class="text-3xl font-baskervville">Available Hotels</h2>
          {
            checkIn && checkOut && (
              <p class="text-primary font-medium mt-1">
                {`${new Date(checkIn).toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" })} - ${new Date(checkOut).toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" })}`}
                {` · ${adults} Adult${adults !== 1 ? "s" : ""}`}
                {children > 0 &&
                  `, ${children} Child${children !== 1 ? "ren" : ""}`}
                {infants > 0 &&
                  `, ${infants} Infant${infants !== 1 ? "s" : ""}`}
                {destinationId && " · Filtered by destination"}
              </p>
            )
          }
          <p class="text-muted-foreground mt-2">
            {
              checkIn && checkOut
                ? ""
                : "Browse our selection of luxury ski hotels and chalets"
            }
          </p>
        </div>
        <FeaturedHotelsWithModal
          client:load
          checkIn={checkIn}
          checkOut={checkOut}
          adults={adults}
          children={children}
          infants={infants}
          currencyCode={currencyCode}
          destinationId={destinationId}
          isPetsAllowed={isPetsAllowed}
        />
      </div>
    </div>
  </div>

  <!-- Share Modal Component -->
  <ShareModalController client:load />
</Layout>
