---
import Layout from "../../components/layout/Layout.astro";
import { getHotelAndRoomTypes } from "../../utils/dataService";
import BookingForm from "../../components/booking/BookingForm";

// Define the getStaticPaths function to generate all possible hotel pages
export async function getStaticPaths() {
  try {
    // Import the getAllHotels function
    const { getAllHotels } = await import("../../utils/dataService");

    // Get all hotels
    const hotels = await getAllHotels();

    // Generate paths for each hotel
    const paths = hotels.map((hotel) => ({
      params: { id: hotel.id },
      props: { hotelId: hotel.id },
    }));

    // Add grand-hotel for testing
    paths.push({
      params: { id: "grand-hotel" },
      props: { hotelId: "grand-hotel" },
    });

    return paths;
  } catch (error) {
    console.error("Error generating static paths for booking pages:", error);
    return [];
  }
}

// Get the hotel ID from params
const { id } = Astro.params;
const { hotelId } = Astro.props;

// Get query parameters
const url = new URL(Astro.request.url);
const checkInDate = url.searchParams.get("checkIn") || "";
const checkOutDate = url.searchParams.get("checkOut") || "";
const roomId = url.searchParams.get("roomId") || "";
const totalAmount = url.searchParams.get("totalAmount") || "0";
const currencyCode = url.searchParams.get("currency") || "USD";
const guestCount = url.searchParams.get("guests") || "1";
const checkInTime = url.searchParams.get("checkInTime") || "14:00";
const checkOutTime = url.searchParams.get("checkOutTime") || "11:00";
const mealPlan = url.searchParams.get("mealPlan") || "none";

// Validate dates
const isValidDate = (dateStr: string) => {
  if (!dateStr) return false;
  const date = new Date(dateStr);
  return !isNaN(date.getTime());
};

// If dates are invalid, set them to empty strings
const validCheckInDate = isValidDate(checkInDate) ? checkInDate : "";
const validCheckOutDate = isValidDate(checkOutDate) ? checkOutDate : "";

// Fetch the hotel data
let hotel,
  roomTypes = [];

try {
  const result = await getHotelAndRoomTypes(id);
  hotel = result.hotel;
  roomTypes = result.roomTypes || [];
} catch (error) {
  console.error(`Error fetching hotel data for ID ${id}:`, error);

  // For development/testing purposes, create mock data for grand-hotel
  if (id === "grand-hotel") {
    hotel = {
      id: "grand-hotel",
      name: "Grand Hotel",
      description: "A luxurious hotel in the heart of St. Moritz.",
      location: "St. Moritz, Switzerland",
      price: 340,
      currency: "USD",
      currencyCode: "USD",
      rating: 4.9,
      reviewCount: 32,
      imageUrl: "/images/room-placeholder.jpg",
      images: ["/images/room-placeholder.jpg"],
      amenities: ["Spa", "Pool", "Restaurant", "Bar", "Gym"],
      uuid: "grand-hotel",
    };

    roomTypes = [
      {
        id: "deluxe-suite",
        name: "Deluxe Suite",
        description: "Spacious suite with mountain views.",
        price: 340,
        maxGuests: 2,
        maxAdults: 2,
        maxChildren: 1,
        maxInfants: 1,
        images: ["/images/room-placeholder.jpg"],
        amenities: ["King Bed", "Balcony", "Mountain View"],
      },
    ];
  } else {
    return Astro.redirect("/404"); // Redirect to 404 page if hotel not found
  }
}

// Find the selected room
const selectedRoom = roomTypes.find((room) => room.id.toString() === roomId);

// Get default image URL
const getDefaultImageUrl = () => {
  return (
    hotel?.imageUrl || hotel?.images?.[0] || "/images/room-placeholder.jpg"
  );
};

const title = `Complete Your Booking - ${hotel.name}`;
const description = `Complete your reservation at ${hotel.name}. Just a few steps away from your perfect stay.`;
---

<Layout title={title} description={description}>
  <div class="container-custom py-12">
    <div class="max-w-4xl mx-auto">
      <div class="mb-8">
        <h1
          class="text-xl font-karla uppercase tracking-wider mb-2 text-[#285DA6]"
        >
          Complete Your Booking
        </h1>
        <p class="text-foreground/70">
          Please provide your details to confirm your reservation.
        </p>
      </div>

      <BookingForm
        client:load
        hotelId={hotelId.toString()}
        roomConfigId={roomId}
        checkInDate={validCheckInDate}
        checkOutDate={validCheckOutDate}
        checkInTime={checkInTime}
        checkOutTime={checkOutTime}
        totalAmount={parseFloat(totalAmount)}
        currencyCode={currencyCode}
        guestCount={parseInt(guestCount)}
        hotelName={hotel.name || "Grand Hotel"}
        hotelLocation={hotel.location || "St. Moritz, Switzerland"}
        roomName={selectedRoom?.name || "Superior Room"}
        roomImage={selectedRoom?.images?.[0] || getDefaultImageUrl()}
        mealPlan={mealPlan}
      />
    </div>
  </div>
</Layout>
