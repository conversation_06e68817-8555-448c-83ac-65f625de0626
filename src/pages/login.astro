---
import Layout from "../components/layout/Layout.astro";
import LoginFormWrapper from "../components/auth/LoginFormWrapper";
---

<script>
  // Check if user is already logged in
  document.addEventListener("DOMContentLoaded", () => {
    // Show loading spinner
    const contentEl = document.getElementById("login-content");
    const loadingEl = document.getElementById("auth-loading");

    if (contentEl) contentEl.style.display = "none";
    if (loadingEl) loadingEl.style.display = "flex";

    // Check authentication status
    const token = localStorage.getItem("auth_token");
    if (token) {
      // User is logged in, redirect to home page
      window.location.href = "/";
    } else {
      // User is not logged in, show content
      if (contentEl) contentEl.style.display = "block";
      if (loadingEl) loadingEl.style.display = "none";
    }
  });
</script>

<Layout title="Login - Perfect Piste">
  <!-- Loading spinner -->
  <div
    id="auth-loading"
    class="min-h-[calc(100vh-80px)] flex items-center justify-center"
    style="display: none;"
  >
    <div class="text-center">
      <div
        class="inline-block w-12 h-12 border-4 border-[#285DA6]/30 border-t-[#285DA6] rounded-full animate-spin mb-4"
      >
      </div>
    </div>
  </div>

  <!-- Main content -->
  <div
    id="login-content"
    class="min-h-[calc(100vh-80px)] flex items-center justify-center bg-gradient-to-b from-gray-50 to-white"
  >
    <div class="container-custom py-20 w-full">
      <div class="max-w-5xl mx-auto">
        <div
          class="flex flex-col md:flex-row md:items-stretch md:gap-0 bg-white rounded-xl shadow-xl overflow-hidden"
        >
          <!-- Left side: Form -->
          <div class="w-full md:w-1/2 p-8 md:p-12 flex flex-col justify-center">
            <div class="mb-10">
              <h1
                class="text-3xl md:text-4xl font-baskervville mb-4 text-gray-900"
              >
                Welcome Back
              </h1>
              <p class="text-gray-600 text-base leading-relaxed">
                Sign in to your Perfect Piste account to access your bookings,
                wishlist, and exclusive member benefits.
              </p>
            </div>

            <LoginFormWrapper client:visible />
          </div>

          <!-- Right side: Image and benefits -->
          <div class="w-full md:w-1/2 hidden md:block relative">
            <div class="absolute inset-0">
              <img
                src="https://dev-assets.store.flinkk.io/powderbyrne-prod/Belvedere Swiss Quality Hotel Grindelwald_Drohne Winter (15)-01JSM00XWM4MBG75ME0KP1JS43.jpg"
                alt="Luxury ski resort"
                class="w-full h-full object-cover"
              />
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/10"
              >
                <div class="absolute bottom-0 left-0 right-0 p-12 text-white">
                  <div class="mb-6">
                    <span
                      class="inline-block px-3 py-1 bg-[#285DA6]/90 text-white text-xs uppercase tracking-wider rounded-sm mb-4 backdrop-blur-sm"
                      >Premium Experience</span
                    >
                    <h3 class="text-white text-2xl font-baskervville mb-3">
                      Discover Luxury Ski Destinations
                    </h3>
                    <p class="text-white/90 text-sm mb-6 leading-relaxed">
                      Access exclusive accommodations and personalized concierge
                      services tailored to your preferences.
                    </p>
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <span
                      class="bg-white/10 text-white text-xs px-4 py-1.5 rounded-full backdrop-blur-sm border border-white/20"
                      >Luxury Stays</span
                    >
                    <span
                      class="bg-white/10 text-white text-xs px-4 py-1.5 rounded-full backdrop-blur-sm border border-white/20"
                      >Concierge Service</span
                    >
                    <span
                      class="bg-white/10 text-white text-xs px-4 py-1.5 rounded-full backdrop-blur-sm border border-white/20"
                      >Member Benefits</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>
