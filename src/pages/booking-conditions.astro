---
import Layout from "../components/layout/Layout.astro";
import BookingConditionsHero from "../components/booking/BookingConditionsHero.astro";
import BookingConditionsContent from "../components/booking/BookingConditionsContent.astro";

// Page metadata
const title = "Booking Conditions - Perfect Piste - Luxury Ski Holidays";
const description = "Read our comprehensive booking conditions and terms for Perfect Piste luxury ski holidays. Clear policies on payments, cancellations, insurance requirements, and our commitments to you.";
---

<Layout
  title={title}
  description={description}
>
  <main class="min-h-screen bg-background">
    <!-- Hero Section -->
    <BookingConditionsHero
      title="Booking Conditions"
      subtitle="Our comprehensive terms and conditions designed to provide clarity and protection for your luxury ski holiday booking"
      imageUrl="https://images.unsplash.com/photo-1450101499163-c8848c66ca85?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
    />
    
    <!-- Booking Conditions Content -->
    <BookingConditionsContent />
  </main>
</Layout>
