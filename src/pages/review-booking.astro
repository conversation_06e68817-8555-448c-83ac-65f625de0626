---
import Layout from "../components/layout/Layout.astro";
import ReviewBookingWrapper from "../components/booking/ReviewBookingWrapper";

// The booking data will be passed from client-side JavaScript
// Default values are provided as fallbacks
const title = "Review Your Booking - Perfect Piste";
const description = "Review your booking details before proceeding to payment.";
---

<Layout title={title} description={description}>
  <div class="bg-white min-h-[calc(100vh-80px)]">
    <div class="container-custom py-8">
      <div class="mb-4 max-w-7xl mx-auto">
        <h1
          class="text-xl font-karla uppercase tracking-wider mb-2 text-[#3566ab]"
        >
          Review your Booking
        </h1>
      </div>

      <ReviewBookingWrapper client:load />
    </div>
  </div>
</Layout>

<script>
  // Handle back button
  document.addEventListener("DOMContentLoaded", () => {
    const backButton = document.getElementById("back-button");
    if (backButton) {
      backButton.addEventListener("click", () => {
        window.history.back();
      });
    }
  });
</script>
