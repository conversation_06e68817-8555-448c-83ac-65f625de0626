---
import Layout from "../components/layout/Layout.astro";
import PrivacyPolicyHero from "../components/legal/PrivacyPolicyHero.astro";
import PrivacyPolicyContent from "../components/legal/PrivacyPolicyContent.astro";

// Page metadata
const title = "Privacy Policy - Perfect Piste - Luxury Ski Holidays";
const description = "Learn about how <PERSON> Piste collects, uses, and protects your personal information in accordance with GDPR and data protection regulations.";
---

<Layout
  title={title}
  description={description}
>
  <main class="min-h-screen bg-background">
    <!-- Hero Section -->
    <PrivacyPolicyHero
      title="Privacy Policy"
      subtitle="Learn about how <PERSON> Piste collects, uses, and protects your personal information in accordance with GDPR and data protection regulations"
      imageUrl="https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
    />

    <!-- Privacy Policy Content -->
    <PrivacyPolicyContent />
  </main>
</Layout>
