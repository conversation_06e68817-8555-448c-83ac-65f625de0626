---
import Layout from "../../components/layout/Layout.astro";
import { getHotelAndRoomTypes } from "../../utils/dataService";
import SimplePhotoModal from "../../components/photos/SimplePhotoModal.astro";

// Define the getStaticPaths function to generate all possible hotel pages
export async function getStaticPaths() {
  try {
    // Import the getAllHotels function
    const { getAllHotels } = await import("../../utils/dataService");

    // Get all hotels
    const hotels = await getAllHotels();

    // Generate paths for each hotel
    return hotels.map((hotel) => ({
      params: { id: hotel.id },
    }));
  } catch (error) {
    console.error("Error generating static paths for photo gallery:", error);
    return [];
  }
}

// Get the hotel ID from params
const { id } = Astro.params;

// Fetch the hotel data
let hotel,
  roomTypes = [];

try {
  const result = await getHotelAndRoomTypes(id as any);
  hotel = result.hotel;
  roomTypes = result.roomTypes || [];
} catch (error) {
  console.error(`Error fetching hotel data for ID ${id}:`, error);
  return Astro.redirect("/404"); // Redirect to 404 page if hotel not found
}

// Use hotel data from API
const hotelData = {
  ...hotel,
};

// Prepare all images for the gallery
const hotelImages = hotelData.images || [];
const roomImages = roomTypes.flatMap((room) => room.images || []);

// Categorize images (in a real app, these would be properly tagged in the database)
const exteriorImages = hotelImages.slice(
  0,
  Math.ceil(hotelImages.length * 0.3)
);
const amenityImages = hotelImages.slice(
  Math.ceil(hotelImages.length * 0.3),
  Math.ceil(hotelImages.length * 0.6)
);
const diningImages = hotelImages.slice(Math.ceil(hotelImages.length * 0.6));
const locationImages = hotelImages.slice(0, 2); // Just use a couple of hotel images for location

// Create a map of all categorized images
const categorizedImages = {
  all: [...hotelImages, ...roomImages],
  rooms: roomImages,
  exterior: exteriorImages,
  amenities: amenityImages,
  dining: diningImages,
  location: locationImages,
};

// Ensure we have at least 15 images by duplicating if needed
const allImages = [...hotelImages, ...roomImages];
const galleryImages = [...allImages];
while (galleryImages.length < 15) {
  galleryImages.push(
    ...allImages.slice(0, Math.min(15 - galleryImages.length, allImages.length))
  );
}

// Define page metadata
const title = `${hotelData.name} - Photo Gallery`;
const description = `Browse all photos of ${hotelData.name}`;
---

<Layout title={title} description={description}>
  <div
    class="fixed top-0 left-0 w-full bg-white z-50 border-b border-[#285DA6]/10"
  >
    <div class="container-custom py-4 flex items-center justify-between">
      <a
        href={`/stays/${id}`}
        class="flex items-center text-[#285DA6] hover:opacity-80 transition-opacity"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="mr-2"
        >
          <path d="M19 12H5"></path>
          <path d="M12 19l-7-7 7-7"></path>
        </svg>
        <span class="font-medium">Back to {hotelData.name}</span>
      </a>

      <div class="flex items-center gap-4">
        <button
          class="flex items-center bg-white border border-[#285DA6]/20 rounded-full px-4 py-2 text-sm font-karla hover:bg-[#285DA6]/5 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="mr-2"
          >
            <path d="M7 11v13l7-7 7 7V11"></path>
            <rect x="3" y="3" width="18" height="8" rx="1" ry="1"></rect>
          </svg>
          Share
        </button>

        <button
          class="flex items-center bg-white border border-[#285DA6]/20 rounded-full px-4 py-2 text-sm font-karla hover:bg-[#285DA6]/5 transition-colors group"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="mr-2 group-hover:fill-red-500 group-hover:text-red-500 transition-colors"
          >
            <path
              d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
            ></path>
          </svg>
          Save
        </button>
      </div>
    </div>
  </div>

  <div class="pt-20 pb-16">
    <div class="container-custom">
      <h1 class="text-3xl font-baskervville mb-2">{hotelData.name}</h1>
      <p class="text-foreground/70 mb-8">{hotelData.location}</p>

      <!-- Photo Categories -->
      <div
        class="flex items-center gap-6 mb-8 overflow-x-auto pb-2 scrollbar-hide"
        id="photo-categories"
      >
        <button
          data-category="all"
          class="category-tab flex flex-col items-center gap-2 min-w-[80px] text-[#285DA6] border-b-2 border-[#285DA6] pb-2 active"
        >
          <span class="text-sm font-medium">All photos</span>
        </button>
        <button
          data-category="rooms"
          class="category-tab flex flex-col items-center gap-2 min-w-[80px] text-foreground/70 hover:text-[#285DA6] transition-colors"
        >
          <span class="text-sm font-medium">Rooms</span>
        </button>
        <button
          data-category="exterior"
          class="category-tab flex flex-col items-center gap-2 min-w-[80px] text-foreground/70 hover:text-[#285DA6] transition-colors"
        >
          <span class="text-sm font-medium">Exterior</span>
        </button>
        <button
          data-category="amenities"
          class="category-tab flex flex-col items-center gap-2 min-w-[80px] text-foreground/70 hover:text-[#285DA6] transition-colors"
        >
          <span class="text-sm font-medium">Amenities</span>
        </button>
        <button
          data-category="dining"
          class="category-tab flex flex-col items-center gap-2 min-w-[80px] text-foreground/70 hover:text-[#285DA6] transition-colors"
        >
          <span class="text-sm font-medium">Dining</span>
        </button>
        <button
          data-category="location"
          class="category-tab flex flex-col items-center gap-2 min-w-[80px] text-foreground/70 hover:text-[#285DA6] transition-colors"
        >
          <span class="text-sm font-medium">Location</span>
        </button>
      </div>

      <!-- Photo Grid -->
      <div
        id="photo-grid"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
      >
        {
          Object.entries(categorizedImages).map(
            ([category, images], categoryIndex) =>
              images.map((image, imageIndex) => {
                const isFirstInCategory = imageIndex === 0;
                const dataIndex =
                  categoryIndex === 0
                    ? imageIndex
                    : categorizedImages.all.indexOf(image);

                return (
                  <div
                    data-category={category}
                    class={`photo-item overflow-hidden rounded-lg ${category !== "all" ? "hidden" : ""} ${isFirstInCategory && category === "all" ? "md:col-span-2 md:row-span-2" : ""}`}
                  >
                    <div class="group relative cursor-pointer h-full">
                      <img
                        src={image}
                        alt={`${hotelData.name} - ${category} photo ${imageIndex + 1}`}
                        class="w-full h-full object-cover hover:scale-105 transition-transform duration-700 ease-in-out"
                        data-index={dataIndex}
                        loading={imageIndex > 5 ? "lazy" : "eager"}
                        width="800"
                        height="600"
                      />
                      <div class="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <button class="bg-white/90 backdrop-blur-sm rounded-full p-3 transform scale-90 group-hover:scale-100 transition-transform duration-300">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          >
                            <circle cx="11" cy="11" r="8" />
                            <line x1="21" y1="21" x2="16.65" y2="16.65" />
                            <line x1="11" y1="8" x2="11" y2="14" />
                            <line x1="8" y1="11" x2="14" y2="11" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })
          )
        }
      </div>
    </div>
  </div>

  <!-- Photo Modal Component -->
  <SimplePhotoModal images={categorizedImages.all} hotelName={hotelData.name} />
</Layout>

<style>
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
</style>

<script is:inline>
  // Client-side functionality for photo gallery
  document.addEventListener("DOMContentLoaded", () => {
    // Tab functionality
    const categoryTabs = document.querySelectorAll(".category-tab");
    const photoItems = document.querySelectorAll(".photo-item");
    const photoGrid = document.getElementById("photo-grid");

    // Track if filtering is in progress to prevent multiple rapid clicks
    let isFiltering = false;

    // Cache category data for faster filtering
    const itemCategories = {};
    photoItems.forEach((item) => {
      const category = item.dataset.category;
      itemCategories[category] = itemCategories[category] || [];
      itemCategories[category].push(item);
    });

    // Preload images for visible categories
    const preloadCategoryImages = (category) => {
      // Only preload if we're showing a specific category (not "all")
      if (category !== "all") {
        const categoryItems = itemCategories[category] || [];
        categoryItems.forEach((item) => {
          const img = item.querySelector("img");
          if (img && img.getAttribute("loading") === "lazy") {
            // Force eager loading for this category's images
            img.setAttribute("loading", "eager");
          }
        });
      }
    };

    // Function to filter photos by category with performance optimizations
    const filterPhotosByCategory = (category) => {
      if (isFiltering) return;
      isFiltering = true;

      // Use requestAnimationFrame for smoother UI updates
      requestAnimationFrame(() => {
        // Optimize by using classList.toggle with forced state
        photoItems.forEach((item) => {
          const itemCategory = item.getAttribute("data-category");
          const shouldShow = category === "all" || itemCategory === category;
          item.classList.toggle("hidden", !shouldShow);
        });

        // Update active tab styling
        categoryTabs.forEach((tab) => {
          const tabCategory = tab.getAttribute("data-category");
          const isActive = tabCategory === category;

          // Use classList.toggle with forced state for better performance
          tab.classList.toggle("text-[#285DA6]", isActive);
          tab.classList.toggle("border-b-2", isActive);
          tab.classList.toggle("border-[#285DA6]", isActive);
          tab.classList.toggle("text-foreground/70", !isActive);
        });

        // Preload images for the selected category
        preloadCategoryImages(category);

        // Reset filtering flag after a short delay
        setTimeout(() => {
          isFiltering = false;
        }, 100);
      });
    };

    // Add click event to category tabs with debounce
    categoryTabs.forEach((tab) => {
      tab.addEventListener("click", () => {
        if (isFiltering) return;
        const category = tab.getAttribute("data-category");
        filterPhotosByCategory(category);
      });
    });

    // Photo click functionality with optimizations
    const photoClickHandler = (event) => {
      // Use event delegation for better performance
      const photo = event.target.closest(".group.relative.cursor-pointer");
      if (!photo) return;

      // Get the index from the img element
      const img = photo.querySelector("img");
      const index = img ? parseInt(img.getAttribute("data-index") || "0") : 0;

      // Open the modal with the clicked image
      if (typeof window.openPhotoModal === "function") {
        window.openPhotoModal(index);
      }
    };

    // Use event delegation instead of attaching listeners to each photo
    photoGrid.addEventListener("click", photoClickHandler);

    // Preload first few images of each category for faster tab switching
    Object.keys(itemCategories).forEach((category) => {
      const firstFewItems = (itemCategories[category] || []).slice(0, 3);
      firstFewItems.forEach((item) => {
        const img = item.querySelector("img");
        if (img) {
          const imgSrc = img.getAttribute("src");
          if (imgSrc) {
            const preloadImg = new Image();
            preloadImg.src = imgSrc;
          }
        }
      });
    });
  });
</script>
