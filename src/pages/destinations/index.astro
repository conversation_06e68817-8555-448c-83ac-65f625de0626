---
import Layout from "../../components/layout/Layout.astro";
import { getAllDestinations } from "../../utils/dataService";
import DestinationHero from "../../components/destinations/DestinationHero.astro";
import CategorySection from "../../components/destinations/CategorySection.astro";
import DestinationFeatures from "../../components/destinations/DestinationFeatures.astro";
import AllDestinations from "../../components/destinations/AllDestinations.astro";
import CTASection from "../../components/home/<USER>";

// Fetch destinations from API
const destinations = await getAllDestinations();

// Define page metadata
const title = "Exclusive Destinations - Perfect Piste";
const description =
  "Discover the world's most extraordinary locations, where luxury and authentic experiences create unforgettable moments.";
---

<Layout title={title} description={description}>
  <!-- Hero Section -->
  <DestinationHero
    title="Exclusive Destinations"
    description={description}
    backgroundImage="https://images.unsplash.com/photo-1491555103944-7c647fd857e6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
    ctaText="Explore Destinations"
    ctaLink="#all-destinations"
  />

  <!-- Category Section -->
  <CategorySection />

  <!-- Destination Icons Section -->
  <DestinationFeatures />

  <!-- Featured Destinations Section Removed -->

  <!-- Grid of All Destinations -->
  <div id="all-destinations">
    <AllDestinations
      destinations={destinations}
      title="Explore All Destinations"
    />
  </div>

  <!-- CTA Section -->
  <CTASection
    title="Begin Your Alpine Adventure"
    description="Let our team of ski experts create a bespoke mountain experience tailored to your preferences and skill level."
    ctaText="PLAN YOUR TRIP"
    ctaLink="/ai-search"
    backgroundImage="https://images.unsplash.com/photo-1486870591958-9b9d0d1dda99?ixlib=rb-4.0.3&auto=format&fit=crop&w=2576&q=80"
  />
</Layout>
