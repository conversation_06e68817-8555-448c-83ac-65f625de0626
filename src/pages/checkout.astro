---
import Layout from "../components/layout/Layout.astro";
import CheckoutForm from "../components/checkout/CheckoutForm";
import { getHotelAndRoomTypes } from "../utils/dataService";

// Get query parameters
const url = new URL(Astro.request.url);
const hotelId = url.searchParams.get("hotelId") || "";
const roomId = url.searchParams.get("roomId") || "";
const checkInDate = url.searchParams.get("checkIn") || "";
const checkOutDate = url.searchParams.get("checkOut") || "";
const checkInTime = url.searchParams.get("checkInTime") || "";
const checkOutTime = url.searchParams.get("checkOutTime") || "";
const totalAmount = url.searchParams.get("totalAmount") || "0";
const currencyCode = url.searchParams.get("currency") || "USD";
const guestCount = url.searchParams.get("guests") || "1";
const infantCount = url.searchParams.get("infants") || "0";
const mealPlan = url.searchParams.get("mealPlan") || "none";
const roomQuantity = url.searchParams.get("quantity") || "1";
const regionId =
  url.searchParams.get("regionId") || "reg_01JP9R0NP6B5DXGDYHFSSW0FK1";

// Validate dates
const isValidDate = (dateStr: string) => {
  if (!dateStr) return false;
  const date = new Date(dateStr);
  return !isNaN(date.getTime());
};

// If dates are invalid, set them to empty strings
const validCheckInDate = isValidDate(checkInDate) ? checkInDate : "";
const validCheckOutDate = isValidDate(checkOutDate) ? checkOutDate : "";

// Fetch the hotel data
let hotel: any = { name: "", location: "" };
let selectedRoom: any = { name: "", images: [] };

try {
  if (hotelId) {
    const result = await getHotelAndRoomTypes(hotelId);
    hotel = result.hotel || {};
    const roomTypes = result.roomTypes || [];

    // Find the selected room
    selectedRoom =
      roomTypes.find((room: any) => room.id.toString() === roomId) || {};
  }
} catch (error) {
  console.error(`Error fetching hotel data for ID ${hotelId}:`, error);
}

// Get default image URL
const getDefaultImageUrl = () => {
  return (
    selectedRoom?.imageUrl ||
    selectedRoom?.images?.[0] ||
    hotel?.imageUrl ||
    hotel?.images?.[0] ||
    "/images/room-placeholder.jpg"
  );
};

const title = `Complete Your Booking - ${hotel.name || "Perfect Piste"}`;
const description = `Complete your reservation at ${hotel.name || "Perfect Piste"}. Just a few steps away from your perfect stay.`;
---

<Layout title={title} description={description}>
  <div class="container-custom py-8">
    <div class="mb-4 max-w-7xl mx-auto" id="booking-header">
      <h1
        class="text-xl font-karla uppercase tracking-wider mb-2 text-[#285DA6]"
      >
        Complete Your Booking
      </h1>
    </div>

    <CheckoutForm
      client:load
      hotelId={hotelId}
      roomConfigId={roomId}
      checkInDate={validCheckInDate}
      checkOutDate={validCheckOutDate}
      checkInTime={checkInTime}
      checkOutTime={checkOutTime}
      totalAmount={parseFloat(totalAmount)}
      currencyCode={currencyCode}
      guestCount={parseInt(guestCount)}
      infantCount={parseInt(infantCount)}
      mealPlan={mealPlan}
      roomQuantity={parseInt(roomQuantity)}
      hotelName={hotel.name || ""}
      hotelLocation={hotel.location || ""}
      roomName={selectedRoom?.name || ""}
      roomImage={getDefaultImageUrl()}
      regionId={regionId}
    />
  </div>
</Layout>
