---
import Layout from "../components/layout/Layout.astro";
import RegisterFormWrapper from "../components/auth/RegisterFormWrapper";
---

<script>
  // Check if user is already logged in
  document.addEventListener("DOMContentLoaded", () => {
    // Show loading spinner
    const contentEl = document.getElementById("register-content");
    const loadingEl = document.getElementById("auth-loading");

    if (contentEl) contentEl.style.display = "none";
    if (loadingEl) loadingEl.style.display = "flex";

    // Check authentication status
    const token = localStorage.getItem("auth_token");
    if (token) {
      // User is logged in, redirect to home page
      window.location.href = "/";
    } else {
      // User is not logged in, show content
      if (contentEl) contentEl.style.display = "block";
      if (loadingEl) loadingEl.style.display = "none";
    }
  });
</script>

<Layout title="Register - Perfect Piste">
  <!-- Loading spinner -->
  <div
    id="auth-loading"
    class="min-h-[calc(100vh-80px)] flex items-center justify-center"
    style="display: none;"
  >
    <div class="text-center">
      <div
        class="inline-block w-12 h-12 border-4 border-[#285DA6]/30 border-t-[#285DA6] rounded-full animate-spin mb-4"
      >
      </div>
    </div>
  </div>

  <!-- Main content -->
  <div
    id="register-content"
    class="min-h-[calc(100vh-80px)] flex items-center justify-center bg-gradient-to-b from-gray-50 to-white"
  >
    <div class="container-custom py-20 w-full">
      <div class="max-w-6xl mx-auto">
        <div
          class="flex flex-col md:flex-row md:items-stretch md:gap-0 bg-white rounded-xl shadow-xl overflow-hidden"
        >
          <!-- Left side: Benefits -->
          <div class="w-full md:w-2/5 hidden md:block relative">
            <div class="absolute inset-0 bg-[#285DA6]/5 backdrop-blur-sm">
              <div
                class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#285DA6] to-blue-400"
              >
              </div>

              <div class="p-10 h-full flex flex-col">
                <div class="mb-8">
                  <h2 class="text-2xl font-baskervville mb-2 text-gray-900">
                    Join Perfect Piste
                  </h2>
                  <div class="w-16 h-1 bg-[#285DA6]"></div>
                </div>

                <div class="space-y-8 flex-grow">
                  <div class="flex items-start">
                    <div
                      class="flex-shrink-0 h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center text-[#285DA6] shadow-sm"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-6 w-6"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </div>
                    <div class="ml-5">
                      <h3 class="text-lg font-karla font-medium text-gray-900">
                        Exclusive Access
                      </h3>
                      <p class="mt-2 text-sm text-gray-600 leading-relaxed">
                        Unlock premium ski destinations and luxury
                        accommodations not available to the general public.
                      </p>
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div
                      class="flex-shrink-0 h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center text-[#285DA6] shadow-sm"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-6 w-6"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"
                        ></path>
                      </svg>
                    </div>
                    <div class="ml-5">
                      <h3 class="text-lg font-karla font-medium text-gray-900">
                        Personalized Service
                      </h3>
                      <p class="mt-2 text-sm text-gray-600 leading-relaxed">
                        Enjoy tailored recommendations and concierge services
                        for a truly bespoke ski experience.
                      </p>
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div
                      class="flex-shrink-0 h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center text-[#285DA6] shadow-sm"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-6 w-6"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </div>
                    <div class="ml-5">
                      <h3 class="text-lg font-karla font-medium text-gray-900">
                        Member Benefits
                      </h3>
                      <p class="mt-2 text-sm text-gray-600 leading-relaxed">
                        Enjoy special rates, priority booking, and exclusive
                        events for Perfect Piste members.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="mt-auto pt-8 border-t border-gray-200">
                  <div
                    class="bg-white/80 p-5 rounded-lg shadow-sm backdrop-blur-sm"
                  >
                    <p class="text-sm text-gray-600 italic leading-relaxed">
                      "Perfect Piste has transformed our ski holidays. The
                      personalized service and exclusive accommodations are
                      unmatched."
                    </p>
                    <div class="mt-4 flex items-center">
                      <div
                        class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                            clip-rule="evenodd"></path>
                        </svg>
                      </div>
                      <div class="ml-3">
                        <p class="text-xs font-medium text-gray-900">
                          Sarah & James
                        </p>
                        <p class="text-xs text-gray-500">Members since 2022</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right side: Form -->
          <div class="w-full md:w-3/5 p-8 md:p-12">
            <div class="mb-10">
              <h1
                class="text-3xl md:text-4xl font-baskervville mb-4 text-gray-900"
              >
                Create Your Account
              </h1>
              <p class="text-gray-600 text-base leading-relaxed">
                Join Perfect Piste to access exclusive luxury ski experiences,
                personalized recommendations, and member-only benefits.
              </p>
            </div>

            <RegisterFormWrapper client:load />
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>
