---
import Layout from "../components/layout/Layout.astro";

// Get booking ID from URL parameters
const { bookingId } = Astro.params;
const searchParams = Astro.url.searchParams;
const email = searchParams.get("email") || "";
const sessionId = searchParams.get("session_id") || "";
---

<Layout
  title="Booking Confirmation - Perfect Piste"
  description="Your booking has been confirmed. Thank you for choosing Perfect Piste."
>
  <div class="bg-white min-h-[calc(100vh-80px)]">
    <div class="container-custom py-16 mt-32">
      <div class="max-w-3xl mx-auto text-center">
        <!-- Success Icon (hidden initially, shown only after successful processing) -->
        <div
          id="success-icon"
          class="w-20 h-20 bg-green-100 rounded-full items-center justify-center mx-auto mb-8 hidden"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="40"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="text-green-600"
          >
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        </div>

        <!-- Confirmation Message -->
        <!-- Processing State -->
        <div id="processing-state">
          <h1 class="font-baskervville text-4xl mb-6">
            Processing Your Payment...
          </h1>
          <p class="text-lg mb-8">
            Please wait while we confirm your booking and process your payment.
          </p>
          <div
            class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#285DA6] mx-auto mb-8"
          >
          </div>
        </div>

        <!-- Success State (hidden initially) -->
        <div id="success-state" class="hidden">
          <h1 class="font-baskervville text-4xl mb-6">Booking Confirmed!</h1>
          <p class="text-lg mb-8">
            Thank you for your booking. We've sent a confirmation email with all
            the details.
          </p>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row justify-center gap-4">
            <a
              href="/account"
              class="px-6 py-3 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 font-karla uppercase tracking-wider transition-all duration-300"
            >
              View My Bookings
            </a>
            <a
              href="/"
              class="px-6 py-3 border border-[#285DA6] text-[#285DA6] rounded-lg hover:bg-[#285DA6]/5 font-karla uppercase tracking-wider transition-all duration-300"
            >
              Return to Home
            </a>
          </div>
        </div>

        <!-- Error State (hidden initially) -->
        <div id="error-state" class="hidden">
          <h1 class="font-baskervville text-4xl mb-6 text-red-600">
            Payment Processing Failed
          </h1>
          <p class="text-lg mb-8">
            There was an issue processing your payment. Please contact support
            or try again.
          </p>
          <div class="flex flex-col sm:flex-row justify-center gap-4">
            <a
              href="/review-booking"
              class="px-6 py-3 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 font-karla uppercase tracking-wider transition-all duration-300"
            >
              Try Again
            </a>
            <a
              href="/"
              class="px-6 py-3 border border-[#285DA6] text-[#285DA6] rounded-lg hover:bg-[#285DA6]/5 font-karla uppercase tracking-wider transition-all duration-300"
            >
              Return to Home
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Import cart completion functions
  import { completeCart, capturePayment } from "../utils/store/cart.js";

  // Handle booking confirmation and cleanup
  document.addEventListener("DOMContentLoaded", async () => {
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get("session_id");

    // Get elements
    const processingState = document.getElementById("processing-state");
    const successState = document.getElementById("success-state");
    const errorState = document.getElementById("error-state");
    const successIcon = document.getElementById("success-icon");

    if (sessionId) {
      try {
        console.log("Stripe Checkout session completed:", sessionId);

        // Get stored booking data to complete the cart
        const storedBookingData = localStorage.getItem("bookingData");
        if (!storedBookingData) {
          throw new Error("No booking data found");
        }

        const bookingData = JSON.parse(storedBookingData);

        // Get cart ID from the session (you might need to call an API to get this)
        // For now, we'll assume it's stored or we need to retrieve it
        const cartId = bookingData.cartId; // This should be stored when cart was created

        if (!cartId) {
          throw new Error("Cart ID not found");
        }

        // Complete the cart
        console.log("Completing cart:", cartId);
        const completeResponse = await completeCart(
          cartId,
          bookingData.roomId,
          bookingData.checkIn,
          bookingData.checkOut,
          sessionId
        );

        if (completeResponse.booking?.id || completeResponse.order?.id) {
          const orderId =
            completeResponse.booking?.id || completeResponse.order?.id;

          // Copy booking data to summary localStorage before clearing
          const storedBookingData = localStorage.getItem("bookingData");
          if (storedBookingData) {
            try {
              const bookingData = JSON.parse(storedBookingData);
              const summaryData = {
                ...bookingData,
                orderId: orderId,
                bookingReference: `PP-${Math.floor(Math.random() * 10000)
                  .toString()
                  .padStart(4, "0")}`,
                confirmedAt: new Date().toISOString(),
              };
              localStorage.setItem(
                "bookingSummaryData",
                JSON.stringify(summaryData)
              );
            } catch (error) {
              console.error("Error creating booking summary data:", error);
            }
          }

          // Clear original booking data from localStorage
          localStorage.removeItem("bookingData");

          // Redirect to booking summary page
          window.location.href = `/booking-summary/${orderId}`;
        } else {
          throw new Error("Failed to complete booking");
        }
      } catch (error) {
        console.error("Error processing booking confirmation:", error);

        // Show error state
        if (processingState) processingState.classList.add("hidden");
        if (errorState) errorState.classList.remove("hidden");
      }
    } else {
      // No session ID, show error
      if (processingState) processingState.classList.add("hidden");
      if (errorState) errorState.classList.remove("hidden");
    }
  });
</script>
