import { MEDUSA_BACKEND_URL, MEDUSA_API_KEY } from "../constants";

export interface RegisterData {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  phone?: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface OtpRegisterData {
  first_name: string;
  last_name: string;
  identifier: string; // email
  phone?: string;
}

export interface OtpRequestData {
  identifier: string; // email
}

export interface OtpVerifyData {
  identifier: string; // email
  otp: string;
}

export interface AuthResponse {
  token: string;
  customer?: any;
}

class AuthService {
  private apiUrl: string;
  private token: string | null = null;

  constructor() {
    this.apiUrl = MEDUSA_BACKEND_URL;
    // Check if token exists in localStorage (only in browser environment)
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      this.token = localStorage.getItem("auth_token");
    }
  }

  /**
   * Get the authentication token
   */
  getToken(): string | null {
    return this.token;
  }

  /**
   * Set the authentication token
   */
  setToken(token: string): void {
    this.token = token;
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      localStorage.setItem("auth_token", token);
    }
  }

  /**
   * Clear the authentication token
   */
  clearToken(): void {
    this.token = null;
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      localStorage.removeItem("auth_token");
    }
  }

  /**
   * Register a new customer
   */
  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      // Step 1: Get registration token
      const registrationResponse = await fetch(
        `${this.apiUrl}/auth/customer/emailpass/register`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-publishable-api-key": MEDUSA_API_KEY,
          },
          credentials: "include", // Important for cookie handling
          body: JSON.stringify({
            email: data.email,
            password: data.password,
          }),
        }
      );

      const responseData = await registrationResponse.json();

      if (!registrationResponse.ok) {
        console.error("Registration error response:", responseData);

        // Check if it's an existing identity error
        if (responseData.code === "auth_identity_exists") {
          // Try to login instead
          return this.login({
            email: data.email,
            password: data.password,
          });
        }

        throw new Error(responseData.message || "Registration failed");
      }

      const { token } = responseData;
      this.setToken(token);

      const customerResponse = await fetch(`${this.apiUrl}/store/customers`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
          "x-publishable-api-key": MEDUSA_API_KEY,
        },
        credentials: "include", // Important for cookie handling
        body: JSON.stringify({
          first_name: data.first_name,
          last_name: data.last_name,
          email: data.email,
          phone: data?.phone,
        }),
      });

      const customerData = await customerResponse.json();

      if (!customerResponse.ok) {
        console.error("Customer creation error:", customerData);
        throw new Error(customerData.message || "Customer creation failed");
      }

      // Step 3: Create a session with the token
      try {
        const sessionResponse = await fetch(
          `${this.apiUrl}/store/auth/session`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
              "x-publishable-api-key": MEDUSA_API_KEY,
            },
            credentials: "include", // Important for cookie handling
          }
        );

        if (!sessionResponse.ok) {
          console.warn(
            "Session creation warning:",
            await sessionResponse.text()
          );
          // Continue even if session creation fails, as we have the token
        }
      } catch (error) {
        // If there's a CORS error or any other network error, log it but continue
        console.warn("Session creation error (possibly CORS):", error);
      }

      return { token, customer: customerData.customer };
    } catch (error: any) {
      console.error("Registration error:", error);
      throw new Error(
        error.message || "Registration failed. Please try again."
      );
    }
  }

  /**
   * Login a customer
   */
  async login(data: LoginData): Promise<AuthResponse> {
    try {
      const response = await fetch(`${this.apiUrl}/auth/customer/emailpass`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-publishable-api-key": MEDUSA_API_KEY,
        },
        credentials: "include", // Important for cookie handling
        body: JSON.stringify({
          email: data.email,
          password: data.password,
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error("Login error response:", responseData);
        throw new Error(responseData.message || "Login failed");
      }

      const { token } = responseData;
      this.setToken(token);

      // Create a session with the token
      try {
        const sessionResponse = await fetch(
          `${this.apiUrl}/store/auth/session`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
              "x-publishable-api-key": MEDUSA_API_KEY,
            },
            credentials: "include", // Important for cookie handling
          }
        );

        if (!sessionResponse.ok) {
          console.warn(
            "Session creation warning:",
            await sessionResponse.text()
          );
          // Continue even if session creation fails, as we have the token
        }
      } catch (error) {
        // If there's a CORS error or any other network error, log it but continue
        console.warn("Session creation error (possibly CORS):", error);
      }
      const customerResponse = await fetch(
        `${this.apiUrl}/store/customers/me`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "x-publishable-api-key": MEDUSA_API_KEY,
          },
          credentials: "include", // Important for cookie handling
        }
      );

      const customerData = await customerResponse.json();

      if (!customerResponse.ok) {
        console.error("Customer fetch error:", customerData);
        throw new Error(customerData.message || "Failed to get customer data");
      }

      return { token, customer: customerData.customer };
    } catch (error: any) {
      console.error("Login error:", error);
      throw new Error(
        error.message ||
          "Login failed. Please check your credentials and try again."
      );
    }
  }

  /**
   * Logout the current customer
   */
  async logout(): Promise<void> {
    if (this.token) {
      try {
        // Call the logout endpoint to clear the session cookie
        const response = await fetch(`${this.apiUrl}/store/auth`, {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${this.token}`,
            "x-publishable-api-key": MEDUSA_API_KEY,
          },
          credentials: "include", // Important for cookie handling
        });

        if (!response.ok) {
          console.warn(
            "Logout warning: Server returned non-OK status",
            response.status
          );
        }
      } catch (error) {
        // If there's a CORS error or any other network error, log it but continue
        console.warn("Logout error (possibly CORS):", error);
      }
    }

    // Always clear the local token regardless of server response
    this.clearToken();
  }

  /**
   * Check if the user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.token;
  }

  /**
   * Get the current customer
   */
  async getCurrentCustomer(): Promise<any> {
    if (!this.token) {
      throw new Error("Not authenticated");
    }

    try {
      const response = await fetch(`${this.apiUrl}/store/customers/me`, {
        headers: {
          Authorization: `Bearer ${this.token}`,
          "x-publishable-api-key": MEDUSA_API_KEY,
        },
        credentials: "include", // Important for cookie handling
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to get customer data");
      }

      return response.json();
    } catch (error) {
      console.error("Get current customer error:", error);
      throw error;
    }
  }

  /**
   * Register a new customer with OTP
   */
  async registerWithOtp(data: OtpRegisterData): Promise<{ message: string }> {
    try {
      const response = await fetch(
        `${this.apiUrl}/auth/customer/otp/register`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-publishable-api-key": MEDUSA_API_KEY,
          },
          credentials: "include", // Important for cookie handling
          body: JSON.stringify({
            identifier: data.identifier,
            first_name: data.first_name,
            last_name: data.last_name,
            phone: data.phone,
          }),
        }
      );

      const responseData = await response.json();

      // Special case: If the response contains "otp_generated" message, it's actually a success
      if (
        responseData.message === "otp_generated" ||
        (responseData.type === "unauthorized" &&
          responseData.message === "otp_generated")
      ) {
        return { message: "OTP generated successfully" };
      }

      if (!response.ok) {
        console.error("OTP Registration error response:", responseData);
        throw new Error(responseData.message || "OTP Registration failed");
      }

      return { message: responseData.message || "OTP generated successfully" };
    } catch (error: any) {
      console.error("OTP Registration error:", error);
      throw new Error(
        error.message || "OTP Registration failed. Please try again."
      );
    }
  }

  /**
   * Request OTP for login
   */
  async requestLoginOtp(data: OtpRequestData): Promise<{ message: string }> {
    try {
      const response = await fetch(`${this.apiUrl}/auth/customer/otp`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-publishable-api-key": MEDUSA_API_KEY,
        },
        credentials: "include", // Important for cookie handling
        body: JSON.stringify({
          identifier: data.identifier,
        }),
      });

      const responseData = await response.json();

      // Special case: If the response contains "otp_generated" message or location, it's a success
      if (
        responseData.location === "otp_generated" ||
        responseData.message === "otp_generated" ||
        (responseData.type === "unauthorized" &&
          responseData.message === "otp_generated")
      ) {
        return { message: "OTP generated successfully" };
      }

      if (!response.ok) {
        console.error("OTP Request error response:", responseData);
        throw new Error(responseData.message || "OTP Request failed");
      }

      return { message: responseData.location || "OTP generated successfully" };
    } catch (error: any) {
      console.error("OTP Request error:", error);
      throw new Error(error.message || "OTP Request failed. Please try again.");
    }
  }

  /**
   * Verify OTP and get token
   */
  async verifyOtp(data: OtpVerifyData): Promise<AuthResponse> {
    try {
      const response = await fetch(`${this.apiUrl}/auth/customer/otp`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-publishable-api-key": MEDUSA_API_KEY,
        },
        credentials: "include", // Important for cookie handling
        body: JSON.stringify({
          identifier: data.identifier,
          otp: data.otp,
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error("OTP Verification error response:", responseData);
        throw new Error(responseData.message || "OTP Verification failed");
      }

      const { token } = responseData;
      this.setToken(token);

      // Get customer data
      const customerResponse = await fetch(
        `${this.apiUrl}/store/customers/me`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "x-publishable-api-key": MEDUSA_API_KEY,
          },
          credentials: "include", // Important for cookie handling
        }
      );

      if (!customerResponse.ok) {
        console.warn("Customer fetch warning:", await customerResponse.text());
        // Continue even if customer fetch fails, as we have the token
        return { token };
      }

      const customerData = await customerResponse.json();

      return { token, customer: customerData.customer };
    } catch (error: any) {
      console.error("OTP Verification error:", error);
      throw new Error(
        error.message || "OTP Verification failed. Please try again."
      );
    }
  }
}

export default new AuthService();
