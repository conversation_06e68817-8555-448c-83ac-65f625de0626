/**
 * Utility functions for date handling
 */

/**
 * Format a date to a user-friendly format for display
 * @param date - The date to format
 * @returns The formatted date string or placeholder if input is null/undefined
 */
export function formatDate(date: Date | null | undefined): string {
  // If date is null or undefined, return a placeholder
  if (!date) {
    return "Select date";
  }

  // Check if the date is valid
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    console.warn("Invalid date provided to formatDate, returning placeholder");
    return "Select date";
  }

  // For display in the UI, use a more user-friendly format
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
}

/**
 * Calculate the number of nights between two dates
 * @param checkIn - The check-in date
 * @param checkOut - The check-out date
 * @returns The number of nights (defaults to 1 if dates are invalid)
 */
export function calculateNights(
  checkIn: Date | null | undefined,
  checkOut: Date | null | undefined
): number {
  // If dates are null or undefined, use default dates
  const validCheckIn =
    checkIn instanceof Date ? checkIn : getDefaultCheckInDate();
  const validCheckOut =
    checkOut instanceof Date ? checkOut : getDefaultCheckOutDate();

  // Check if dates are valid
  if (isNaN(validCheckIn.getTime()) || isNaN(validCheckOut.getTime())) {
    console.warn(
      "Invalid dates provided to calculateNights, returning default of 1 night"
    );
    return 1;
  }

  const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
  const diffDays = Math.round(
    Math.abs((validCheckOut.getTime() - validCheckIn.getTime()) / oneDay)
  );

  // Ensure we return at least 1 night
  return Math.max(1, diffDays);
}

/**
 * Add days to a date
 * @param date - The starting date
 * @param days - Number of days to add
 * @returns A new date with the days added
 */
export function addDays(date: Date | null | undefined, days: number): Date {
  // If date is null or undefined, use current date
  const validDate = date instanceof Date ? date : new Date();

  // Check if the date is valid
  if (isNaN(validDate.getTime())) {
    console.warn(
      "Invalid date provided to addDays, using current date instead"
    );
    return addDays(new Date(), days);
  }

  const result = new Date(validDate);
  result.setDate(result.getDate() + days);
  return result;
}

/**
 * Get default check-in date (today)
 * @returns The default check-in date
 */
export function getDefaultCheckInDate(): Date {
  const date = new Date();
  date.setDate(date.getDate() + 1);
  return date;
}

/**
 * Get default check-out date (today + 3 days)
 * @returns The default check-out date
 */
export function getDefaultCheckOutDate(): Date {
  const checkOut = new Date();
  checkOut.setDate(checkOut.getDate() + 3);
  return checkOut;
}

/**
 * Format a date to YYYY-MM-DD format for API calls
 * @param date - The date to format
 * @returns The formatted date string or current date if input is null/undefined
 */
export function formatDateForAPI(date: Date | null | undefined): string {
  // If date is null or undefined, use current date
  const validDate = date instanceof Date ? date : new Date();

  // Check if the date is valid
  if (isNaN(validDate.getTime())) {
    console.warn(
      "Invalid date provided to formatDateForAPI, using current date instead"
    );
    return formatDateForAPI(new Date());
  }

  const year = validDate.getFullYear();
  const month = String(validDate.getMonth() + 1).padStart(2, "0");
  const day = String(validDate.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}
