interface CookieConsent {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  preferences: boolean;
}

const COOKIE_CONSENT_KEY = "perfect-piste-cookie-consent";

/**
 * Get the stored cookie consent preferences
 */
export const getCookieConsent = (): CookieConsent | null => {
  if (typeof window === "undefined" || typeof localStorage === "undefined")
    return null;

  try {
    const storedConsent = localStorage.getItem(COOKIE_CONSENT_KEY);
    if (!storedConsent) return null;

    return JSON.parse(storedConsent) as CookieConsent;
  } catch (error) {
    console.error("Error retrieving cookie consent:", error);
    return null;
  }
};

/**
 * Set the cookie consent preferences
 */
export const setCookieConsent = (consent: CookieConsent): void => {
  if (typeof window === "undefined" || typeof localStorage === "undefined")
    return;

  try {
    localStorage.setItem(COOKIE_CONSENT_KEY, JSON.stringify(consent));

    // Here you would typically set actual cookies based on the consent
    // For example, if consent.analytics is true, you would set analytics cookies

    // Example of how you might handle Google Analytics consent
    if (consent.analytics) {
      enableAnalytics();
    } else {
      disableAnalytics();
    }
  } catch (error) {
    console.error("Error setting cookie consent:", error);
  }
};

/**
 * Clear the cookie consent preferences
 */
export const clearCookieConsent = (): void => {
  if (typeof window === "undefined" || typeof localStorage === "undefined")
    return;

  try {
    localStorage.removeItem(COOKIE_CONSENT_KEY);
  } catch (error) {
    console.error("Error clearing cookie consent:", error);
  }
};

// Placeholder functions for enabling/disabling analytics
// These would be replaced with actual implementation
const enableAnalytics = () => {
  // Example: Initialize Google Analytics
  console.log("Analytics enabled");
};

const disableAnalytics = () => {
  // Example: Disable Google Analytics
  console.log("Analytics disabled");
};
