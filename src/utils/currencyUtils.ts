/**
 * Currency utilities for the application
 * Provides mapping between currency codes and symbols for display purposes
 */

/**
 * Map of currency codes to their symbols
 * This is used for display purposes only - the original currency code is preserved in the data
 */
export const CURRENCY_SYMBOLS: Record<string, string> = {
  // Major currencies
  usd: "$",
  eur: "€",
  gbp: "£",
  jpy: "¥",
  cny: "¥",
  inr: "₹",
  
  // Other common currencies
  aud: "A$",
  cad: "C$",
  chf: "CHF",
  hkd: "HK$",
  nzd: "NZ$",
  sgd: "S$",
  
  // European currencies
  dkk: "kr",
  nok: "kr",
  sek: "kr",
  rub: "₽",
  try: "₺",
  
  // Middle Eastern currencies
  aed: "د.إ",
  sar: "﷼",
  
  // Asian currencies
  krw: "₩",
  thb: "฿",
  myr: "RM",
  idr: "Rp",
  php: "₱",
  
  // Latin American currencies
  mxn: "Mex$",
  brl: "R$",
  ars: "AR$",
  clp: "CLP$",
  
  // African currencies
  zar: "R",
  ngn: "₦",
  egp: "E£",
};

/**
 * Get the currency symbol for a given currency code
 * @param currencyCode - The currency code (e.g., "USD", "EUR")
 * @returns The currency symbol (e.g., "$", "€")
 */
export function getCurrencySymbol(currencyCode: string): string {
  if (!currencyCode) return "$"; // Default to $ if no currency code provided
  
  // Normalize the currency code to lowercase for consistent lookup
  const normalizedCode = currencyCode.toLowerCase();
  
  // Return the symbol if it exists in our mapping, otherwise return the original code
  return CURRENCY_SYMBOLS[normalizedCode] || currencyCode;
}

/**
 * Format a price with the appropriate currency symbol
 * @param amount - The amount to format
 * @param currencyCode - The currency code (e.g., "USD", "EUR")
 * @returns Formatted price with currency symbol
 */
export function formatPriceWithSymbol(
  amount: number,
  currencyCode: string = "USD"
): string {
  const symbol = getCurrencySymbol(currencyCode);
  return `${symbol} ${amount.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;
}
