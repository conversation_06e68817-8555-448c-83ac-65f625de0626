// Sample data for the application

// Hotels data

// Destinations data
export const destinations = [
  {
    id: "switzerland",
    name: "Switzerland",
    description:
      "Experience the breathtaking beauty of the Alps, pristine lakes, and charming villages. Switzerland offers a perfect blend of natural wonders and refined luxury, from exclusive ski resorts to wellness retreats with panoramic mountain views.",
    propertyCount: 24,
    imageUrl:
      "https://images.unsplash.com/photo-1530122037265-a5f1f91d3b99?ixlib=rb-4.0.3&auto=format&fit=crop&w=1080&q=80",
    featured: true,
    activities: [
      "Alpine Skiing",
      "Luxury Watch Tours",
      "Lake Cruises",
      "Chocolate Tastings",
      "Mountain Hiking",
    ],
  },
  {
    id: "japan",
    name: "Japan",
    description:
      "Discover the perfect harmony of ancient tradition and cutting-edge modernity. From serene ryokans with private onsen baths to exclusive Tokyo penthouses, Japan offers unique luxury experiences rooted in mindfulness and precision.",
    propertyCount: 18,
    imageUrl:
      "https://images.unsplash.com/photo-1492571350019-22de08371fd3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1080&q=80",
    featured: true,
    activities: [
      "Cherry Blossom Viewing",
      "Tea Ceremonies",
      "Private Zen Gardens",
      "Kaiseki Dining",
      "Hot Spring Retreats",
    ],
  },
  {
    id: "maldives",
    name: "Maldives",
    description:
      "Escape to paradise in the Indian Ocean, where luxurious overwater villas perch above crystal-clear turquoise lagoons. The Maldives offers unparalleled privacy, exclusive underwater dining, and direct access to vibrant coral reefs.",
    propertyCount: 16,
    imageUrl:
      "https://images.unsplash.com/photo-1514282401047-d79a71a590e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1080&q=80",
    featured: true,
    activities: [
      "Underwater Dining",
      "Private Reef Snorkeling",
      "Sunset Dolphin Cruises",
      "Island Hopping",
      "Seaplane Tours",
    ],
  },
];

// Room types data
export const roomTypes = [
  {
    id: 1,
    name: "Deluxe Suite",
    description:
      "Spacious suite with mountain view, king bed, and luxury amenities",
    maxGuests: 2,
    bedType: "1 king bed",
    size: "65 sq.m / 700 sq.ft",
    price: 350,
    images: [
      "https://images.unsplash.com/photo-1611892440504-42a792e24d32?q=80&w=2070&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1540518614846-7eded433c457?q=80&w=1957&auto=format&fit=crop",
    ],
    features: [
      "King-size bed with premium linens",
      "Separate living area",
      "Private balcony with mountain view",
      "Marble bathroom with soaking tub",
      "Walk-in rainfall shower",
      "High-speed WiFi",
      "Smart TV with streaming services",
      "Nespresso coffee machine",
    ],
  },
  {
    id: 2,
    name: "Family Room",
    description:
      "Comfortable room for families with two queen beds and extra space",
    maxGuests: 4,
    bedType: "2 queen beds",
    size: "80 sq.m / 860 sq.ft",
    price: 450,
    images: [
      "https://images.unsplash.com/photo-1566665797739-1674de7a421a?q=80&w=1974&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1598928506311-c55ded91a20c?q=80&w=2070&auto=format&fit=crop",
    ],
    features: [
      "Two queen beds with premium linens",
      "Spacious living area with sofa",
      "Family-friendly amenities",
      "Large bathroom with bathtub and shower",
      "Smart TV with kids' channels",
      "High-speed WiFi",
      "Mini refrigerator",
      "Board games and books available",
    ],
  },
  {
    id: 3,
    name: "Standard Room",
    description:
      "Cozy room with all essential amenities and a comfortable queen bed",
    maxGuests: 2,
    bedType: "1 queen bed",
    size: "40 sq.m / 430 sq.ft",
    price: 250,
    images: [
      "https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?q=80&w=2070&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1551105378-78e609e1d168?q=80&w=1974&auto=format&fit=crop",
    ],
    features: [
      "Queen-size bed",
      "Private bathroom with shower",
      "Work desk",
      "Smart TV",
      "High-speed WiFi",
      "Coffee and tea making facilities",
      "Air conditioning",
      "Daily housekeeping",
    ],
  },
];

// Get destination by ID
export const getDestinationById = (id: string) => {
  return destinations.find((destination) => destination.id === id);
};
