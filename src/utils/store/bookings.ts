/**
 * Utility functions for fetching booking data from the backend API
 */

// Common headers for API requests
const getHeaders = () => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    "x-publishable-api-key": import.meta.env.PUBLIC_BACKEND_API_KEY || "",
  };

  // Get auth_token from localStorage
  const authToken = localStorage.getItem("auth_token");
  if (authToken) {
    // Add auth token as a custom header that the backend can use
    headers["Authorization"] = `Bearer ${authToken}`;
  }

  return headers;
};

/**
 * Interface for booking data returned from the API
 */
export interface Booking {
  id: string;
  hotel_name: string;
  room_type: string;
  room_config_name?: string;
  check_in_date: string;
  check_out_date: string;
  total_amount: number;
  currency_code: string;
  status: "confirmed" | "pending" | "cancelled";
  created_at: string;
  // Add any additional fields that might be returned by the API
}

/**
 * Interface for pagination parameters
 */
export interface PaginationParams {
  limit?: number;
  offset?: number;
}

/**
 * Fetch customer bookings
 * @param params - Pagination parameters
 * @returns A promise that resolves to the bookings data and count
 */
export async function fetchCustomerBookings(
  params?: PaginationParams
): Promise<{
  bookings: Booking[];
  count: number;
}> {
  try {
    // Ensure we have a publishable API key
    const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY;
    if (!apiKey) {
      console.warn("No publishable API key found in environment variables");
    }

    // Get auth token from localStorage
    const authToken = localStorage.getItem("auth_token");
    if (!authToken) {
      throw new Error("Authentication token not found");
    }

    // Set the auth token as a cookie before making the request
    // This is a workaround since we can't directly set Cookie headers in fetch
    document.cookie = `connect.sid=${authToken}; path=/`;

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params?.limit) {
      queryParams.append("limit", params.limit.toString());
    }
    if (params?.offset) {
      queryParams.append("offset", params.offset.toString());
    }

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/bookings${
      queryParams.toString() ? `?${queryParams.toString()}` : ""
    }`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
      credentials: "include", // Important for cookie handling
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    return {
      bookings: data.bookings || [],
      count: data.count || 0,
    };
  } catch (e) {
    console.error("Error fetching customer bookings:", e);
    throw e;
  }
}

/**
 * Fetch a single booking by ID
 * @param id - The booking ID
 * @returns A promise that resolves to the booking data
 */
/**
 * Download invoice for a booking
 * @param bookingId - The booking ID
 * @returns A promise that resolves when the download starts
 */
export async function downloadBookingInvoice(bookingId: string): Promise<void> {
  try {
    // Ensure we have a publishable API key
    const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY;
    if (!apiKey) {
      console.warn("No publishable API key found in environment variables");
    }
    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/bookings/${bookingId}/invoice`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
      credentials: "include", // Important for cookie handling
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message ||
          `Failed to download invoice: ${response.status} ${response.statusText}`
      );
    }

    // Create blob and download
    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = downloadUrl;
    a.download = `invoice-${bookingId}.pdf`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(downloadUrl);
    document.body.removeChild(a);
  } catch (error: any) {
    console.error("Error downloading invoice:", error);
    throw new Error(
      error.message || "Failed to download invoice. Please try again."
    );
  }
}

export async function fetchBookingById(id: string): Promise<Booking> {
  try {
    // Ensure we have a publishable API key
    const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY;
    if (!apiKey) {
      console.warn("No publishable API key found in environment variables");
    }

    // Get auth token from localStorage
    const authToken = localStorage.getItem("auth_token");
    if (!authToken) {
      throw new Error("Authentication token not found");
    }

    // Set the auth token as a cookie before making the request
    // This is a workaround since we can't directly set Cookie headers in fetch
    document.cookie = `connect.sid=${authToken}; path=/`;

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/bookings/${id}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
      credentials: "include", // Important for cookie handling
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data.booking) {
      throw new Error("Booking not found");
    }

    return data.booking;
  } catch (e) {
    console.error("Error fetching booking details:", e);
    throw e;
  }
}
