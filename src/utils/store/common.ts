/**
 * Common utility functions for API requests
 */

/**
 * Get common headers for API requests
 * @returns Headers object with content type and API key
 */
export const getHeaders = () => ({
  "Content-Type": "application/json",
  "x-publishable-api-key": import.meta.env.PUBLIC_BACKEND_API_KEY || "",
});

/**
 * Simple cache implementation for API responses
 */
export class SimpleCache {
  private cache: Map<string, { data: any; timestamp: number }>;
  private defaultTTL: number;

  constructor(defaultTTL = 5 * 60 * 1000) {
    // Default TTL is 5 minutes
    this.cache = new Map();
    this.defaultTTL = defaultTTL;
  }

  /**
   * Get data from cache
   * @param key - Cache key
   * @returns Cached data or undefined if not found or expired
   */
  get<T>(key: string): T | undefined {
    const item = this.cache.get(key);
    if (!item) return undefined;

    const now = Date.now();
    if (now - item.timestamp > this.defaultTTL) {
      // Cache expired
      this.cache.delete(key);
      return undefined;
    }

    return item.data as T;
  }

  /**
   * Set data in cache
   * @param key - Cache key
   * @param data - Data to cache
   */
  set(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * Clear the entire cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Delete a specific key from cache
   * @param key - Cache key to delete
   */
  delete(key: string): void {
    this.cache.delete(key);
  }
}
