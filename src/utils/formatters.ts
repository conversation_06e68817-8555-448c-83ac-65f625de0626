import { getCurrencySymbol } from "./currencyUtils";

/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param currency - The currency code (default: USD)
 * @param locale - The locale to use for formatting (default: en-US)
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  currency: string = "USD",
  locale: string = "en-US"
): string {
  // For display purposes, we'll continue to use Intl.NumberFormat
  // This ensures proper formatting based on locale
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

/**
 * Format a price with currency code
 * @param price - The price to format
 * @param currencyCode - The currency code (default: USD)
 * @returns Formatted price string with currency code
 */
export function formatPrice(
  price: number,
  currencyCode: string = "USD"
): string {
  // Use the currency symbol mapping from currencyUtils
  // This will be imported at the top of the file
  const symbol = getCurrencySymbol(currencyCode);
  return `${symbol} ${price.toFixed(0)}`;
}

/**
 * Format a date as a string
 * @param date - The date to format
 * @param options - Intl.DateTimeFormatOptions
 * @param locale - The locale to use for formatting (default: en-US)
 * @returns Formatted date string
 */
export function formatDate(
  date: Date,
  options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  },
  locale: string = "en-US"
): string {
  return new Intl.DateTimeFormat(locale, options).format(date);
}

/**
 * Format a number with commas
 * @param num - The number to format
 * @returns Formatted number string with commas
 */
export function formatNumber(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

/**
 * Format a price with currency symbol
 * Uses the currency symbol mapping from currencyUtils
 * @param amount - The amount to format
 * @param currencyCode - The currency code (e.g., "USD", "EUR")
 * @returns Formatted price with currency symbol
 */
export function formatPriceWithSymbol(
  amount: number,
  currencyCode: string = "USD"
): string {
  // Use the currency symbol mapping from currencyUtils
  // This will be imported at the top of the file
  const symbol = getCurrencySymbol(currencyCode);
  return `${symbol} ${formatNumber(amount)}`;
}
