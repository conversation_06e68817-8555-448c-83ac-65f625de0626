/**
 * Utility functions for cart operations
 */

export interface CartItem {
  id: string;
  hotelId: string;
  roomId: string;
  roomType: string;
  hotelName: string;
  checkIn: string;
  checkOut: string;
  checkInTime?: string;
  checkOutTime?: string;
  guests: number;
  infants?: number;
  price: number;
  currency: string;
  mealPlan?: string;
  addedAt: string;
  image?: string;
  regionId?: string; // Added region_id field
  available_rooms?: number; // Number of available rooms
  quantity?: number; // Number of rooms being booked
}

/**
 * Get cart data from localStorage
 * @returns The cart data as an array
 */
export const getCart = (): CartItem[] => {
  if (typeof window === "undefined") return [];

  try {
    const cartData = localStorage.getItem("cart");

    if (!cartData) {
      return [];
    }

    const cart = JSON.parse(cartData);

    if (!Array.isArray(cart)) {
      console.error("[cartUtils] Cart data is not an array:", cart);
      return [];
    }

    return cart;
  } catch (error) {
    console.error("[cartUtils] Error getting cart:", error);
    return [];
  }
};

/**
 * Save cart data to localStorage
 * @param cart - The cart data to save
 * @returns true if successful, false if failed
 */
export const saveCart = (cart: CartItem[]): boolean => {
  if (typeof window === "undefined") return false;

  try {
    const cartData = JSON.stringify(cart);
    localStorage.setItem("cart", cartData);

    // Verify the save worked
    const verifyData = localStorage.getItem("cart");

    return true;
  } catch (error) {
    console.error("[cartUtils] Error saving cart:", error);
    return false;
  }
};

/**
 * Add an item to the cart
 * @param item - The item to add
 * @returns true if successful, false if failed
 */
export const addToCart = (item: CartItem): boolean => {
  if (typeof window === "undefined") return false;

  try {
    const cart = getCart();

    // Remove any existing items with the same ID
    const filteredCart = cart.filter((cartItem) => cartItem.id !== item.id);

    // Add the new item
    filteredCart.push(item);

    // Save the cart
    return saveCart(filteredCart);
  } catch (error) {
    console.error("[cartUtils] Error adding item to cart:", error);
    return false;
  }
};

/**
 * Remove an item from the cart
 * @param id - The ID of the item to remove
 * @returns true if successful, false if failed
 */
export const removeFromCart = (id: string): boolean => {
  if (typeof window === "undefined") return false;

  try {
    const cart = getCart();

    // Remove the item
    const filteredCart = cart.filter((item) => item.id !== id);

    // Save the cart
    return saveCart(filteredCart);
  } catch (error) {
    console.error("[cartUtils] Error removing item from cart:", error);
    return false;
  }
};

/**
 * Clear the cart
 * @returns true if successful, false if failed
 */
export const clearCart = (): boolean => {
  if (typeof window === "undefined") return false;

  try {
    localStorage.setItem("cart", "[]");
    return true;
  } catch (error) {
    console.error("[cartUtils] Error clearing cart:", error);
    return false;
  }
};

/**
 * Calculate the total price of the cart
 * @returns The total price
 */
export const calculateCartTotal = (): number => {
  try {
    const cart = getCart();

    return cart.reduce(
      (total, item) =>
        total + (typeof item.price === "number" ? item.price : 0),
      0
    );
  } catch (error) {
    console.error("[cartUtils] Error calculating cart total:", error);
    return 0;
  }
};
