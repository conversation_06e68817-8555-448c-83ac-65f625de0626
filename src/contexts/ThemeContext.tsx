import React, { createContext, useState, useContext, useEffect, type ReactNode } from "react";

// Define all available themes
export type ThemeName = "alpine" | "midnight" | "frost" | "default";

interface ThemeContextType {
  theme: ThemeName;
  setTheme: (theme: ThemeName) => void;
}

const ThemeContext = createContext<ThemeContextType>({
  theme: "default",
  setTheme: () => {},
});

export const useTheme = () => useContext(ThemeContext);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setTheme] = useState<ThemeName>("default");

  // Check if user has a preferred theme stored
  useEffect(() => {
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      const storedTheme = localStorage.getItem(
        "perfectpiste-theme"
      ) as ThemeName | null;

      if (
        storedTheme &&
        ["default", "alpine", "midnight", "frost"].includes(storedTheme)
      ) {
        setTheme(storedTheme);
        document.documentElement.classList.remove(
          "theme-default",
          "theme-alpine",
          "theme-midnight",
          "theme-frost"
        );
        document.documentElement.classList.add(`theme-${storedTheme}`);
      } else {
        // Default theme
        setTheme("default");
        document.documentElement.classList.add("theme-default");
      }
    }
  }, []);

  const handleThemeChange = (newTheme: ThemeName) => {
    setTheme(newTheme);

    if (typeof document !== 'undefined') {
      // Remove all theme classes
      document.documentElement.classList.remove(
        "theme-default",
        "theme-alpine",
        "theme-midnight",
        "theme-frost"
      );
      // Add the new theme class
      document.documentElement.classList.add(`theme-${newTheme}`);
    }

    // Store the theme preference
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      localStorage.setItem("perfectpiste-theme", newTheme);
    }
  };

  return (
    <ThemeContext.Provider value={{ theme, setTheme: handleThemeChange }}>
      {children}
    </ThemeContext.Provider>
  );
};
