import React, { createContext, useContext, useState, useEffect } from "react";
import AuthService from "../services/AuthService";

// Define ReactNode type
type ReactNode = React.ReactNode;

interface UserContextType {
  user: any | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    first_name: string;
    last_name: string;
    email: string;
    password: string;
    phone?: string;
  }) => Promise<void>;
  registerWithOtp: (userData: {
    first_name: string;
    last_name: string;
    identifier: string;
    phone?: string;
  }) => Promise<{ message: string }>;
  requestLoginOtp: (identifier: string) => Promise<{ message: string }>;
  verifyOtp: (identifier: string, otp: string) => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<any | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuth = async () => {
      try {
        if (AuthService.isAuthenticated()) {
          const userData = await AuthService.getCurrentCustomer();
          setUser(userData);
          setIsAuthenticated(true);
        }
      } catch (err) {
        console.error("Authentication check failed:", err);
        AuthService.logout();
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      const result = await AuthService.login({ email, password });

      if (result && result.customer) {
        setUser(result.customer);
        setIsAuthenticated(true);
      } else {
        console.error("UserContext: Login returned invalid data", result);
        throw new Error("Login failed: Invalid response from server");
      }
    } catch (err: any) {
      console.error("UserContext: Login error", err);
      setError(err.message || "Login failed");
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: {
    first_name: string;
    last_name: string;
    email: string;
    password: string;
    phone?: string;
  }) => {
    setLoading(true);
    setError(null);
    try {
      const result = await AuthService.register(userData);

      // Just check if registration was successful, but don't set user data
      // User will need to login separately after registration
      if (result) {
        // Don't set user or isAuthenticated here
        // The user will be redirected to login page
      } else {
        console.error(
          "UserContext: Registration returned invalid data",
          result
        );
        throw new Error("Registration failed: Invalid response from server");
      }
    } catch (err: any) {
      console.error("UserContext: Registration error", err);
      setError(err.message || "Registration failed");
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    await AuthService.logout();
    setUser(null);
    setIsAuthenticated(false);
  };

  // Register with OTP
  const registerWithOtp = async (userData: {
    first_name: string;
    last_name: string;
    identifier: string;
    phone?: string;
  }) => {
    setLoading(true);
    setError(null);
    try {
      const result = await AuthService.registerWithOtp(userData);
      return result;
    } catch (err: any) {
      console.error("UserContext: OTP Registration error", err);
      setError(err.message || "OTP Registration failed");
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Request login OTP
  const requestLoginOtp = async (identifier: string) => {
    setLoading(true);
    setError(null);
    try {
      const result = await AuthService.requestLoginOtp({ identifier });
      return result;
    } catch (err: any) {
      console.error("UserContext: Login OTP request error", err);
      setError(err.message || "Login OTP request failed");
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Verify OTP
  const verifyOtp = async (identifier: string, otp: string) => {
    setLoading(true);
    setError(null);
    try {
      const result = await AuthService.verifyOtp({ identifier, otp });

      if (result && result.customer) {
        setUser(result.customer);
        setIsAuthenticated(true);
      } else if (result && result.token) {
        setIsAuthenticated(true);
        // Try to fetch user data separately
        try {
          const userData = await AuthService.getCurrentCustomer();
          setUser(userData);
        } catch (userErr) {
          console.warn(
            "UserContext: Could not fetch user data after OTP verification",
            userErr
          );
        }
      } else {
        console.error(
          "UserContext: OTP verification returned invalid data",
          result
        );
        throw new Error(
          "OTP verification failed: Invalid response from server"
        );
      }
    } catch (err: any) {
      console.error("UserContext: OTP verification error", err);
      setError(err.message || "OTP verification failed");
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return (
    <UserContext.Provider
      value={{
        user,
        loading,
        error,
        login,
        register,
        registerWithOtp,
        requestLoginOtp,
        verifyOtp,
        logout,
        isAuthenticated,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
