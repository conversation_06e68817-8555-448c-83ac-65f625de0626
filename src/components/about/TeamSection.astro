---
// Props for the component
interface TeamMember {
  name: string;
  role: string;
  imageUrl: string;
}

interface Props {
  teamMembers?: TeamMember[];
}

const defaultTeamMembers = [
  {
    name: "<PERSON>",
    role: "Founder & CEO",
    imageUrl:
      "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
  },
  {
    name: "<PERSON>",
    role: "Head of Destinations",
    imageUrl:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
  },
  {
    name: "<PERSON>",
    role: "Luxury Concierge Director",
    imageUrl:
      "https://images.unsplash.com/photo-1587614387466-0a72ca909e16?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
  },
];

const { teamMembers = defaultTeamMembers } = Astro.props;
---

<!-- Team Section -->
<section class="py-20 container-custom">
  <div class="mx-auto">
    <h2 class="text-3xl md:text-4xl font-baskervville mb-16 text-center">
      Our Team of Experts
    </h2>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {
        teamMembers.map((member) => (
          <div class="border border-border rounded-lg overflow-hidden shadow-sm">
            <div class="h-80 overflow-hidden">
              <img
                src={member.imageUrl}
                alt={member.name}
                class="w-full h-full object-cover"
              />
            </div>
            <div class="p-6 text-center">
              <h3 class="text-xl font-baskervville mb-1">{member.name}</h3>
              <p class="text-foreground/70">{member.role}</p>
            </div>
          </div>
        ))
      }
    </div>
  </div>
</section>
