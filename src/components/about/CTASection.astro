---
import <PERSON><PERSON> from "../ui/Button.astro";

// Props for the component
interface Props {
  title?: string;
  subtitle?: string;
  primaryButtonLink?: string;
  secondaryButtonLink?: string;
}

const {
  title = "Begin Your Luxury Journey",
  subtitle = "Let our team of experts create a bespoke travel experience tailored to your desires.",
  primaryButtonLink = "/stays",
  secondaryButtonLink = "/contact",
} = Astro.props;
---

<!-- CTA Section -->
<section class="py-20 bg-primary text-primary-foreground">
  <div class="container-custom">
    <div class="max-w-3xl mx-auto text-center">
      <h2 class="text-3xl md:text-4xl font-baskervville mb-6">{title}</h2>
      <p class="text-xl mb-10">
        {subtitle}
      </p>
      <div class="flex flex-col sm:flex-row justify-center gap-4">
        <Button
          href={primaryButtonLink}
          variant="secondary"
          size="md"
          withArrow={true}
        >
          Explore Our Collection
        </Button>

        <Button
          href={secondaryButtonLink}
          variant="outline"
          size="md"
          withArrow={true}
          arrowVisible={true}
        >
          Contact Us
        </Button>
      </div>
    </div>
  </div>
</section>
