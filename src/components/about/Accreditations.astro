---
// Props for the component
interface Accreditation {
  name: string;
  imageUrl: string;
}

interface Props {
  accreditations?: Accreditation[];
}

const defaultAccreditations = [
  {
    name: "Luxury Travel Association",
    imageUrl:
      "https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
  },
  {
    name: "International Hotel Awards",
    imageUrl:
      "https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
  },
  {
    name: "Global Tourism Council",
    imageUrl:
      "https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
  },
  {
    name: "Five Star Alliance",
    imageUrl:
      "https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
  },
];

const { accreditations = defaultAccreditations } = Astro.props;
---

<!-- Accreditations -->
<section
  class="py-20 bg-accent/30 container-custom"
  style="background-color: color-mix(in srgb, var(--accent) 30%, transparent);"
>
  <div class="mx-auto">
    <h2 class="text-3xl md:text-4xl font-baskervville mb-16 text-center">
      Our Accreditations
    </h2>

    <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
      {
        accreditations.map((accreditation) => (
          <div class="bg-card p-6 rounded-lg flex items-center justify-center shadow-sm">
            <img
              src={accreditation.imageUrl}
              alt={accreditation.name}
              class="h-12 object-contain"
            />
          </div>
        ))
      }
    </div>
  </div>
</section>
