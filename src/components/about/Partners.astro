---
// Props for the component
interface Partner {
  name: string;
  imageUrl: string;
}

interface Props {
  partners?: Partner[];
}

const defaultPartners = [
  {
    name: "Ritz <PERSON>",
    imageUrl:
      "https://images.unsplash.com/photo-1551150441-3f3828204ef0?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=100&q=80",
  },
  {
    name: "Four Seasons",
    imageUrl:
      "https://images.unsplash.com/photo-1551150441-3f3828204ef0?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=100&q=80",
  },
  {
    name: "Waldorf Astoria",
    imageUrl:
      "https://images.unsplash.com/photo-1551150441-3f3828204ef0?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=100&q=80",
  },
  {
    name: "Mandarin Oriental",
    imageUrl:
      "https://images.unsplash.com/photo-1551150441-3f3828204ef0?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=100&q=80",
  },
  {
    name: "St. <PERSON>",
    imageUrl:
      "https://images.unsplash.com/photo-1551150441-3f3828204ef0?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=100&q=80",
  },
  {
    name: "Aman Resorts",
    imageUrl:
      "https://images.unsplash.com/photo-1551150441-3f3828204ef0?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=100&q=80",
  },
];

const { partners = defaultPartners } = Astro.props;
---

<!-- Hotel Logo Banner -->
<section class="py-20 container-custom">
  <div class="mx-auto">
    <h2 class="text-3xl md:text-4xl font-baskervville mb-16 text-center">
      Our Luxury Partners
    </h2>

    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
      {
        partners.map((partner) => (
          <div class="bg-card p-6 rounded-lg flex items-center justify-center shadow-sm">
            <img
              src={partner.imageUrl}
              alt={partner.name}
              class="h-10 object-contain"
            />
          </div>
        ))
      }
    </div>
  </div>
</section>
