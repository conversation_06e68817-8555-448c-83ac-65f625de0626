---
// Props for the component
interface Props {
  title: string;
  subtitle: string;
  imageUrl: string;
}

const {
  title = "About Perfect Piste",
  subtitle = "Crafting extraordinary ski journeys for discerning travelers since 2005",
  imageUrl = "https://images.unsplash.com/photo-1596394516093-501ba68a0ba6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
} = Astro.props;
---

<!-- Hero Section -->
<div class="container-custom">
  <section class="relative h-[60vh] overflow-hidden rounded-lg">
    <img
      src={imageUrl}
      alt={title}
      class="w-full h-full object-cover rounded-lg"
    />
    <div
      class="absolute inset-0 bg-gradient-to-r from-black/60 to-black/40 flex items-center"
    >
      <div class="container-custom">
        <div>
          <div class="max-w-3xl">
            <h1
              class="text-4xl md:text-5xl lg:text-6xl text-white font-baskervville mb-6 animate-fade-in"
            >
              {title}
            </h1>
            <p
              class="text-xl text-white/90 mb-4 animate-fade-in animate-delay-200"
            >
              {subtitle}
            </p>
            <div class="animate-fade-in animate-delay-400">
              <a
                href="/contact"
                class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
              >
                Get in Touch
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="ml-2"
                >
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                  <polyline points="12 5 19 12 12 19"></polyline>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

<style>
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
    opacity: 0;
  }

  .animate-delay-200 {
    animation-delay: 0.2s;
  }

  .animate-delay-400 {
    animation-delay: 0.4s;
  }
</style>
