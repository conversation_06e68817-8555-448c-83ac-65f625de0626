---
// Props for the component
interface Props {
  imageUrl: string;
}

const {
  imageUrl = "https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1080&q=80",
} = Astro.props;
---

<!-- Our Story -->
<section class="py-20 container-custom">
  <div class="mx-auto">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div>
        <h2 class="text-3xl md:text-4xl font-baskervville mb-6">Our Story</h2>
        <p class="text-lg mb-6">
          Founded in 2005, <PERSON>ste emerged from a simple yet profound
          vision: to create transformative ski travel experiences that transcend
          the ordinary and speak to the soul of the discerning traveler.
        </p>
        <p class="mb-6">
          What began as a boutique agency serving a select clientele has evolved
          into a globally recognized name in luxury ski travel, while retaining
          our founding principle of personalized service and unwavering
          attention to detail.
        </p>
        <p>
          Today, we curate extraordinary journeys to the world's most exclusive
          ski destinations, partnering with prestigious properties and local
          experts to provide our clients with authentic, immersive experiences
          that create lasting memories.
        </p>
      </div>
      <div class="order-first lg:order-last">
        <div class="relative h-96 rounded-lg overflow-hidden">
          <img
            src={imageUrl}
            alt="Luxury Ski Resort"
            class="w-full h-full object-cover"
          />
        </div>
      </div>
    </div>
  </div>
</section>
