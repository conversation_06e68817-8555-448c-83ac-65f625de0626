---
interface Hotel {
  id: number | string;
  name: string;
  location: string;
  imageUrl: string;
  description?: string;
}

interface Props {
  hotels: Hotel[];
  title?: string;
}

const { hotels, title = "Explore All Hotels" } = Astro.props;
---

<section class="pt-4 pb-16 bg-white">
  <div class="container-custom">
    <div
      class="flex flex-col sm:flex-row justify-between items-start mb-6 sm:mb-10"
    >
      <div>
        <span class="text-sm uppercase tracking-wider text-[#285DA6] font-karla"
          >All Hotels</span
        >
        <h2
          class="text-2xl sm:text-[28px] md:text-[32px] font-baskervville uppercase tracking-[0.1em] mt-2 sm:whitespace-nowrap"
        >
          {title}
        </h2>
        <p class="font-baskervville text-sm sm:text-base mt-2 max-w-2xl">
          Discover our handpicked collection of the world's most extraordinary
          luxury accommodations
        </p>
      </div>
    </div>

    <style>
      h2 {
        font-size: clamp(1.5rem, 1.8vw, 1.8rem);
        overflow: visible;
        max-width: 100%;
      }

      @media (max-width: 1200px) {
        h2 {
          font-size: clamp(1.2rem, 1.4vw, 1.4rem);
          letter-spacing: 0.08em;
        }
      }
    </style>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {
        hotels.map((hotel) => (
          <div class="overflow-hidden bg-card border border-border/10 rounded-md shadow-sm transition-all duration-300 hover:shadow-md hover:border-foreground/20 group-hover:-translate-y-1">
            <a href={`/stays/${hotel.id}`} class="block group">
              <div class="h-48 overflow-hidden">
                <img
                  src={hotel.imageUrl}
                  alt={hotel.name}
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
              </div>

              <div class="p-6">
                <h3 class="text-xl font-medium font-baskervville mb-2">
                  {hotel.name}
                </h3>
                <p class="text-foreground/70 text-sm mb-4 line-clamp-2">
                  {hotel.description ||
                    "Experience luxury and comfort in this exceptional accommodation, offering the perfect blend of elegance and mountain charm."}
                </p>

                <div class="flex items-center text-primary">
                  <span class="mr-1">Explore</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M5 12h14" />
                    <path d="m12 5 7 7-7 7" />
                  </svg>
                </div>
              </div>
            </a>
          </div>
        ))
      }
    </div>
  </div>
</section>
