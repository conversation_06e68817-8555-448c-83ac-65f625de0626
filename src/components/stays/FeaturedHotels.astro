---
import FeaturedSimpleHotelCard from "../hotels/FeaturedSimpleHotelCard.astro";

interface Hotel {
  id: number | string;
  name: string;
  location: string;
  rating?: number;
  price?: number;
  currency?: string;
  imageUrl: string;
  featured?: boolean;
  description?: string;
  tags?: string[] | string[][];
}

interface Props {
  hotels: Hotel[];
  backgroundImage?: string;
}

const {
  hotels,
  backgroundImage = "https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
} = Astro.props;

// Filter hotels that are featured, or use all if none are marked as featured
const featuredHotels =
  hotels.filter((hotel) => hotel.featured === true).length > 0
    ? hotels.filter((hotel) => hotel.featured === true)
    : hotels;
---

<section class="py-16 relative">
  <div class="absolute inset-0 z-0">
    <img
      src={backgroundImage}
      alt="Luxury background"
      class="w-full h-full object-cover opacity-15"
    />
    <div class="absolute inset-0 bg-background backdrop-blur-sm"></div>
  </div>
  <div class="container-custom relative z-10">
    <div class="flex flex-col sm:flex-row justify-between items-start mb-10">
      <div class="max-w-3xl">
        <p
          class="text-sm uppercase tracking-wider text-[#285DA6] font-karla mb-2"
        >
          Stays
        </p>
        <h2
          id="featured-hotels"
          class="section-title font-baskervville uppercase tracking-[0.1em] text-left"
        >
          FEATURED HOTELS
        </h2>
        <p class="section-subtitle text-left mt-4">
          Each property in our collection has been personally visited and vetted
          to ensure it meets our exacting standards of luxury, authenticity, and
          exceptional service.
        </p>
      </div>
      <div class="mt-4 sm:mt-0">
        <a
          href="#all-hotels"
          class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-[#285DA6] border-b border-[#285DA6] pb-1 transition-all hover:border-transparent whitespace-nowrap"
        >
          View All Hotels
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="ml-2 text-[#285DA6]"
          >
            <line x1="5" y1="12" x2="19" y2="12"></line>
            <polyline points="12 5 19 12 12 19"></polyline>
          </svg>
        </a>
      </div>
    </div>

    <style>
      .section-title {
        white-space: normal;
        font-size: clamp(1.25rem, 1.5vw, 1.8rem);
        overflow: visible;
        max-width: 100%;
        text-align: left;
      }

      .section-subtitle {
        font-size: 0.875rem;
        line-height: 1.5rem;
        font-weight: 300;
      }

      @media (min-width: 640px) {
        .section-title {
          white-space: nowrap;
        }
      }

      @media (max-width: 1200px) {
        .section-title {
          font-size: clamp(1.2rem, 1.4vw, 1.4rem);
          letter-spacing: 0.08em;
        }
      }
    </style>

    <div
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 sm:gap-10 lg:gap-12 max-w-[1400px] mx-auto"
    >
      {
        featuredHotels.map((hotel) => (
          <FeaturedSimpleHotelCard
            id={hotel.id}
            name={hotel.name}
            location={hotel.location}
            imageUrl={hotel.imageUrl}
            country={hotel.location.split(",").pop()?.trim() || ""}
            resort={hotel.location.split(",")[0]?.trim() || ""}
          />
        ))
      }
    </div>
  </div>
</section>
