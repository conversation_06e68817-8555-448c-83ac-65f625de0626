---
const guarantees = [
  {
    icon: "check-circle",
    title: "Best Price Guarantee",
    description: "Find a lower rate elsewhere? We'll match it and give you an additional 10% discount."
  },
  {
    icon: "calendar",
    title: "Flexible Cancellation",
    description: "Plans change. Many of our properties offer free cancellation up to 30 days before arrival."
  },
  {
    icon: "shield",
    title: "Secure Booking",
    description: "Your personal and payment information is protected with industry-leading encryption."
  }
];
---

<section class="py-16 bg-primary/5">
  <div class="container-custom">
    <div class="max-w-4xl mx-auto bg-background rounded-lg shadow-glow p-8 border border-border/30">
      <div class="text-center mb-8">
        <h2 class="text-2xl font-baskervville mb-4">Book With Confidence</h2>
        <p class="text-foreground/70">
          We understand that planning a luxury ski holiday is a significant investment. That's why we offer these guarantees to ensure your peace of mind.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        {guarantees.map(guarantee => (
          <div class="text-center">
            <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              {guarantee.icon === "check-circle" && (
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              )}
              {guarantee.icon === "calendar" && (
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
              )}
              {guarantee.icon === "shield" && (
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                </svg>
              )}
            </div>
            <h3 class="text-lg font-baskervville mb-2">{guarantee.title}</h3>
            <p class="text-sm text-foreground/70">{guarantee.description}</p>
          </div>
        ))}
      </div>

      <div class="mt-8 text-center">
        <a href="/contact" class="btn-primary inline-block">Contact Our Travel Experts</a>
      </div>
    </div>
  </div>
</section>
