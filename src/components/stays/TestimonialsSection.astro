---
const testimonials = [
  {
    quote:
      "<PERSON> curated the most extraordinary ski holiday for our family. The chalet was impeccable, and the personalized service exceeded all expectations.",
    author: "<PERSON>",
    location: "Stayed at Chalet Mont Blanc, Chamonix",
    image:
      "https://images.unsplash.com/photo-1566492031773-4f4e44671857?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
  },
  {
    quote:
      "The attention to detail was remarkable. From the moment we arrived, every aspect of our stay was thoughtfully arranged. We'll never book with anyone else.",
    author: "<PERSON>",
    location: "Stayed at Grand Hotel Zermatterhof, Zermatt",
    image:
      "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
  },
  {
    quote:
      "Our Perfect Piste concierge arranged impossible-to-get restaurant reservations and secured us the best ski instructor in Courchevel. Truly a five-star experience.",
    author: "<PERSON>",
    location: "Stayed at Hôtel Les Airelles, Courchevel",
    image:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
  },
];
---

<section class="py-16 bg-primary/5">
  <div class="container-custom">
    <div class="text-center mb-12">
      <p class="section-micro-headline">Guest Experiences</p>
      <h2 class="section-title">What Our Guests Say</h2>
      <p class="section-subtitle max-w-3xl mx-auto">
        Discover why discerning travelers choose Perfect Piste for their luxury
        ski holidays.
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      {
        testimonials.map((testimonial) => (
          <div class="bg-background p-8 rounded-md border border-border/30 shadow-sm relative">
            <div class="absolute -top-4 left-8 w-8 h-8 bg-[#285DA6] text-white flex items-center justify-center rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="currentColor"
                stroke="none"
              >
                <path d="M11.9999 10.4999C11.9999 9.11924 10.8806 7.99994 9.49992 7.99994H7.99992C7.44764 7.99994 6.99992 7.55223 6.99992 6.99994C6.99992 6.44766 7.44764 5.99994 7.99992 5.99994H9.49992C11.9851 5.99994 13.9999 8.01472 13.9999 10.4999V12.4999C13.9999 13.8806 12.8806 14.9999 11.4999 14.9999C10.1192 14.9999 8.99992 13.8806 8.99992 12.4999C8.99992 11.1192 10.1192 9.99994 11.4999 9.99994C11.6713 9.99994 11.8388 10.0166 11.9999 10.0483V10.4999Z" />
                <path d="M5.99992 10.4999C5.99992 9.11924 4.88062 7.99994 3.49992 7.99994H1.99992C1.44764 7.99994 0.999924 7.55223 0.999924 6.99994C0.999924 6.44766 1.44764 5.99994 1.99992 5.99994H3.49992C5.98513 5.99994 7.99992 8.01472 7.99992 10.4999V12.4999C7.99992 13.8806 6.88062 14.9999 5.49992 14.9999C4.11922 14.9999 2.99992 13.8806 2.99992 12.4999C2.99992 11.1192 4.11922 9.99994 5.49992 9.99994C5.67135 9.99994 5.83878 10.0166 5.99992 10.0483V10.4999Z" />
              </svg>
            </div>

            <p class="italic text-foreground/80 mb-6 pt-4">
              {testimonial.quote}
            </p>

            <div class="flex items-center">
              <img
                src={testimonial.image}
                alt={testimonial.author}
                class="w-12 h-12 rounded-full object-cover mr-4"
              />
              <div>
                <h4 class="font-baskervville">{testimonial.author}</h4>
                <p class="text-xs text-foreground/60">{testimonial.location}</p>
              </div>
            </div>
          </div>
        ))
      }
    </div>
  </div>
</section>
