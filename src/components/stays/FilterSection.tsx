import { useState } from 'react';

interface FilterSectionProps {
  destinations: string[];
  onFilterChange?: (filters: FilterState) => void;
}

interface FilterState {
  destination: string;
  priceRange: [number, number];
  amenities: string[];
  stars: number[];
}

const FilterSection = ({ destinations = [], onFilterChange }: FilterSectionProps) => {
  const [filters, setFilters] = useState<FilterState>({
    destination: '',
    priceRange: [0, 5000],
    amenities: [],
    stars: []
  });

  const [isExpanded, setIsExpanded] = useState(false);

  const amenitiesList = [
    'Spa',
    'Pool',
    'Restaurant',
    'Ski-in/Ski-out',
    'Fitness Center',
    'Concierge'
  ];

  const handleDestinationChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newFilters = { ...filters, destination: e.target.value };
    setFilters(newFilters);
    onFilterChange?.(newFilters);
  };

  const handleAmenityToggle = (amenity: string) => {
    const newAmenities = filters.amenities.includes(amenity)
      ? filters.amenities.filter(a => a !== amenity)
      : [...filters.amenities, amenity];
    
    const newFilters = { ...filters, amenities: newAmenities };
    setFilters(newFilters);
    onFilterChange?.(newFilters);
  };

  const handleStarToggle = (star: number) => {
    const newStars = filters.stars.includes(star)
      ? filters.stars.filter(s => s !== star)
      : [...filters.stars, star];
    
    const newFilters = { ...filters, stars: newStars };
    setFilters(newFilters);
    onFilterChange?.(newFilters);
  };

  return (
    <div className="bg-background border border-border/30 rounded-md shadow-sm">
      <div className="p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <h3 className="text-lg font-baskervville mb-4 md:mb-0">Refine Your Search</h3>
          <button 
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-sm font-karla text-primary flex items-center"
          >
            {isExpanded ? 'Show Less' : 'Show More Filters'}
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
              className={`ml-1 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
            >
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="destination" className="block text-sm font-karla text-foreground/70 mb-2">
              Destination
            </label>
            <select
              id="destination"
              value={filters.destination}
              onChange={handleDestinationChange}
              className="w-full p-3 border border-border rounded-md bg-background text-foreground focus:border-primary transition-colors"
            >
              <option value="">All Destinations</option>
              {destinations.map((destination, index) => (
                <option key={index} value={destination}>
                  {destination}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="check-in" className="block text-sm font-karla text-foreground/70 mb-2">
              Check-in Date
            </label>
            <input
              type="date"
              id="check-in"
              className="w-full p-3 border border-border rounded-md bg-background text-foreground focus:border-primary transition-colors"
            />
          </div>

          <div>
            <label htmlFor="check-out" className="block text-sm font-karla text-foreground/70 mb-2">
              Check-out Date
            </label>
            <input
              type="date"
              id="check-out"
              className="w-full p-3 border border-border rounded-md bg-background text-foreground focus:border-primary transition-colors"
            />
          </div>
        </div>

        {isExpanded && (
          <div className="mt-6 pt-6 border-t border-border/30">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-karla text-foreground/70 mb-3">Star Rating</h4>
                <div className="flex flex-wrap gap-2">
                  {[5, 4, 3].map(star => (
                    <button
                      key={star}
                      onClick={() => handleStarToggle(star)}
                      className={`px-3 py-1 text-sm rounded-md transition-colors ${
                        filters.stars.includes(star)
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-secondary/30 text-foreground/70 hover:bg-secondary/50'
                      }`}
                    >
                      {star} {star === 1 ? 'Star' : 'Stars'}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-karla text-foreground/70 mb-3">Amenities</h4>
                <div className="grid grid-cols-2 gap-2">
                  {amenitiesList.map(amenity => (
                    <div key={amenity} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`amenity-${amenity}`}
                        checked={filters.amenities.includes(amenity)}
                        onChange={() => handleAmenityToggle(amenity)}
                        className="mr-2 h-4 w-4 text-primary border-border rounded focus:ring-primary"
                      />
                      <label htmlFor={`amenity-${amenity}`} className="text-sm">
                        {amenity}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FilterSection;
