---
const features = [
  {
    icon: "star",
    title: "Handpicked Properties",
    description: "Every hotel in our collection has been personally visited and vetted by our luxury travel experts."
  },
  {
    icon: "gift",
    title: "Exclusive Benefits",
    description: "Enjoy VIP amenities, room upgrades, and special experiences available only to Perfect Piste guests."
  },
  {
    icon: "shield",
    title: "Best Rate Guarantee",
    description: "We promise you'll always get the best available rate when booking through Perfect Piste."
  },
  {
    icon: "users",
    title: "Dedicated Concierge",
    description: "Our 24/7 concierge team is available to assist with any request before, during, and after your stay."
  }
];
---

<section class="py-16 bg-secondary/10">
  <div class="container-custom">
    <div class="text-center mb-12">
      <p class="section-micro-headline">Why Choose Perfect Piste</p>
      <h2 class="section-title">Unparalleled Luxury Experience</h2>
      <p class="section-subtitle max-w-3xl mx-auto">
        We go beyond booking accommodations to create extraordinary travel experiences tailored to your preferences.
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      {features.map(feature => (
        <div class="bg-background p-6 rounded-md border border-border/30 shadow-sm hover:shadow-glow transition-all duration-300">
          <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
            {feature.icon === "star" && (
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
            )}
            {feature.icon === "gift" && (
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                <polyline points="20 12 20 22 4 22 4 12"></polyline>
                <rect x="2" y="7" width="20" height="5"></rect>
                <line x1="12" y1="22" x2="12" y2="7"></line>
                <path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"></path>
                <path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"></path>
              </svg>
            )}
            {feature.icon === "shield" && (
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
              </svg>
            )}
            {feature.icon === "users" && (
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
            )}
          </div>
          <h3 class="text-lg font-baskervville mb-2">{feature.title}</h3>
          <p class="text-foreground/70 text-sm">{feature.description}</p>
        </div>
      ))}
    </div>
  </div>
</section>
