import React from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "./dialog";

interface CancellationPolicy {
  id: string;
  name: string;
  description: string;
  days_before_checkin: number;
  refund_type: string;
  refund_amount: number;
}

interface PoliciesModalProps {
  isOpen: boolean;
  onClose: () => void;
  policies: CancellationPolicy[];
}

const PoliciesModal: React.FC<PoliciesModalProps> = ({
  isOpen,
  onClose,
  policies,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90vw] max-w-[700px] max-h-[90vh] z-[1200] bg-gradient-to-br from-white via-white to-blue-50/30 border-0 shadow-2xl backdrop-blur-sm">
        {/* Compact Header */}
        <DialogHeader className="relative pb-4 border-b border-gradient-to-r from-transparent via-[#285DA6]/20 to-transparent">
          <div className="flex items-center justify-center mb-3">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#285DA6] to-[#1e4a8c] flex items-center justify-center shadow-lg">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-white"
              >
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
              </svg>
            </div>
          </div>
          <DialogTitle className="text-xl sm:text-2xl font-baskervville text-center bg-gradient-to-r from-[#285DA6] to-[#1e4a8c] bg-clip-text text-transparent">
            Hotel Policies
          </DialogTitle>
        </DialogHeader>

        {/* Compact Content Area */}
        <div className="mt-4 space-y-3 max-h-[70vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-[#285DA6]/20 scrollbar-track-transparent">
          {policies && policies.length > 0 ? (
            policies.map((policy, index) => (
              <div
                key={index}
                className="group relative bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-[#285DA6]/10 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.01] hover:bg-white/90"
              >
                {/* Compact Policy Header */}
                <div className="flex items-center mb-3">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-[#285DA6] to-[#1e4a8c] mr-3 shadow-sm"></div>
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900 group-hover:text-[#285DA6] transition-colors">
                    {policy.name}
                  </h3>
                </div>

                {/* Compact Description */}
                <p className="text-gray-700 mb-3 text-sm leading-relaxed">
                  {policy.description}
                </p>

                {/* Compact Policy Details */}
                <div className="flex flex-wrap gap-2">
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg px-3 py-2 border border-blue-100/50 shadow-sm">
                    <div className="flex items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="12"
                        height="12"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-[#285DA6] mr-1"
                      >
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                      </svg>
                      <span className="text-xs font-medium text-gray-900">
                        {policy.days_before_checkin} days before
                      </span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-lg px-3 py-2 border border-emerald-100/50 shadow-sm">
                    <div className="flex items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="12"
                        height="12"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-emerald-600 mr-1"
                      >
                        <path d="M12 2v20m8-10H4"></path>
                      </svg>
                      <span className="text-xs font-medium text-gray-900 capitalize">
                        {policy.refund_type.replace("_", " ")}
                      </span>
                    </div>
                  </div>

                  {policy.refund_type === "percentage" && (
                    <div className="bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg px-3 py-2 border border-amber-100/50 shadow-sm">
                      <div className="flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="12"
                          height="12"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-amber-600 mr-1"
                        >
                          <line x1="12" y1="1" x2="12" y2="23"></line>
                          <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                        </svg>
                        <span className="text-xs font-medium text-gray-900">
                          {policy.refund_amount}% refund
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-gray-400"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
              </div>
              <p className="text-gray-500 text-sm">
                No policies available at this time.
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PoliciesModal;
