---
interface Props {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
}

const { currentPage, totalPages, baseUrl } = Astro.props;

// Generate an array of page numbers to display
const getPageNumbers = () => {
  const pageNumbers = [];
  const maxPagesToShow = 5; // Show at most 5 page numbers

  if (totalPages <= maxPagesToShow) {
    // If we have 5 or fewer pages, show all of them
    for (let i = 1; i <= totalPages; i++) {
      pageNumbers.push(i);
    }
  } else {
    // Always include first page
    pageNumbers.push(1);

    // Calculate start and end of page numbers to show
    let start = Math.max(2, currentPage - 1);
    let end = Math.min(totalPages - 1, currentPage + 1);

    // Adjust if we're at the beginning
    if (currentPage <= 2) {
      end = Math.min(totalPages - 1, 4);
    }

    // Adjust if we're at the end
    if (currentPage >= totalPages - 1) {
      start = Math.max(2, totalPages - 3);
    }

    // Add ellipsis if needed before middle pages
    if (start > 2) {
      pageNumbers.push("...");
    }

    // Add middle pages
    for (let i = start; i <= end; i++) {
      pageNumbers.push(i);
    }

    // Add ellipsis if needed after middle pages
    if (end < totalPages - 1) {
      pageNumbers.push("...");
    }

    // Always include last page
    pageNumbers.push(totalPages);
  }

  return pageNumbers;
};

const pageNumbers = getPageNumbers();
---

<div class="flex flex-col items-center mt-12 mb-12">
  <div class="flex justify-center items-center space-x-2">
    <!-- Previous Page Button -->
    {
      currentPage > 1 ? (
        <a
          href={`${baseUrl}?page=${currentPage - 1}`}
          class="flex items-center justify-center w-10 h-10 rounded-md border border-[#285DA6]/30 text-[#285DA6] hover:bg-[#285DA6]/5 hover:border-[#285DA6]/50 transition-colors"
          aria-label="Previous page"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polyline points="15 18 9 12 15 6" />
          </svg>
        </a>
      ) : (
        <span
          class="flex items-center justify-center w-10 h-10 rounded-md border border-border/10 text-foreground/30 cursor-not-allowed"
          aria-label="Previous page (disabled)"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polyline points="15 18 9 12 15 6" />
          </svg>
        </span>
      )
    }

    <!-- Page Numbers -->
    {
      pageNumbers.map((pageNumber) => {
        if (pageNumber === "...") {
          return (
            <span class="flex items-center justify-center w-10 h-10 text-foreground/50 font-karla">
              ...
            </span>
          );
        }

        return pageNumber === currentPage ? (
          <span
            class="flex items-center justify-center w-10 h-10 rounded-md bg-[#285DA6] text-white font-karla font-medium"
            aria-current="page"
          >
            {pageNumber}
          </span>
        ) : (
          <a
            href={`${baseUrl}?page=${pageNumber}`}
            class="flex items-center justify-center w-10 h-10 rounded-md border border-[#285DA6]/30 text-[#285DA6] hover:bg-[#285DA6]/5 hover:border-[#285DA6]/50 transition-colors font-karla"
          >
            {pageNumber}
          </a>
        );
      })
    }

    <!-- Next Page Button -->
    {
      currentPage < totalPages ? (
        <a
          href={`${baseUrl}?page=${currentPage + 1}`}
          class="flex items-center justify-center w-10 h-10 rounded-md border border-[#285DA6]/30 text-[#285DA6] hover:bg-[#285DA6]/5 hover:border-[#285DA6]/50 transition-colors"
          aria-label="Next page"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polyline points="9 18 15 12 9 6" />
          </svg>
        </a>
      ) : (
        <span
          class="flex items-center justify-center w-10 h-10 rounded-md border border-border/10 text-foreground/30 cursor-not-allowed"
          aria-label="Next page (disabled)"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polyline points="9 18 15 12 9 6" />
          </svg>
        </span>
      )
    }
  </div>
</div>
