---
export interface Props {
  href: string;
  variant?: "primary" | "secondary" | "outline";
  size?: "sm" | "md" | "lg";
  class?: string;
  withArrow?: boolean;
  arrowVisible?: boolean;
}

const {
  href,
  variant = "primary",
  size = "md",
  class: className = "",
  withArrow = false,
  arrowVisible = true,
} = Astro.props;

// Base classes
const baseClasses =
  "inline-flex items-center justify-center font-karla font-bold text-xs uppercase tracking-wider transition-all duration-300 border-2";

// Size classes
const sizeClasses = {
  sm: "py-2 px-3 min-w-[140px]",
  md: "py-3 px-4 min-w-[180px]",
  lg: "py-4 px-6 min-w-[220px]",
};

// Variant classes
const variantClasses = {
  primary:
    "bg-primary text-primary-foreground border-primary hover:bg-primary/90 hover:shadow-md",
  secondary:
    "bg-white text-primary border-white hover:bg-white/90 hover:shadow-glow-soft",
  outline:
    "bg-transparent text-white border-white hover:bg-white/20 hover:text-white hover:shadow-glow-soft",
};

// Combine all classes
const classes = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className} group`;
---

<a href={href} class={classes}>
  <span class="flex items-center justify-center whitespace-nowrap px-2">
    <slot />
    {
      withArrow && (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class={`ml-2 transition-all duration-300 ${
            arrowVisible
              ? "transform group-hover:translate-x-1"
              : "opacity-0 group-hover:opacity-100"
          } ${variant === "outline" ? "text-white" : ""}`}
        >
          <line x1="5" y1="12" x2="19" y2="12" />
          <polyline points="12 5 19 12 12 19" />
        </svg>
      )
    }
  </span>
</a>
