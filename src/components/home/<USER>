import { useState, useEffect, useRef } from "react";
import "../../styles/magical-experience.css";

interface SlideProps {
  image: string;
  title: string;
  subtitle: string;
  tagline?: string;
}

const slides: SlideProps[] = [
  {
    image:
      "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
    title: "Escape Ordinary",
    subtitle: "Your Journey to the World's Finest Stays Begins Here.",
    tagline: "Experience the world's most exclusive properties",
  },
  {
    image:
      "https://images.unsplash.com/photo-1600011689032-8b628b8a8747?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
    title: "Alpine Retreats",
    subtitle: "Discover serene mountain escapes in Switzerland",
    tagline: "Where luxury meets pristine nature",
  },
  {
    image:
      "https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
    title: "Coastal Havens",
    subtitle: "Unwind in oceanfront villas with breathtaking views",
    tagline: "The finest seaside retreats await",
  },
];

const FeaturedSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  // State for tracking animation states
  const typingRef = useRef<number | null>(null);
  const buttonAnimationRef = useRef<number | null>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % slides.length);
    }, 8000);

    return () => {
      clearInterval(interval);
      // Clean up any typing or animation intervals/timeouts
      if (typingRef.current) clearTimeout(typingRef.current);
      if (buttonAnimationRef.current) clearTimeout(buttonAnimationRef.current);

      // Clean up any global functions
      // @ts-ignore
      if (window.cleanupMagicalExperience) {
        // @ts-ignore
        window.cleanupMagicalExperience();
        // @ts-ignore
        delete window.cleanupMagicalExperience;
      }
    };
  }, []);

  // Function to handle the magical experience
  const handleFindPerfectPiste = () => {
    // Try multiple approaches to toggle the concierge mode
    // 1. Dispatch a custom event
    document.dispatchEvent(
      new CustomEvent("toggle-concierge", {
        detail: { toggleConcierge: true },
      })
    );

    // 2. Try to use the global function if available
    // @ts-ignore
    if (window.toggleConciergeMode) {
      // @ts-ignore
      window.toggleConciergeMode();
    }
    // 3. Try using the direct setter if available
    // @ts-ignore
    else if (window.setIsAIMode) {
      // @ts-ignore
      window.setIsAIMode(true);
    } else {
      // 4. Try direct DOM manipulation as a fallback
      const conciergeToggle = document.getElementById(
        "concierge-mode"
      ) as HTMLInputElement;
      if (conciergeToggle) {
        conciergeToggle.checked = true;

        // Try to trigger change handlers
        conciergeToggle.dispatchEvent(new Event("change", { bubbles: true }));
        conciergeToggle.dispatchEvent(new Event("click", { bubbles: true }));
      }
    }

    // Add a longer delay to ensure the concierge component is fully rendered
    const timeoutId = setTimeout(() => {
      // Add a cleanup function to the window object to handle any errors or unmounting
      // @ts-ignore
      window.cleanupMagicalExperience = () => {
        clearTimeout(timeoutId);

        // Find and clean up any typing classes
        const conciergeInput = document.getElementById(
          "concierge-question"
        ) as HTMLInputElement;
        if (conciergeInput) {
          conciergeInput.classList.remove("typing");
        }

        // Clean up any ongoing animations
        if (typingRef.current) {
          clearTimeout(typingRef.current);
          typingRef.current = null;
        }

        if (buttonAnimationRef.current) {
          clearTimeout(buttonAnimationRef.current);
          buttonAnimationRef.current = null;
        }
      };

      // Set a timeout to clean up after 10 seconds in case something goes wrong
      setTimeout(() => {
        // @ts-ignore
        if (window.cleanupMagicalExperience) {
          // @ts-ignore
          window.cleanupMagicalExperience();
          // @ts-ignore
          delete window.cleanupMagicalExperience;
        }
      }, 10000);
      // Find the concierge input field
      const conciergeInput = document.getElementById(
        "concierge-question"
      ) as HTMLInputElement;

      if (conciergeInput) {
        // Focus the input
        conciergeInput.focus();

        // Clear any existing text
        conciergeInput.value = "";

        // Array of different phrases to make it feel more dynamic
        const phrasesToType = [
          "Find your perfect experience",
          "Looking for a luxury ski resort",
          "Recommend a beginner-friendly ski destination",
          "Show me family-friendly ski resorts",
          "Find luxury chalets with mountain views",
          "Suggest a romantic ski getaway",
          "Looking for ski resorts with best powder",
          "Find ski destinations with great après-ski",
          "Where can I learn to ski in Switzerland",
          "Best ski resorts for advanced skiers",
          "Luxury ski-in ski-out accommodations",
          "Ski resorts with best snow conditions",
          "Recommend a ski resort with spa facilities",
          "Find ski destinations for Christmas holiday",
          "Ski resorts with best off-piste options",
        ];

        // Randomly select one of the phrases
        const textToType =
          phrasesToType[Math.floor(Math.random() * phrasesToType.length)];

        let currentIndex = 0;

        // Clear any existing typing timeout
        if (typingRef.current) {
          clearTimeout(typingRef.current);
        }

        // Function to get a slightly random typing speed for more natural effect
        const getTypingSpeed = () => {
          // Base speed of 80ms with a random variation of +/- 40ms
          return Math.floor(Math.random() * 80) + 40;
        };

        // Add typing class to show cursor
        conciergeInput.classList.add("typing");

        // Function to type the next character
        const typeNextChar = () => {
          if (currentIndex < textToType.length) {
            conciergeInput.value = textToType.substring(0, currentIndex + 1);
            // Trigger an input event to update React state
            conciergeInput.dispatchEvent(new Event("input", { bubbles: true }));
            currentIndex++;

            // Schedule the next character with a slightly random delay
            typingRef.current = window.setTimeout(
              typeNextChar,
              getTypingSpeed()
            );
          } else {
            // Typing finished
            if (typingRef.current) {
              clearTimeout(typingRef.current);
              typingRef.current = null;
            }

            // Remove typing class when done
            conciergeInput.classList.remove("typing");

            // Animate the Ask button
            const askButton = document.querySelector(
              ".search-button-premium"
            ) as HTMLButtonElement;
            if (askButton) {
              askButton.classList.add("animate-pulse");

              // Clear animation after a few seconds
              buttonAnimationRef.current = window.setTimeout(() => {
                askButton.classList.remove("animate-pulse");
              }, 3000);
            }
          }
        };

        // Start the typing effect
        typingRef.current = window.setTimeout(typeNextChar, 500); // Initial delay before typing starts
      }
    }, 800); // Longer delay to ensure component is fully rendered
  };

  return (
    <div className="container-custom w-full pt-0 pb-4 overflow-hidden rounded-lg">
      <div className="max-w-[1440px] mx-auto relative">
        <div className="relative">
          {slides.map((slide, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${
                index === currentSlide ? "opacity-100 z-10" : "opacity-0 z-0"
              }`}
              aria-hidden={index !== currentSlide}
            >
              <div className="relative">
                <div className="max-w-[1440px] mx-auto relative rounded-lg overflow-hidden">
                  <div className="aspect-[3/4] sm:aspect-[4/3] md:aspect-[16/9] overflow-hidden shadow-md rounded-lg">
                    <img
                      src={slide.image}
                      alt={slide.title}
                      className="w-full h-full object-cover transition-transform duration-1000 rounded-lg"
                    />
                  </div>

                  <div className="absolute inset-0 bg-black/40 flex items-center justify-center rounded-lg">
                    <div className="text-center max-w-xs sm:max-w-md md:max-w-2xl px-4 z-10">
                      {slide.tagline && (
                        <p className="text-xs sm:text-sm uppercase tracking-widest text-white/90 mb-2 sm:mb-3">
                          {slide.tagline}
                        </p>
                      )}

                      <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl text-white font-baskervville mb-2 sm:mb-4 leading-tight">
                        {slide.title}
                      </h1>

                      <p className="text-xs sm:text-sm md:text-base lg:text-xl text-white/80 max-w-2xl font-light tracking-wide mb-6">
                        {slide.subtitle}
                      </p>

                      <button
                        onClick={handleFindPerfectPiste}
                        className="inline-flex items-center justify-center px-6 py-3 rounded-full text-white text-sm font-karla font-bold uppercase tracking-wider premium-cta-button"
                        aria-label="Find your perfect piste"
                      >
                        Find your perfect piste
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          <div className="relative" aria-hidden="true">
            <div className="max-w-[1440px] mx-auto rounded-lg overflow-hidden">
              <div className="aspect-[3/4] sm:aspect-[4/3] md:aspect-[16/9] overflow-hidden shadow-md rounded-lg"></div>
            </div>
          </div>
        </div>

        <div className="flex justify-center mt-2 sm:mt-3 relative z-20">
          <div className="inline-flex space-x-2 sm:space-x-3 md:space-x-4 py-2 sm:py-3 px-4 sm:px-6 bg-background/90 backdrop-blur-sm rounded-full shadow-md">
            {slides.map((_, index) => (
              <button
                key={index}
                className={`w-2 sm:w-2.5 md:w-3 h-2 sm:h-2.5 md:h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? "bg-primary w-4 sm:w-5 md:w-6 h-2 sm:h-2.5 md:h-3"
                    : "bg-foreground opacity-30"
                }`}
                onClick={() => setCurrentSlide(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturedSlider;
