---
import SectionIntroWithMicro from "./SectionIntroWithMicro.tsx";
---

<section
  class="py-20"
  style="background-color: color-mix(in srgb, var(--accent) 30%, transparent);"
>
  <div class="container-custom">
    <SectionIntroWithMicro
      microHeadline="Trusted by the Best in the Industry"
      title="Our Distinguished Collaborators"
      poeticLine="Excellence recognizes excellence - our partners share our commitment to the extraordinary"
      subtitle="We partner with the world's most prestigious hospitality brands and travel experts to ensure an unparalleled experience for our clients."
      client:visible
    />

    <div class="flex flex-wrap justify-center items-center gap-12 mt-16">
      <div
        class="grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300"
      >
        <img
          src="https://via.placeholder.com/180x80?text=Luxury+Brand"
          alt="Luxury Brand Partner"
          class="h-12"
        />
      </div>
      <div
        class="grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300"
      >
        <img
          src="https://via.placeholder.com/180x80?text=Premium+Hotels"
          alt="Premium Hotels Partner"
          class="h-12"
        />
      </div>
      <div
        class="grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300"
      >
        <img
          src="https://via.placeholder.com/180x80?text=Travel+Experts"
          alt="Travel Experts Partner"
          class="h-12"
        />
      </div>
      <div
        class="grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300"
      >
        <img
          src="https://via.placeholder.com/180x80?text=Alpine+Resorts"
          alt="Alpine Resorts Partner"
          class="h-12"
        />
      </div>
      <div
        class="grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300"
      >
        <img
          src="https://via.placeholder.com/180x80?text=Luxury+Transport"
          alt="Luxury Transport Partner"
          class="h-12"
        />
      </div>
    </div>
  </div>
</section>
