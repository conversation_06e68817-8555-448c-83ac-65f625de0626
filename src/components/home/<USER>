---
interface Props {
  title?: string;
  description?: string;
  ctaText?: string;
  ctaLink?: string;
  backgroundImage?: string;
}

const {
  title = "Contact Us",
  description = "At Perfect Piste, our mission is helping you find your perfect stay.",
  ctaText = "CONTACT US",
  ctaLink = "/contact",
  backgroundImage = "https://plus.unsplash.com/premium_photo-1670190799349-431dabadc489?q=80&w=2576&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
} = Astro.props;
---

<div class="container-custom">
  <section class="relative h-[50vh] sm:h-[60vh] overflow-hidden rounded-md">
    <img
      src={backgroundImage}
      alt="Snow covered mountain range during daytime"
      class="w-full h-full object-cover"
    />
    <div class="absolute inset-0 bg-black/40 flex items-center">
      <div class="container-custom">
        <div class="max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-3xl">
          <h2
            class="text-2xl sm:text-3xl md:text-4xl font-baskervville mb-2 sm:mb-4 text-white"
          >
            {title}
          </h2>
          <p class="text-base sm:text-lg md:text-xl text-white/90 mb-4 sm:mb-6">
            {description}
          </p>
          <div class="mt-4 sm:mt-6 md:mt-8">
            <a
              href={ctaLink}
              class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
            >
              {ctaText}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-2"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
