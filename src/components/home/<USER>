import { useState, useEffect, useRef } from "react";
import "../../styles/web-stories.css";

interface StoryProps {
  id: string;
  title: string;
  subtitle: string;
  imageUrl: string;
  link: string;
}

// Sample stories data
const stories: StoryProps[] = [
  {
    id: "story1",
    title: "Alpine Luxury",
    subtitle: "Discover the most exclusive chalets in the Swiss Alps",
    imageUrl:
      "https://images.unsplash.com/photo-1548777123-e216912df7d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    link: "/destinations/swiss-alps",
  },
  {
    id: "story2",
    title: "Après-Ski Elegance",
    subtitle: "The finest dining experiences after a day on the slopes",
    imageUrl:
      "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    link: "/experiences/dining",
  },
  {
    id: "story3",
    title: "Private Powder",
    subtitle: "Exclusive access to untouched slopes and hidden gems",
    imageUrl:
      "https://images.unsplash.com/photo-1551524559-8af4e6624178?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    link: "/experiences/private-skiing",
  },
  {
    id: "story4",
    title: "Mountain Wellness",
    subtitle: "Rejuvenate with premium spa treatments in alpine retreats",
    imageUrl:
      "https://images.unsplash.com/photo-1540555700478-4be289fbecef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    link: "/experiences/wellness",
  },
  {
    id: "story5",
    title: "Winter Wonderland",
    subtitle: "Magical experiences beyond the slopes for the whole family",
    imageUrl:
      "https://images.unsplash.com/photo-1610995195985-7d1e76c43fd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    link: "/experiences/family",
  },
];

const WebStories = () => {
  const [activeStory, setActiveStory] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const storyRefs = useRef<(HTMLDivElement | null)[]>([]);
  const touchStartX = useRef<number | null>(null);
  const autoPlayRef = useRef<number | null>(null);

  // Initialize story refs
  useEffect(() => {
    storyRefs.current = storyRefs.current.slice(0, stories.length);
  }, []);

  // Auto-play functionality
  useEffect(() => {
    const startAutoPlay = () => {
      autoPlayRef.current = window.setTimeout(() => {
        if (!isTransitioning) {
          handleNextStory();
        }
      }, 6000); // Change story every 6 seconds
    };

    startAutoPlay();

    return () => {
      if (autoPlayRef.current) {
        clearTimeout(autoPlayRef.current);
      }
    };
  }, [activeStory, isTransitioning]);

  // Handle story navigation
  const handleStoryChange = (index: number) => {
    if (index === activeStory || isTransitioning) return;

    setIsTransitioning(true);
    setActiveStory(index);

    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 600);

    // Reset auto-play timer
    if (autoPlayRef.current) {
      clearTimeout(autoPlayRef.current);
    }
  };

  const handleNextStory = () => {
    const nextIndex = (activeStory + 1) % stories.length;
    handleStoryChange(nextIndex);
  };

  const handlePrevStory = () => {
    const prevIndex = (activeStory - 1 + stories.length) % stories.length;
    handleStoryChange(prevIndex);
  };

  // Touch event handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (touchStartX.current === null) return;

    const touchEndX = e.changedTouches[0].clientX;
    const diff = touchStartX.current - touchEndX;

    // Swipe threshold
    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        handleNextStory();
      } else {
        handlePrevStory();
      }
    }

    touchStartX.current = null;
  };

  return (
    <div className="web-stories-container">
      <div className="web-stories-header">
        <div className="web-stories-nav-container">
          <button
            className="web-stories-nav-button"
            onClick={handlePrevStory}
            aria-label="Previous story"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>
          <button
            className="web-stories-nav-button"
            onClick={handleNextStory}
            aria-label="Next story"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
        </div>
      </div>

      <div
        className="web-stories-track"
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
      >
        {stories.map((story, index) => (
          <div
            key={story.id}
            ref={(el) => (storyRefs.current[index] = el)}
            className={`web-story ${
              index === activeStory ? "web-story-active" : ""
            }`}
            style={{
              transform: `translateX(${(index - activeStory) * 100}%)`,
              opacity: index === activeStory ? 1 : 0.5,
              zIndex: stories.length - Math.abs(index - activeStory),
            }}
          >
            <a href={story.link} className="web-story-link">
              <div className="web-story-image-container">
                <img
                  src={story.imageUrl}
                  alt={story.title}
                  className="web-story-image"
                />
                <div className="web-story-gradient-overlay"></div>
              </div>
              <div className="web-story-content">
                <h3 className="web-story-content-title">{story.title}</h3>
                <p className="web-story-content-subtitle">{story.subtitle}</p>
              </div>
            </a>
          </div>
        ))}
      </div>

      <div className="web-stories-indicators">
        {stories.map((_, index) => (
          <button
            key={index}
            className={`web-stories-indicator ${
              index === activeStory ? "web-stories-indicator-active" : ""
            }`}
            onClick={() => handleStoryChange(index)}
            aria-label={`Go to story ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default WebStories;
