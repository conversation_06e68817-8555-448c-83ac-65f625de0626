---
import DestinationCard from "../destinations/DestinationCard.astro";

// Define the ski categories
const categories = [
  {
    id: "beginners",
    name: "Beginners",
    category: "Perfect for Beginners",
    propertyCount: 12,
    imageUrl:
      "https://wallpaperbat.com/img/270245-image-men-helmet-sun-sports-jacket-winter-snow-skiing-1920x1080.jpg",
    description:
      "Ideal for first-timers and early learners, these destinations offer wide, gentle slopes, excellent ski schools, and a high number of blue runs. Well- groomed pistes and patient instructors make your first ski experience smooth and confidence-building.",
    searchPrompt:
      "I'm looking for ski resorts perfect for beginners with gentle slopes, excellent ski schools, and well-groomed pistes",
  },
  {
    id: "beginner-family",
    name: "Beginner with Families",
    category: "Perfect for Beginner with Families",
    propertyCount: 15,
    imageUrl:
      "https://wallpaperbat.com/img/136977-skiing-hd-wallpaper-and-background-image.jpg",
    description:
      "Ideal for families with beginner skiers, these properties offer gentle slopes, top ski schools, nearby childcare, and family-friendly activities—creating the perfect setting for a smooth, confidence-building start on the snow.",
    searchPrompt:
      "I need family-friendly ski resorts with gentle slopes, good ski schools, and childcare facilities for beginner skiers",
  },
  {
    id: "off-piste",
    name: "Off Piste",
    category: "Perfect for Off-Piste Ski",
    propertyCount: 10,
    imageUrl: "https://wallpaperbat.com/img/49481-snow-skiing-wallpaper.jpg",
    description:
      "Ideal for adventurous skiers, these luxury properties offer access to world-class off-piste terrain, guided backcountry experiences, and pristine powder - followed by indulgent comfort, fine dining, and serene mountain views at day's end.",
    searchPrompt:
      "I want luxury accommodations with access to world-class off-piste terrain and guided backcountry experiences",
  },
  {
    id: "family-friendly",
    name: "Family Friendly",
    category: "Perfect for a Group of Mixed Ability",
    propertyCount: 18,
    imageUrl:
      "https://images.unsplash.com/photo-1535640368727-187c8a674b5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    description:
      "Ideal for mixed-ability groups, these properties offer varied terrain for all levels, vibrant après-ski, and flexible, social spaces - making it easy to ski your way by day and come together in style by night.",
    searchPrompt:
      "I need ski resorts suitable for a group with mixed skiing abilities from beginners to advanced skiers",
  },
  {
    id: "party",
    name: "Party Lovers",
    category: "Perfect for Après-Ski and Party Lovers",
    propertyCount: 8,
    imageUrl:
      "https://wallpaperbat.com/img/24512-skiing-wallpaper-top-free-skiing-background.jpg",
    description:
      "Ideal for those who live for the après, these properties sit in lively resorts with bars, clubs, and mountain energy - offering great skiing by day and unforgettable parties just steps from your door.",
    searchPrompt:
      "I want luxury ski resorts with the best après-ski scene, vibrant nightlife, and great bars and restaurants",
  },
  {
    id: "budget-friendly",
    name: "Perfect for Luxury Retreats",
    category: "Perfect for Luxury Retreats",
    propertyCount: 14,
    imageUrl:
      "https://images.unsplash.com/photo-1520364311437-283acc84fe43?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    description:
      "Experience indulgent luxury in the mountains with five-star hotels, exclusive spa days, gourmet dining, and champagne at altitude—perfect for those seeking elegance, comfort, and breathtaking alpine views off the slopes.",
    searchPrompt:
      "I'm looking for luxurious ski retreats with upscale amenities, spa experiences, and gourmet dining in a serene alpine setting",
  },
  {
    id: "multigenerational",
    name: "Perfect for Multigenerational Ski Holidays",
    category: "Perfect for Multigenerational Ski Holidays",
    propertyCount: 14,
    imageUrl:
      "https://wallpaperbat.com/img/341871-wallpaper-of-skiing-snow-winter-sport-background-hd-image.jpg",
    description:
      "Destinations that cater to kids, adults, and grandparents alike—these resorts feature gentle slopes, fun parks, convenient access, and spacious accommodations to ensure a comfortable and enjoyable experience for every generation.",
    searchPrompt:
      "I need ski resorts suitable for all generations, with easy slopes, family-friendly amenities, and group accommodations",
  },
  {
    id: "couples",
    name: "Perfect for Couples Ski Breaks",
    category: "Perfect for Couples Ski Breaks",
    propertyCount: 14,
    imageUrl:
      "https://wallpaperbat.com/img/166824-ski-jacket-brands-skiing-wallpaper.jpg",
    description:
      "Peaceful resorts with scenic trails, spa hotels, and intimate dining set the scene for romantic getaways—ideal for quality time, cozy accommodations, and unforgettable snowy memories together.",
    searchPrompt:
      "I'm searching for romantic ski resorts with cozy lodges, scenic runs, and spa amenities for a couples retreat",
  },
  {
    id: "late-season",
    name: "Perfect for Late Season Skiing",
    category: "Perfect for Late Season Skiing",
    propertyCount: 14,
    imageUrl: "https://wallpaperbat.com/img/146426-ski-resort-wallpaper.jpg",
    description:
      "High-altitude and glacier resorts with excellent snow in March and April—perfect for skiers chasing spring sunshine, reliable conditions, and quieter slopes late in the season.",
    searchPrompt:
      "I'm looking for snow-sure ski resorts with great late-season conditions in March and April",
  },
  {
    id: "advanced",
    name: "Perfect for Advanced Skiers",
    category: "Perfect for Advanced Skiers",
    propertyCount: 14,
    imageUrl:
      "https://wallpaperbat.com/img/292211-hd-wallpaper-person-skiing-on-snow-with-gear-set-person-doing.jpg",
    description:
      "Challenging black runs, steep descents, and technical terrain define these resorts—crafted for expert skiers seeking adrenaline, precision, and the ultimate test on the slopes.",
    searchPrompt:
      "I want advanced ski resorts with steep runs, technical terrain, and thrilling off-piste options",
  },
];
---

<section>
  <div class="container-custom">
    <div class="flex flex-col sm:flex-row justify-between items-start">
      <div>
        <span class="text-sm uppercase tracking-wider text-[#285DA6] font-karla"
          >WHAT WE DO</span
        >
        <h2
          class="text-2xl sm:text-[28px] md:text-[32px] font-baskervville uppercase tracking-[0.1em] mt-2 sm:whitespace-nowrap"
        >
          EXPERTLY MATCHING YOU TO YOUR PERFECT PISTE
        </h2>
        <p class="font-baskervville text-sm sm:text-base mt-2 max-w-2xl">
          Our smart AAI pairs your preferences with handpicked properties and
          local insight to find your ideal mountain escape—quickly and
          personally.
        </p>
      </div>
      <a
        href="/destinations"
        class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-[#285DA6] border-b border-[#285DA6] pb-1 transition-all hover:border-transparent mt-4 sm:mt-1"
      >
        Search now
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2"
        >
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </a>
    </div>

    <style>
      h2 {
        font-size: clamp(1.5rem, 1.8vw, 1.8rem);
        overflow: visible;
        max-width: 100%;
      }

      @media (max-width: 1200px) {
        h2 {
          font-size: clamp(1.2rem, 1.4vw, 1.4rem);
          letter-spacing: 0.08em;
        }
      }

      /* Carousel Styles */
      .carousel-container {
        position: relative;
        overflow: hidden;
        width: 100%;
      }

      .carousel-track {
        display: flex;
        transition: transform 0.5s ease-in-out;
        width: 100%;
      }

      .carousel-item {
        flex: 0 0 100%;
        padding: 0 0.5rem;
        box-sizing: border-box;
      }

      .carousel-controls {
        display: flex;
        justify-content: center;
        margin-top: 1.5rem;
        gap: 1rem;
      }

      .carousel-indicators {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        margin-top: 1rem;
      }

      .carousel-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.2);
        cursor: pointer;
        transition: background-color 0.3s ease;
      }

      .carousel-indicator.active {
        background-color: #285da6;
      }

      @media (min-width: 768px) {
        .carousel-item {
          flex: 0 0 33.333%;
        }
      }
    </style>

    <!-- Carousel Container -->
    <div class="carousel-container mt-6 sm:mt-8">
      <div class="carousel-track" id="category-track">
        {
          categories.map((category) => (
            <div class="carousel-item px-2">
              <DestinationCard
                id={category.id}
                name={category.name}
                propertyCount={category.propertyCount}
                imageUrl={category.imageUrl}
                category={category.category}
                description={category.description}
                searchPrompt={category.searchPrompt}
              />
            </div>
          ))
        }
      </div>

      <!-- Carousel Controls -->
      <div class="carousel-controls mt-4">
        <button
          class="carousel-prev bg-white/80 backdrop-blur-sm hover:bg-white rounded-full p-3 shadow-md transition-all duration-300 mr-4 mb-1"
          aria-label="Previous slide"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M15 18l-6-6 6-6"></path>
          </svg>
        </button>
        <button
          class="carousel-next bg-white/80 backdrop-blur-sm hover:bg-white rounded-full p-3 shadow-md transition-all duration-300 mb-1"
          aria-label="Next slide"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M9 18l6-6-6-6"></path>
          </svg>
        </button>
      </div>

      <!-- Carousel Indicators -->
      <!-- <div class="carousel-indicators mt-4">
        {
          Array.from({ length: categories.length }, (_, i) => (
            <button
              class={`carousel-indicator ${i === 0 ? "active" : ""}`}
              data-index={i}
              aria-label={`Go to slide ${i + 1}`}
            />
          ))
        }
      </div> -->
    </div>

    <script>
      // Client-side carousel functionality
      document.addEventListener("DOMContentLoaded", () => {
        const track = document.getElementById("category-track");
        const items = track?.querySelectorAll(".carousel-item");
        const prevBtn = document.querySelector(".carousel-prev");
        const nextBtn = document.querySelector(".carousel-next");
        const indicators = document.querySelectorAll(".carousel-indicator");
        let currentIndex = 0;
        const itemCount = items?.length || 0;

        // Function to get current items per view based on screen width
        const getItemsPerView = () => (window.innerWidth >= 768 ? 3 : 1);

        // Function to calculate max index based on current view
        const getMaxIndex = () => {
          const itemsPerView = getItemsPerView();
          return Math.max(0, itemCount - itemsPerView);
        };

        // Function to update the carousel position
        const updateCarousel = (index: number) => {
          if (!track || !items) return;

          // Ensure index is within bounds
          const maxIndex = getMaxIndex();
          currentIndex = Math.max(0, Math.min(index, maxIndex));

          // Calculate the translation percentage based on item width
          const itemWidth = 100 / getItemsPerView();
          track.style.transform = `translateX(-${currentIndex * itemWidth}%)`;

          // Update indicators - highlight the indicator that corresponds to the visible items
          indicators.forEach((indicator, i) => {
            // For desktop (3 items per view), we want to highlight the indicator for each group of 3
            // For mobile (1 item per view), we highlight the indicator for each item
            if (i === currentIndex) {
              indicator.classList.add("active");
            } else {
              indicator.classList.remove("active");
            }
          });
        };

        // Event listeners for prev/next buttons
        prevBtn?.addEventListener("click", () => {
          updateCarousel(currentIndex - 1);
        });

        nextBtn?.addEventListener("click", () => {
          updateCarousel(currentIndex + 1);
        });

        // Event listeners for indicators
        indicators.forEach((indicator, index) => {
          indicator.addEventListener("click", () => {
            // When clicking an indicator, go to that specific item
            updateCarousel(index);
          });
        });

        // Initialize carousel
        updateCarousel(0);

        // Handle window resize
        window.addEventListener("resize", () => {
          // Recalculate and update carousel on resize
          updateCarousel(currentIndex);
        });
      });
    </script>
  </div>
</section>
