---
import WebStoriesViewer from "./WebStoriesViewer";
import SectionIntroWithMicro from "./SectionIntroWithMicro.tsx";
---

<section>
  <div class="container-custom">
    <SectionIntroWithMicro
      microHeadline="Google Web Stories"
      microHeadlineColor="#285DA6"
      title="IMMERSIVE SKI EXPERIENCES"
      subtitle="Explore our curated collection of luxury alpine stories and discover the extraordinary world of Perfect Piste."
      client:visible
    />

    <style>
      :global(.section-intro) {
        max-width: 100%;
        width: 100%;
        margin-bottom: 2rem;
      }

      :global(.section-title) {
        white-space: normal;
        font-size: clamp(1.25rem, 1.5vw, 1.8rem);
        overflow: visible;
        max-width: 100%;
        text-align: center;
      }

      :global(.section-subtitle) {
        font-size: 0.875rem;
        line-height: 1.5rem;
        font-weight: 300;
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
      }

      @media (min-width: 640px) {
        :global(.section-intro) {
          margin-bottom: 2.5rem;
        }
        :global(.section-title) {
          white-space: nowrap;
        }
      }

      @media (max-width: 1200px) {
        :global(.section-title) {
          font-size: clamp(1.2rem, 1.4vw, 1.4rem);
          letter-spacing: 0.08em;
        }
      }
    </style>

    <WebStoriesViewer client:visible />
  </div>
</section>
