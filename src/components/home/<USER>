import { useState, useEffect, useRef } from "react";
import "../../styles/web-stories-viewer.css";

interface WebStoryProps {
  id: string;
  title: string;
  subtitle: string;
  coverImageUrl: string;
  storyUrl: string;
  publishDate: string;
}

// Sample stories data with actual implemented stories
const webStories: WebStoryProps[] = [
  {
    id: "exclusive-alpine-retreats",
    title: "Exclusive Alpine Retreats",
    subtitle: "Discover the most luxurious chalets in the Swiss Alps",
    coverImageUrl:
      "https://images.unsplash.com/photo-1548777123-e216912df7d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    storyUrl: "/stories/exclusive-alpine-retreats/index.html",
    publishDate: "2023-12-15",
  },
  {
    id: "luxury-apres-ski",
    title: "Luxury Après-Ski",
    subtitle: "The finest dining experiences after a day on the slopes",
    coverImageUrl:
      "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    storyUrl: "/stories/luxury-apres-ski/index.html",
    publishDate: "2023-12-10",
  },
  {
    id: "private-powder",
    title: "Private Powder",
    subtitle: "Exclusive access to untouched slopes and hidden gems",
    coverImageUrl:
      "https://images.unsplash.com/photo-1551524559-8af4e6624178?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    storyUrl: "/stories/sample-web-story-template.html",
    publishDate: "2023-12-05",
  },
  {
    id: "mountain-wellness",
    title: "Mountain Wellness",
    subtitle: "Rejuvenate with premium spa treatments in alpine retreats",
    coverImageUrl:
      "https://images.unsplash.com/photo-1540555700478-4be289fbecef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    storyUrl: "/stories/exclusive-alpine-retreats/index.html",
    publishDate: "2023-11-30",
  },
  {
    id: "winter-wonderland",
    title: "Winter Wonderland",
    subtitle: "Magical experiences beyond the slopes for the whole family",
    coverImageUrl:
      "https://images.unsplash.com/photo-1610995195985-7d1e76c43fd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    storyUrl: "/stories/luxury-apres-ski/index.html",
    publishDate: "2023-11-25",
  },
];

const WebStoriesViewer = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentStoryUrl, setCurrentStoryUrl] = useState("");
  const storiesContainerRef = useRef<HTMLDivElement>(null);
  const touchStartX = useRef<number | null>(null);

  const handlePrevious = () => {
    setActiveIndex((prevIndex) =>
      prevIndex === 0 ? webStories.length - 1 : prevIndex - 1
    );
  };

  const handleNext = () => {
    setActiveIndex((prevIndex) =>
      prevIndex === webStories.length - 1 ? 0 : prevIndex + 1
    );
  };

  const openStoryModal = (storyUrl: string) => {
    setCurrentStoryUrl(storyUrl);
    setIsModalOpen(true);
    document.body.style.overflow = "hidden"; // Prevent scrolling when modal is open
  };

  const closeStoryModal = () => {
    setIsModalOpen(false);
    document.body.style.overflow = ""; // Restore scrolling
  };

  // Handle touch events for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    // Store the initial touch position
    touchStartX.current = e.touches[0].clientX;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    // Prevent default to avoid scrolling while swiping
    e.preventDefault();
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (touchStartX.current === null) return;

    const touchEndX = e.changedTouches[0].clientX;
    const diff = touchStartX.current - touchEndX;

    // Swipe threshold - increased for better detection
    if (Math.abs(diff) > 30) {
      if (diff > 0) {
        handleNext();
      } else {
        handlePrevious();
      }
    } else {
      // If it's a small movement, treat it as a tap/click
      // This helps with clickability on mobile
      if (Math.abs(diff) < 10 && e.target) {
        // Find the closest story card
        const storyCard = (e.target as HTMLElement).closest(".web-story-card");
        if (
          storyCard &&
          activeIndex === parseInt(storyCard.getAttribute("data-index") || "0")
        ) {
          openStoryModal(webStories[activeIndex].storyUrl);
        }
      }
    }

    touchStartX.current = null;
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isModalOpen) {
        if (e.key === "Escape") {
          closeStoryModal();
        }
      } else {
        if (e.key === "ArrowLeft") {
          handlePrevious();
        } else if (e.key === "ArrowRight") {
          handleNext();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isModalOpen]);

  return (
    <div className="web-stories-viewer">
      <div className="web-stories-controls">
        <button
          className="web-stories-nav-button"
          onClick={handlePrevious}
          aria-label="Previous story"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </button>
        <button
          className="web-stories-nav-button"
          onClick={handleNext}
          aria-label="Next story"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </button>
      </div>

      <div
        className="web-stories-container"
        ref={storiesContainerRef}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {webStories.map((story, index) => (
          <div
            key={story.id}
            className={`web-story-card ${
              index === activeIndex ? "active" : ""
            }`}
            data-index={index}
            style={{
              transform: `translateX(${(index - activeIndex) * 110}%)`,
              opacity: index === activeIndex ? 1 : 0.7,
              zIndex: webStories.length - Math.abs(index - activeIndex),
            }}
            onClick={() => openStoryModal(story.storyUrl)}
          >
            <div className="web-story-card-image">
              <img src={story.coverImageUrl} alt={story.title} />
              <div className="web-story-card-overlay"></div>
            </div>
            <div className="web-story-card-content">
              <h3>{story.title}</h3>
              <p>{story.subtitle}</p>
              <div className="web-story-card-badge">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                  <path d="M2 17l10 5 10-5"></path>
                  <path d="M2 12l10 5 10-5"></path>
                </svg>
                <span>Web Story</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="web-stories-indicators">
        {webStories.map((_, index) => (
          <button
            key={index}
            className={`web-stories-indicator ${
              index === activeIndex ? "active" : ""
            }`}
            onClick={() => setActiveIndex(index)}
            onTouchStart={() => setActiveIndex(index)}
            aria-label={`Go to story ${index + 1}`}
          />
        ))}
      </div>

      {isModalOpen && (
        <div className="web-story-modal">
          <button
            className="web-story-modal-close"
            onClick={closeStoryModal}
            aria-label="Close story"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
          <iframe
            src={currentStoryUrl}
            title="Web Story"
            className="web-story-iframe"
            allowFullScreen
          ></iframe>
        </div>
      )}
    </div>
  );
};

export default WebStoriesViewer;
