import React from "react";

interface SectionIntroWithMicroProps {
  microHeadline: string;
  title: string;
  poeticLine?: string;
  subtitle?: string;
  className?: string;
  microHeadlineColor?: string;
}

const SectionIntroWithMicro = ({
  microHeadline,
  title,
  poeticLine = "",
  subtitle,
  className = "",
  microHeadlineColor,
}) => {
  return (
    <div className={`section-intro ${className}`}>
      <p
        className="section-micro-headline"
        style={microHeadlineColor ? { color: microHeadlineColor } : {}}
      >
        {microHeadline}
      </p>
      <h2 className="section-title">{title}</h2>

      {poeticLine && <p className="poetic-intro">{poeticLine}</p>}

      {subtitle && <p className="section-subtitle">{subtitle}</p>}
    </div>
  );
};

export default SectionIntroWithMicro;
