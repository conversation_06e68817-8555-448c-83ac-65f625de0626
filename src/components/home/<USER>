import React, { useState, useRef, useEffect } from "react";
import { MapPin } from "lucide-react";

interface Option {
  value: string;
  label: string;
}

interface DestinationSelectProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  id?: string;
  className?: string;
  disabled?: boolean;
}

const DestinationSelect: React.FC<DestinationSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = "Where are you going?",
  id,
  className = "",
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);
  const uniqueId = id || `select-${Math.random().toString(36).substring(2, 9)}`;

  // Find the selected option label
  const selectedOption = options.find((option) => option.value === value);
  const displayText = selectedOption ? selectedOption.label : placeholder;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle option selection
  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      <div ref={selectRef} className="relative">
        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#285DA6] h-4 w-4 z-20" />
        <button
          type="button"
          id={uniqueId}
          className={`w-full flex items-center pl-10 pr-4 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 text-left h-10 overflow-hidden ${
            disabled ? "opacity-60 cursor-not-allowed" : ""
          }`}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          aria-haspopup="listbox"
          aria-expanded={isOpen}
          disabled={disabled}
        >
          <span
            className={`${
              !selectedOption ? "text-foreground/60" : ""
            } truncate block overflow-hidden`}
          >
            {displayText}
          </span>
        </button>

        {isOpen && (
          <div className="absolute z-30 w-full min-w-[250px] mt-1 bg-background border border-border rounded-md shadow-lg max-h-60 overflow-auto text-left">
            <ul role="listbox" className="py-1 text-left">
              {options.map((option) => (
                <li
                  key={option.value}
                  role="option"
                  aria-selected={value === option.value}
                  className={`px-4 py-2 cursor-pointer hover:bg-primary/10 text-left ${
                    value === option.value ? "bg-accent text-primary" : ""
                  }`}
                  onClick={() => handleSelect(option.value)}
                >
                  {option.label}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default DestinationSelect;
