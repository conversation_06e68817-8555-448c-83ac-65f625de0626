---

---

<section>
  <style>
    @media (max-width: 1200px) {
      h2 {
        font-size: clamp(1.2rem, 1.4vw, 1.4rem) !important;
        letter-spacing: 0.08em;
      }
    }
  </style>
  <div class="container-custom">
    <div class="grid grid-cols-1 md:grid-cols-12 gap-6 md:gap-8 items-center">
      <!-- Left side - Content -->
      <div class="md:col-span-5 md:pr-4 order-1 md:order-1 mt-6 md:mt-0">
        <span class="text-sm uppercase tracking-wider text-[#285DA6] font-karla"
          >HOW TO SEARCH?</span
        >
        <h2
          class="font-baskervville uppercase tracking-[0.1em] mt-2 mb-3 md:mb-4 text-xl sm:text-2xl md:text-[26px] sm:whitespace-nowrap"
        >
          FIND YOUR PERFECT PISTE – INTELLIGENTLY
        </h2>

        <p class="font-baskervville text-xs sm:text-sm mb-3 md:mb-4">
          Cutting-edge technology meets ski expertise to deliver personalised,
          luxury ski escapes
        </p>

        <div class="space-y-3 md:space-y-4 mt-4 md:mt-6">
          <div class="flex items-start">
            <div
              class="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0 mr-2 sm:mr-3 flex items-center justify-center rounded-full bg-[#285DA6]/10 text-[#285DA6]"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="sm:w-4 sm:h-4"
              >
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
            </div>
            <div>
              <h3
                class="text-sm sm:text-base font-baskervville mb-1 uppercase tracking-wider"
              >
                More Than Just Listings
              </h3>
              <p class="font-baskervville text-xs sm:text-sm">
                At Perfect Piste, we don’t just list properties - we curate
                them. Our AAI blends decades of ski expertise with your
                preferences to match you with the most relevant resorts and
                luxury accommodation.
              </p>
            </div>
          </div>

          <div class="flex items-start">
            <div
              class="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0 mr-2 sm:mr-3 flex items-center justify-center rounded-full bg-[#285DA6]/10 text-[#285DA6]"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="sm:w-4 sm:h-4"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
            </div>
            <div>
              <h3
                class="text-sm sm:text-base font-baskervville mb-1 uppercase tracking-wider"
              >
                Smart Matching, Refined Results
              </h3>
              <p class="font-baskervville text-xs sm:text-sm">
                Whether you're a beginner, expert, solo traveller or a family,
                our smart search understands your needs - from terrain and
                resort vibe to group size and skill level.
              </p>
            </div>
          </div>

          <div class="flex items-start">
            <div
              class="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0 mr-2 sm:mr-3 flex items-center justify-center rounded-full bg-[#285DA6]/10 text-[#285DA6]"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="sm:w-4 sm:h-4"
              >
                <path
                  d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
                ></path>
              </svg>
            </div>
            <div>
              <h3
                class="text-sm sm:text-base font-baskervville mb-1 uppercase tracking-wider"
              >
                How to use our AAI search?
              </h3>
              <p class="font-baskervville text-xs sm:text-sm">
                Easy! Just type something like “family-friendly resort with
                beginner slopes” or “luxury hotel near black runs with a spa.”
                We’ll do the rest - quickly, accurately, and personally.
              </p>
            </div>
          </div>

          <div class="flex items-start">
            <div
              class="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0 mr-2 sm:mr-3 flex items-center justify-center rounded-full bg-[#285DA6]/10 text-[#285DA6]"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="sm:w-4 sm:h-4"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
            </div>
            <div>
              <h3
                class="text-sm sm:text-base font-baskervville mb-1 uppercase tracking-wider"
              >
                How to Use Our AI Search
              </h3>
              <p class="font-baskervville text-xs sm:text-sm">
                Simply type natural language queries like "family-friendly
                resort with beginner slopes" or "luxury chalet near advanced
                pistes with hot tub." Our AI understands your preferences and
                matches them with our curated collection of luxury ski
                accommodations.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Right side - Image -->
      <div class="relative md:col-span-7 order-2 md:order-2 mt-28">
        <!-- Carousel Container -->
        <div class="carousel w-full rounded-md shadow-md overflow-hidden">
          <div class="carousel-container overflow-hidden">
            <div
              class="carousel-track flex transition-transform duration-500"
              id="amenities-image-track"
            >
              <!-- Carousel Items -->
              <div class="carousel-item flex-none w-full">
                <div style="aspect-ratio: 16/10;">
                  <img
                    src="/search.png"
                    alt="Luxurious palace interior with ornate gold details and royal decor"
                    class="w-full h-full object-cover"
                  />
                </div>
              </div>
              <div class="carousel-item flex-none w-full">
                <div style="aspect-ratio:;">
                  <img
                    src="/search-2.png"
                    alt="Mountain lake with snow-capped peaks"
                    class="w-full h-full object-cover"
                  />
                </div>
              </div>
              <!-- <div class="carousel-item flex-none w-full">
                <div style="aspect-ratio: 16/10;">
                  <img
                    src="https://images.unsplash.com/photo-1548777123-e216912df7d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
                    alt="Alpine luxury chalet in the Swiss Alps"
                    class="w-full h-full object-cover"
                  />
                </div>
              </div> -->
            </div>
          </div>

          <!-- Carousel Controls -->
          <!-- <button
            class="amenities-carousel-prev absolute top-1/2 left-4 -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white rounded-full p-1 shadow-md z-10 transition-all duration-300"
            aria-label="Previous slide"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M15 18l-6-6 6-6"></path>
            </svg>
          </button>

          <button
            class="amenities-carousel-next absolute top-1/2 right-4 -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white rounded-full p-1 shadow-md z-10 transition-all duration-300"
            aria-label="Next slide"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M9 18l6-6-6-6"></path>
            </svg>
          </button> -->
        </div>

        <!-- Carousel Indicators -->
        <div
          class="amenities-carousel-indicators flex justify-center mt-4 gap-2"
        >
          <button
            class="amenities-carousel-indicator w-2 h-2 rounded-full bg-[#285DA6] transition-colors"
            data-index="0"
            aria-label="Go to slide 1"></button>
          <button
            class="amenities-carousel-indicator w-2 h-2 rounded-full bg-[#285DA6]/20 transition-colors"
            data-index="1"
            aria-label="Go to slide 2"></button>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  // Client-side functionality for the amenities carousel
  document.addEventListener("DOMContentLoaded", () => {
    // Image Carousel functionality
    const track = document.getElementById(
      "amenities-image-track"
    ) as HTMLElement;
    const items = track?.querySelectorAll(".carousel-item");
    const prevBtn = document.querySelector(".amenities-carousel-prev");
    const nextBtn = document.querySelector(".amenities-carousel-next");
    const indicators = document.querySelectorAll(
      ".amenities-carousel-indicator"
    );
    let currentIndex = 0;
    const itemCount = items?.length || 0;

    // Function to update the carousel position
    const updateCarousel = (index: number) => {
      if (!track || !items) return;

      // Update the transform to show the current slide
      track.style.transform = `translateX(-${index * 100}%)`;

      // Update indicators
      indicators.forEach((indicator, i) => {
        if (i === index) {
          indicator.classList.remove("bg-[#285DA6]/20");
          indicator.classList.add("bg-[#285DA6]");
        } else {
          indicator.classList.remove("bg-[#285DA6]");
          indicator.classList.add("bg-[#285DA6]/20");
        }
      });

      currentIndex = index;
    };

    // Event listeners for prev/next buttons
    prevBtn?.addEventListener("click", () => {
      const newIndex = (currentIndex - 1 + itemCount) % itemCount;
      updateCarousel(newIndex);
    });

    nextBtn?.addEventListener("click", () => {
      const newIndex = (currentIndex + 1) % itemCount;
      updateCarousel(newIndex);
    });

    // Event listeners for indicators
    indicators.forEach((indicator, index) => {
      indicator.addEventListener("click", () => {
        updateCarousel(index);
      });
    });

    // Auto-rotate carousel every 5 seconds
    let autoRotateInterval = setInterval(() => {
      const newIndex = (currentIndex + 1) % itemCount;
      updateCarousel(newIndex);
    }, 2000);

    // Pause auto-rotation when user interacts with carousel
    const pauseAutoRotate = () => {
      clearInterval(autoRotateInterval);
      // Restart after a delay
      setTimeout(() => {
        autoRotateInterval = setInterval(() => {
          const newIndex = (currentIndex + 1) % itemCount;
          updateCarousel(newIndex);
        }, 5000);
      }, 10000); // Restart auto-rotation after 10 seconds of inactivity
    };

    // Add event listeners to pause auto-rotation
    prevBtn?.addEventListener("click", pauseAutoRotate);
    nextBtn?.addEventListener("click", pauseAutoRotate);
    indicators.forEach((indicator) => {
      indicator.addEventListener("click", pauseAutoRotate);
    });
  });
</script>
