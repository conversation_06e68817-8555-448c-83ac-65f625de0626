import React, { useState, useEffect } from "react";
import ShareModal from "./ShareModal";

const ShareModalController: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [url, setUrl] = useState("");
  const [title, setTitle] = useState("");

  useEffect(() => {
    // Set the current URL and title
    setUrl(window.location.href);
    setTitle(document.title);

    // Add the openShareModal function to the window object
    (window as any).openShareModal = () => {
      setIsOpen(true);
    };

    // Cleanup function
    return () => {
      (window as any).openShareModal = undefined;
    };
  }, []);

  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <ShareModal
      isOpen={isOpen}
      onClose={handleClose}
      url={url}
      title={title}
    />
  );
};

export default ShareModalController;
