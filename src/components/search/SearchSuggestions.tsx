import React from "react";
import "../../styles/search-suggestions.css";

interface SearchSuggestionProps {
  onSelectSuggestion: (suggestion: string) => void;
}

interface SuggestionItem {
  icon: string;
  text: string;
  expandedPrompt: string;
}

const SearchSuggestions: React.FC<SearchSuggestionProps> = ({
  onSelectSuggestion,
}) => {
  // First row suggestions (more premium/luxury focused)
  const firstRowSuggestions: SuggestionItem[] = [
    {
      icon: "👨‍👩‍👧‍👦",
      text: "Family resort with 5-star hotels",
      expandedPrompt:
        "I'm looking for a family-friendly ski resort that offers 5-star hotel accommodations with luxury amenities, excellent service, and activities suitable for all family members.",
    },
    {
      icon: "👴👵",
      text: "Resort that works for grandparents and young kids",
      expandedPrompt:
        "I need a ski resort that caters to multi-generational families, with gentle slopes for beginners, easy access facilities for grandparents, and fun activities for young children.",
    },
    {
      icon: "⛷️",
      text: "Family-friendly place to all start skiing together",
      expandedPrompt:
        "I want to find a beginner-friendly ski resort where our entire family can learn to ski together, with excellent ski schools and patient instructors for all ages.",
    },
    {
      icon: "�",
      text: "Adult-friendly resort for learning to ski later in life",
      expandedPrompt:
        "I'm looking for a ski resort that specializes in adult beginner programs, with patient instructors and comfortable learning environments for people starting to ski later in life.",
    },
    {
      icon: "🔰",
      text: "Best resort for first-time skiers",
      expandedPrompt:
        "I want to find the best ski resort for complete beginners, with gentle slopes, excellent beginner facilities, and comprehensive learn-to-ski programs.",
    },
    {
      icon: "☀️",
      text: "Wide blue runs and sunny cruising",
      expandedPrompt:
        "I'm looking for ski resorts with wide, well-groomed blue runs and plenty of sunshine, perfect for relaxed cruising and enjoying beautiful mountain scenery.",
    },
    {
      icon: "🏘️",
      text: "Stylish village with great skiing and lively après",
      expandedPrompt:
        "I want a ski resort with a charming, stylish village atmosphere that combines excellent skiing with vibrant après-ski scene and nightlife.",
    },
    {
      icon: "🏔️",
      text: "Quiet alpine village with great food and charm",
      expandedPrompt:
        "I'm seeking a peaceful, authentic alpine village with exceptional local cuisine, traditional charm, and a tranquil atmosphere away from crowded resorts.",
    },
    {
      icon: "🍽️",
      text: "Quiet alpine village with great food and charm",
      expandedPrompt:
        "I'm seeking a peaceful, authentic alpine village with exceptional local cuisine, traditional charm, and a tranquil atmosphere away from crowded resorts.",
    },
    {
      icon: "🎿",
      text: "Solo-friendly destination with great terrain ",
      expandedPrompt:
        "I'm looking for a ski resort that's welcoming to solo travelers, with diverse terrain, opportunities to meet other skiers, and a friendly, inclusive atmosphere.",
    },
  ];

  // Second row suggestions (more activity/experience focused)
  const secondRowSuggestions: SuggestionItem[] = [
    {
      icon: "🎿",
      text: "Resort that lets me progress without pressure",
      expandedPrompt:
        "I'm a beginner or intermediate skier looking for a relaxed resort with gentle slopes, supportive instructors, and a stress-free learning environment.",
    },
    {
      icon: "👨‍👩‍👧‍👦",
      text: "Black and red pistes without going off-piste",
      expandedPrompt:
        "I'm an experienced skier looking for a resort with well-groomed black and red runs that are challenging but accessible without the need to ski off-piste.",
    },
    {
      icon: "🏆",
      text: "Technical terrain, groomed and lift-accessible",
      expandedPrompt:
        "I want a resort with advanced groomed slopes and technical terrain that is easily accessible by lifts, ideal for pushing my skiing skills further.",
    },
    {
      icon: "⛷️",
      text: "A challenging resort with expert-level runs",
      expandedPrompt:
        "I'm looking for a resort that offers steep gradients, moguls, and expert-only runs to test my limits on the slopes.",
    },
    {
      icon: "🍽️",
      text: "Powder and freeride skiing with minimal crowds",
      expandedPrompt:
        "I'm looking for a hidden gem resort that offers excellent powder and freeride terrain with minimal crowds and untouched backcountry zones.",
    },
    {
      icon: "🌞",
      text: "High-altitude, snow-sure skiing for March or April",
      expandedPrompt:
        "I'm planning a spring ski trip and need a high-altitude resort with reliable snow conditions and open pistes well into March or April.",
    },
    {
      icon: "🏔️",
      text: "Unique ski experiences like glacier descents or Sella Ronda",
      expandedPrompt:
        "I'm looking for unforgettable ski adventures such as glacier skiing, circuit routes like the Sella Ronda, or iconic ski safaris.",
    },
    {
      icon: "🥾",
      text: "A mix of ski touring and alpine comfort",
      expandedPrompt:
        "I'm seeking a resort that offers both guided ski touring experiences and luxurious alpine accommodations to relax after long days on the mountain.",
    },
    {
      icon: "🧒",
      text: "Resort where teens can ski more independently",
      expandedPrompt:
        "I want a resort with safe terrain and teen-friendly zones where older kids can enjoy skiing with a degree of independence and age-appropriate activities.",
    },
    {
      icon: "🎉",
      text: "Fun ski weekend with friends",
      expandedPrompt:
        "I'm planning a lively weekend getaway with friends, looking for great skiing, après-ski bars, and social activities on and off the slopes.",
    },
  ];

  return (
    <div className="search-suggestions-container">
      <div className="suggestions-row">
        {firstRowSuggestions.map((suggestion, index) => (
          <button
            key={`row1-${index}`}
            className="suggestion-chip"
            onClick={() => onSelectSuggestion(suggestion.expandedPrompt)}
          >
            <span className="suggestion-icon">{suggestion.icon}</span>
            <span className="suggestion-text">{suggestion.text}</span>
          </button>
        ))}
      </div>

      <div className="suggestions-row">
        {secondRowSuggestions.map((suggestion, index) => (
          <button
            key={`row2-${index}`}
            className="suggestion-chip"
            onClick={() => onSelectSuggestion(suggestion.expandedPrompt)}
          >
            <span className="suggestion-icon">{suggestion.icon}</span>
            <span className="suggestion-text">{suggestion.text}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default SearchSuggestions;
