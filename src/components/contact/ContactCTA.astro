---
// Props for the component
interface Props {
  title?: string;
  subtitle?: string;
  backgroundImage?: string;
}

const {
  title = "Experience Unparalleled Luxury",
  subtitle = "Join our exclusive community of discerning travelers who expect nothing but the extraordinary.",
  backgroundImage = "https://images.unsplash.com/photo-1506974210756-8e1b8985d348?ixlib=rb-4.0.3&auto=format&fit=crop&w=2576&q=80",
} = Astro.props;
---

<section class="py-12 container-custom">
  <div class="mx-auto">
    <div class="relative overflow-hidden rounded-lg">
      <!-- Background Image with Gradient Overlay -->
      <div class="absolute inset-0 z-0">
        <img
          src={backgroundImage}
          alt="Luxury mountain experience"
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-gradient-to-r from-[#285DA6]/90 to-black/70"></div>
      </div>
      
      <!-- Content -->
      <div class="relative z-10 p-8 md:p-12 lg:p-16">
        <div class="max-w-2xl">
          <!-- Decorative Element -->
          <div class="w-16 h-1 bg-white mb-6"></div>
          
          <h2 class="text-3xl md:text-4xl font-baskervville text-white mb-4">
            {title}
          </h2>
          
          <p class="text-white/90 text-lg mb-8">
            {subtitle}
          </p>
          
          <div class="flex flex-wrap gap-6">
            <a
              href="/stays"
              class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
            >
              Explore Our Collection
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-2"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            </a>
            
            <a
              href="/destinations"
              class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
            >
              Discover Destinations
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-2"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            </a>
          </div>
        </div>
      </div>
      
      <!-- Decorative Elements -->
      <div class="absolute top-8 right-8 w-24 h-24 border border-white/20 rounded-full"></div>
      <div class="absolute bottom-8 right-16 w-16 h-16 border border-white/20 rounded-full"></div>
      <div class="absolute top-16 right-16 w-8 h-8 bg-white/10 rounded-full backdrop-blur-sm"></div>
    </div>
  </div>
</section>
