---
// Props for the component
interface Testimonial {
  quote: string;
  author: string;
  position: string;
  image?: string;
}

interface Props {
  testimonials?: Testimonial[];
}

const defaultTestimonials: Testimonial[] = [
  {
    quote: "<PERSON> <PERSON>ste created a truly bespoke experience that exceeded all expectations. Their attention to detail and personalized service made our ski holiday absolutely unforgettable.",
    author: "<PERSON>",
    position: "London, UK",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80"
  },
  {
    quote: "From the moment we arrived, every detail was meticulously arranged. The private chalet, the ski guides, the restaurant reservations – all were simply perfect.",
    author: "<PERSON>",
    position: "Paris, France",
    image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80"
  },
  {
    quote: "The concierge service was exceptional. They anticipated our needs before we even expressed them. This level of service is what truly sets <PERSON> apart.",
    author: "<PERSON>",
    position: "Munich, Germany",
    image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80"
  }
];

const { testimonials = defaultTestimonials } = Astro.props;
---

<section class="py-12 bg-accent/5">
  <div class="container-custom max-w-5xl mx-auto">
    <div class="text-center mb-10">
      <p class="text-sm uppercase tracking-wider text-[#285DA6] font-karla mb-2">Testimonials</p>
      <h2 class="text-3xl font-baskervville mb-3">What Our Clients Say</h2>
      <div class="w-16 h-0.5 bg-[#285DA6]/30 mx-auto"></div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      {testimonials.map((testimonial) => (
        <div class="bg-white p-6 rounded-lg shadow-sm border border-border/20 flex flex-col h-full">
          <div class="mb-4">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="32" 
              height="32" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              stroke-width="1" 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              class="text-[#285DA6]/30"
            >
              <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path>
              <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path>
            </svg>
          </div>
          
          <p class="italic text-foreground/80 mb-6 flex-grow">"{testimonial.quote}"</p>
          
          <div class="flex items-center mt-auto">
            {testimonial.image && (
              <img 
                src={testimonial.image} 
                alt={testimonial.author} 
                class="w-10 h-10 rounded-full object-cover mr-3"
              />
            )}
            <div>
              <p class="font-medium">{testimonial.author}</p>
              <p class="text-sm text-foreground/60">{testimonial.position}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
</section>
