---
// Props for the component
interface Feature {
  icon: string;
  title: string;
  description: string;
}

interface Props {
  features?: Feature[];
}

const defaultFeatures: Feature[] = [
  {
    icon: `<path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
           <path d="m7 9 5 3-5 3z"></path>`,
    title: "Private Jet Transfers",
    description: "Seamless travel from your doorstep to the slopes with our exclusive private jet service."
  },
  {
    icon: `<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
           <circle cx="12" cy="7" r="4"></circle>`,
    title: "Personal Concierge",
    description: "Your dedicated concierge is available 24/7 to fulfill any request, no matter how extraordinary."
  },
  {
    icon: `<path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
           <polyline points="9 22 9 12 15 12 15 22"></polyline>`,
    title: "Exclusive Properties",
    description: "Access to our portfolio of ultra-luxury chalets and penthouses not available to the general public."
  },
  {
    icon: `<circle cx="12" cy="12" r="10"></circle>
           <path d="m4.93 4.93 14.14 14.14"></path>`,
    title: "Private Mountain Access",
    description: "Experience the slopes before they open to the public with our exclusive mountain access privileges."
  }
];

const { features = defaultFeatures } = Astro.props;
---

<section class="py-12 bg-white">
  <div class="container-custom max-w-5xl mx-auto">
    <div class="text-center mb-10">
      <p class="text-sm uppercase tracking-wider text-[#285DA6] font-karla mb-2">Exclusive Services</p>
      <h2 class="text-3xl font-baskervville mb-3">Premium Privileges</h2>
      <p class="text-foreground/70 max-w-2xl mx-auto">
        As a Perfect Piste client, you'll enjoy access to a range of exclusive services and privileges designed to elevate your luxury travel experience.
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      {features.map((feature) => (
        <div class="flex items-start">
          <div class="w-12 h-12 flex-shrink-0 mr-4 flex items-center justify-center rounded-full bg-[#285DA6]/10 text-[#285DA6]">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="24" 
              height="24" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              stroke-width="1.5" 
              stroke-linecap="round" 
              stroke-linejoin="round"
              set:html={feature.icon}
            >
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-baskervville mb-2">{feature.title}</h3>
            <p class="text-foreground/70">{feature.description}</p>
          </div>
        </div>
      ))}
    </div>
    
    <div class="mt-10 text-center">
      <a
        href="/about"
        class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-[#285DA6] border-b border-[#285DA6] pb-1 transition-all hover:border-transparent"
      >
        Learn More About Our Services
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2"
        >
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </a>
    </div>
  </div>
</section>
