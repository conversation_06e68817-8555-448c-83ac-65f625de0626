---
// Props for the component
interface ContactInfo {
  address: {
    company: string;
    street: string;
    city: string;
  };
  phone: {
    general: string;
    concierge: string;
  };
  email: {
    general: string;
    reservations: string;
  };
  hours: {
    weekdays: string;
    saturday: string;
    sunday: string;
  };
}

interface Props {
  contactInfo?: ContactInfo;
}

const defaultContactInfo: ContactInfo = {
  address: {
    company: "Perfect Piste Headquarters",
    street: "1234 Luxury Avenue",
    city: "Zurich, Switzerland 8001",
  },
  phone: {
    general: "+41 22 345 67 89",
    concierge: "+41 22 345 67 90",
  },
  email: {
    general: "<EMAIL>",
    reservations: "<EMAIL>",
  },
  hours: {
    weekdays: "9:00 AM - 6:00 PM (CET)",
    saturday: "10:00 AM - 4:00 PM (CET)",
    sunday: "Closed",
  },
};

const { contactInfo = defaultContactInfo } = Astro.props;
---

<!-- Contact Details -->
<div>
  <h2 class="text-2xl font-baskervville mb-6">Contact Information</h2>

  <div class="space-y-8">
    <!-- Contact Info Items -->
    <div class="space-y-6">
      <!-- Address -->
      <div class="flex items-start">
        <div class="mr-4 mt-1">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#285DA6"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
        </div>
        <div>
          <h3
            class="text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
          >
            Address
          </h3>
          <address class="not-italic text-sm leading-relaxed">
            {contactInfo.address.company}<br />
            {contactInfo.address.street}<br />
            {contactInfo.address.city}
          </address>
        </div>
      </div>

      <!-- Phone -->
      <div class="flex items-start">
        <div class="mr-4 mt-1">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#285DA6"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
            ></path>
          </svg>
        </div>
        <div>
          <h3
            class="text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
          >
            Phone
          </h3>
          <div class="space-y-1">
            <p class="text-sm">
              <a
                href={`tel:${contactInfo.phone.general.replace(/\s+/g, "")}`}
                class="hover:text-[#285DA6] transition-colors"
              >
                {contactInfo.phone.general}
              </a>
              <span class="text-xs text-foreground/60 ml-1">(General)</span>
            </p>
            <p class="text-sm">
              <a
                href={`tel:${contactInfo.phone.concierge.replace(/\s+/g, "")}`}
                class="hover:text-[#285DA6] transition-colors"
              >
                {contactInfo.phone.concierge}
              </a>
              <span class="text-xs text-foreground/60 ml-1">(Concierge)</span>
            </p>
          </div>
        </div>
      </div>

      <!-- Email -->
      <div class="flex items-start">
        <div class="mr-4 mt-1">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#285DA6"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
            ></path>
            <polyline points="22,6 12,13 2,6"></polyline>
          </svg>
        </div>
        <div>
          <h3
            class="text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
          >
            Email
          </h3>
          <div class="space-y-1">
            <p class="text-sm">
              <a
                href={`mailto:${contactInfo.email.general}`}
                class="hover:text-[#285DA6] transition-colors"
              >
                {contactInfo.email.general}
              </a>
              <span class="text-xs text-foreground/60 ml-1">(General)</span>
            </p>
            <p class="text-sm">
              <a
                href={`mailto:${contactInfo.email.reservations}`}
                class="hover:text-[#285DA6] transition-colors"
              >
                {contactInfo.email.reservations}
              </a>
              <span class="text-xs text-foreground/60 ml-1">(Reservations)</span
              >
            </p>
          </div>
        </div>
      </div>

      <!-- Hours -->
      <div class="flex items-start">
        <div class="mr-4 mt-1">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#285DA6"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
        </div>
        <div>
          <h3
            class="text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
          >
            Hours
          </h3>
          <div class="text-sm space-y-0.5">
            <p>Mon-Fri: {contactInfo.hours.weekdays}</p>
            <p>Sat: {contactInfo.hours.saturday}</p>
            <p>Sun: {contactInfo.hours.sunday}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional contact info or note can go here if needed -->
    <div class="text-sm text-foreground/70 mt-2 italic">
      For urgent inquiries, please call our concierge line directly.
    </div>
  </div>
</div>
