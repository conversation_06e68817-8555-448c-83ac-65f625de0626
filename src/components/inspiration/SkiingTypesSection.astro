---
import DestinationCard from "../destinations/DestinationCard.astro";

// Define the featured skiing types/recommendations
const skiingTypes = [
  {
    id: "family-friendly",
    name: "Family Adventures",
    category: "Perfect for Families",
    propertyCount: 18,
    imageUrl: "https://images.unsplash.com/photo-1535640368727-187c8a674b5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    description: "Ideal for mixed-ability groups, these properties offer varied terrain for all levels, vibrant après-ski, and flexible, social spaces - making it easy to ski your way by day and come together in style by night.",
    searchPrompt: "I need ski resorts suitable for a group with mixed skiing abilities from beginners to advanced skiers",
  },
  {
    id: "off-piste",
    name: "Off-Piste Adventures",
    category: "Perfect for Off-Piste Skiing",
    propertyCount: 10,
    imageUrl: "https://wallpaperbat.com/img/49481-snow-skiing-wallpaper.jpg",
    description: "Ideal for adventurous skiers, these luxury properties offer access to world-class off-piste terrain, guided backcountry experiences, and pristine powder - followed by indulgent comfort, fine dining, and serene mountain views at day's end.",
    searchPrompt: "I want luxury accommodations with access to world-class off-piste terrain and guided backcountry experiences",
  },
  {
    id: "luxury-escapes",
    name: "Luxury Escapes",
    category: "Perfect for Luxury Seekers",
    propertyCount: 14,
    imageUrl: "https://wallpaperbat.com/img/166824-ski-jacket-brands-skiing-wallpaper.jpg",
    description: "Peaceful resorts with scenic trails, spa hotels, and intimate dining set the scene for romantic getaways—ideal for quality time, cozy accommodations, and unforgettable snowy memories together.",
    searchPrompt: "I'm searching for luxury ski resorts with premium amenities, spa facilities, and exceptional service",
  },
  {
    id: "beginners",
    name: "First-Time Skiers",
    category: "Perfect for Beginners",
    propertyCount: 12,
    imageUrl: "https://wallpaperbat.com/img/270245-image-men-helmet-sun-sports-jacket-winter-snow-skiing-1920x1080.jpg",
    description: "Ideal for first-timers and early learners, these destinations offer wide, gentle slopes, excellent ski schools, and a high number of blue runs. Well-groomed pistes and patient instructors make your first ski experience smooth and confidence-building.",
    searchPrompt: "I'm looking for ski resorts perfect for beginners with gentle slopes, excellent ski schools, and well-groomed pistes",
  },
  {
    id: "party",
    name: "Après-Ski Lovers",
    category: "Perfect for Après-Ski",
    propertyCount: 8,
    imageUrl: "https://wallpaperbat.com/img/24512-skiing-wallpaper-top-free-skiing-background.jpg",
    description: "Ideal for those who live for the après, these properties sit in lively resorts with bars, clubs, and mountain energy - offering great skiing by day and unforgettable parties just steps from your door.",
    searchPrompt: "I want luxury ski resorts with the best après-ski scene, vibrant nightlife, and great bars and restaurants",
  },
  {
    id: "advanced",
    name: "Expert Challenges",
    category: "Perfect for Advanced Skiers",
    propertyCount: 14,
    imageUrl: "https://wallpaperbat.com/img/292211-hd-wallpaper-person-skiing-on-snow-with-gear-set-person-doing.jpg",
    description: "Challenging black runs, steep descents, and technical terrain define these resorts—crafted for expert skiers seeking adrenaline, precision, and the ultimate test on the slopes.",
    searchPrompt: "I want advanced ski resorts with steep runs, technical terrain, and thrilling off-piste options",
  },
];
---

<section class="py-16 lg:py-24">
  <div class="container-custom">
    <!-- Section Header -->
    <div class="text-center mb-12 lg:mb-16">
      <span class="text-sm uppercase tracking-wider text-[#285DA6] font-karla">
        SKIING EXPERIENCES
      </span>
      <h2 class="text-3xl md:text-4xl lg:text-5xl font-baskervville uppercase tracking-[0.1em] mt-4 mb-6">
        Find Your Skiing Style
      </h2>
      <p class="font-karla text-base md:text-lg max-w-3xl mx-auto text-foreground/80 leading-relaxed">
        From gentle beginner slopes to challenging off-piste adventures, discover the perfect skiing experience tailored to your skill level and preferences.
      </p>
    </div>

    <!-- Skiing Types Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
      {skiingTypes.map((type) => (
        <DestinationCard
          id={type.id}
          name={type.name}
          propertyCount={type.propertyCount}
          imageUrl={type.imageUrl}
          category={type.category}
          description={type.description}
          searchPrompt={type.searchPrompt}
        />
      ))}
    </div>

    <!-- View All Link -->
    <div class="text-center mt-12">
      <a
        href="/destinations"
        class="inline-flex items-center font-karla font-bold text-sm uppercase tracking-[0.05em] text-[#285DA6] border-b-2 border-[#285DA6] pb-2 transition-all hover:border-transparent hover:text-[#285DA6]/80"
      >
        Explore All Destinations
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2"
        >
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </a>
    </div>
  </div>
</section>
