---
import SimpleHotelCard from "../hotels/SimpleHotelCard.astro";

interface Hotel {
  id: number | string;
  name: string;
  location: string;
  rating?: number;
  price?: number;
  currency?: string;
  imageUrl: string;
  featured?: boolean;
  description?: string;
  tags?: string[] | string[][];
}

interface Props {
  hotels: Hotel[];
}

const { hotels } = Astro.props;

// Filter hotels that are featured, or use all if none are marked as featured
const curatedHotels = hotels.filter((hotel) => hotel.featured === true).length > 0
  ? hotels.filter((hotel) => hotel.featured === true)
  : hotels.slice(0, 8); // Limit to 8 hotels if no featured ones
---

<section class="py-16 lg:py-24 bg-gray-50/50">
  <div class="container-custom">
    <!-- Section Header -->
    <div class="text-center mb-12 lg:mb-16">
      <span class="text-sm uppercase tracking-wider text-[#285DA6] font-karla">
        CURATED COLLECTION
      </span>
      <h2 class="text-3xl md:text-4xl lg:text-5xl font-baskervville uppercase tracking-[0.1em] mt-4 mb-6">
        Handpicked Luxury Stays
      </h2>
      <p class="font-karla text-base md:text-lg max-w-3xl mx-auto text-foreground/80 leading-relaxed">
        Our collection of exceptional properties, carefully selected by ski experts for their outstanding locations, luxury amenities, and proximity to world-class slopes.
      </p>
    </div>

    <!-- Hotels Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
      {curatedHotels.map((hotel) => (
        <SimpleHotelCard
          id={hotel.id}
          name={hotel.name}
          location={hotel.location}
          imageUrl={hotel.imageUrl}
        />
      ))}
    </div>

    <!-- View All Stays Link -->
    <div class="text-center mt-12">
      <a
        href="/stays"
        class="inline-flex items-center font-karla font-bold text-sm uppercase tracking-[0.05em] text-[#285DA6] border-b-2 border-[#285DA6] pb-2 transition-all hover:border-transparent hover:text-[#285DA6]/80"
      >
        View All Luxury Stays
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2"
        >
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </a>
    </div>
  </div>
</section>

<!-- Testimonials or Trust Indicators -->
<section class="py-12 lg:py-16">
  <div class="container-custom">
    <div class="text-center">
      <div class="flex flex-wrap justify-center items-center gap-8 lg:gap-12 opacity-60">
        <!-- Trust indicators or partner logos could go here -->
        <div class="text-sm font-karla uppercase tracking-wider text-foreground/60">
          Trusted by luxury travelers worldwide
        </div>
      </div>
    </div>
  </div>
</section>
