import { PerfectPisteAIProvider } from "./copilot-provider";
import { PerfectPisteAI } from "./copilot-chat";
import React, { useEffect, useState } from "react";

export const PerfectPisteAIContext: React.FC = () => {
  const [initialUserMessage, setInitialUserMessage] = useState<
    string | undefined
  >(undefined);

  // Read URL parameters when the component mounts
  useEffect(() => {
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      const userMessage = searchParams.get("user_message");
      const query = searchParams.get("query");

      // Use either user_message or query parameter
      if (userMessage) {
        console.log("Found user_message parameter:", userMessage);
        setInitialUserMessage(userMessage);
      } else if (query) {
        console.log("Found query parameter:", query);
        setInitialUserMessage(query);
      }
    }
  }, []);

  return (
    <PerfectPisteAIProvider>
      <PerfectPisteAI initialUserMessage={initialUserMessage} />
    </PerfectPisteAIProvider>
  );
};
