import React, { useEffect, useRef } from "react";
import "../../styles/ai-help-modal.css";

interface AIHelpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AIHelpModal: React.FC<AIHelpModalProps> = ({ isOpen, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const defaultExample =
    "Find me a ski resort ideal for beginners with excellent instructors and a heated pool.";

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    window.addEventListener("keydown", handleEscape);
    return () => window.removeEventListener("keydown", handleEscape);
  }, [isOpen, onClose]);

  // Prevent scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  // Handle clicking outside to close
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  };

  // Handle example prompt click - navigate to ai-search page
  const handleExampleClick = (example: string) => {
    // Close the modal
    onClose();

    // Build the search URL with the example as the query
    const params = new URLSearchParams();
    params.set("query", example);
    params.set("user_message", example);
    params.set("ai_search", "true");

    // Navigate to the ai-search page
    window.location.href = `/ai-search?${params.toString()}`;
  };

  return (
    <div
      className={`ai-help-modal-overlay ${isOpen ? "visible" : ""}`}
      onClick={handleOverlayClick}
    >
      <div className="ai-help-modal-container" ref={modalRef}>
        <button
          className="ai-help-modal-close"
          onClick={onClose}
          aria-label="Close modal"
        >
          ✕
        </button>

        <div className="ai-help-modal-content">
          <div className="ai-help-modal-header">
            <h2 className="ai-help-modal-title">
              Welcome to Perfect Piste AI 🏔️
            </h2>
          </div>

          <div className="ai-help-modal-section">
            <h3 className="ai-help-modal-section-title">What Can I Type?</h3>
            <p className="ai-help-modal-section-content">
              You can speak to it like a travel expert. The more detail, the
              better.
            </p>
            <p className="ai-help-modal-section-content">Try something like:</p>
            <div className="ai-help-example-prompts">
              <div
                className="ai-help-example-prompt"
                onClick={() =>
                  handleExampleClick(
                    "I'm looking for a family-friendly resort with ski-in/ski-out access and spa facilities."
                  )
                }
              >
                "I'm looking for a family-friendly resort with ski-in/ski-out
                access and spa facilities."
              </div>
              <div
                className="ai-help-example-prompt"
                onClick={() =>
                  handleExampleClick(
                    "Show me the best luxury chalets in Courchevel with private chefs."
                  )
                }
              >
                "Show me the best luxury chalets in Courchevel with private
                chefs."
              </div>
              <div
                className="ai-help-example-prompt"
                onClick={() =>
                  handleExampleClick(
                    "Plan a romantic weekend in Zermatt with a great view and gourmet dining."
                  )
                }
              >
                "Plan a romantic weekend in Zermatt with a great view and
                gourmet dining."
              </div>
            </div>
          </div>

          <div className="ai-help-modal-section">
            <h3 className="ai-help-modal-section-title">What Does It Do?</h3>
            <p className="ai-help-modal-section-content">
              Our AI understands your preferences and matches them with:
            </p>
            <ul className="ai-help-modal-section-content">
              <li>
                Destination highlights (scenic views, slopes, après-ski options)
              </li>
              <li>
                Hotel features (spa, dining, private access, kid-friendly)
              </li>
              <li>Real guest reviews and popularity</li>
            </ul>
          </div>

          <div className="ai-help-modal-section">
            <div className="ai-help-pro-tips">
              <h3 className="ai-help-pro-tips-title">Pro Tips</h3>
              <ul className="ai-help-pro-tips-list">
                <li>Ask full questions, not just keywords.</li>
                <li>
                  Be specific about who's traveling (solo, couple, family).
                </li>
                <li>
                  Mention amenities or vibe you want — "peaceful," "luxurious,"
                  "adventurous."
                </li>
              </ul>
            </div>
          </div>

          <button
            className="ai-help-try-example-button"
            onClick={() => handleExampleClick(defaultExample)}
          >
            Try this: "Find me a ski resort ideal for beginners with excellent
            instructors and a heated pool."
          </button>
        </div>
      </div>
    </div>
  );
};

export default AIHelpModal;
