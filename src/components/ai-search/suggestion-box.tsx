import React from "react";

/**
 * <PERSON>ps interface for the SuggestionBox component
 */
export interface SuggestionBoxProps {
  /**
   * Optional introduction text to display above the suggestions
   * @example "✨ Here are a few ways I can help you:"
   */
  introText?: string;

  /**
   * Array of suggestion strings to display as clickable chips
   */
  suggestions: string[];

  /**
   * Callback function that receives the selected suggestion text
   * @param suggestion The text of the clicked suggestion
   */
  onSuggestionClick: (suggestion: string) => void;
}

/**
 * A component that displays clickable suggestion chips for the AI search
 */
export const SuggestionBox: React.FC<SuggestionBoxProps> = ({
  introText,
  suggestions,
  onSuggestionClick,
}) => {
  // Don't render anything if there are no suggestions
  if (!suggestions || suggestions.length === 0) {
    return null;
  }

  return (
    <div className="suggestion-box-container mt-2 px-1 sm:px-0">
      {/* Optional introduction text */}
      {introText && (
        <p
          className="suggestion-intro-text text-xs sm:text-sm text-muted-foreground mb-2 sm:mb-3 leading-relaxed"
          style={{
            fontFamily:
              '"Funktional Grotesk", "Inter", "Helvetica Neue", Arial, sans-serif',
          }}
        >
          {introText}
        </p>
      )}

      {/* Suggestions container with flex layout */}
      <div className="suggestion-chips-container flex flex-wrap gap-1.5 sm:gap-2">
        {suggestions.map((suggestion, index) => (
          <button
            key={`suggestion-${index}`}
            className="suggestion-chip text-xs sm:text-sm px-2.5 sm:px-3 py-1.5 sm:py-2 rounded-full bg-muted hover:bg-accent/10 transition-all duration-200 border border-border hover:border-primary/20 hover:shadow-sm hover:-translate-y-[1px] active:translate-y-0 min-h-[36px] sm:min-h-[40px] touch-manipulation"
            style={{
              fontFamily:
                '"Funktional Grotesk", "Inter", "Helvetica Neue", Arial, sans-serif',
            }}
            onClick={() => onSuggestionClick(suggestion)}
            aria-label={`Suggestion: ${suggestion}`}
          >
            {suggestion}
          </button>
        ))}
      </div>
    </div>
  );
};

export default SuggestionBox;
