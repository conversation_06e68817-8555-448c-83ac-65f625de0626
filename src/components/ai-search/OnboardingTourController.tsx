import React, { useState, useEffect } from "react";
import OnboardingTour from "./OnboardingTour";

interface OnboardingTourControllerProps {}

const OnboardingTourController: React.FC<OnboardingTourControllerProps> = () => {
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    // Add the startOnboardingTour function to the window object
    (window as any).startOnboardingTour = () => {
      setIsActive(true);
    };

    // Cleanup function
    return () => {
      (window as any).startOnboardingTour = undefined;
    };
  }, []);

  const handleClose = () => {
    setIsActive(false);
  };

  return <OnboardingTour isActive={isActive} onClose={handleClose} />;
};

export default OnboardingTourController;
