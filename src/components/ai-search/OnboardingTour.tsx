import React, { useState, useEffect, useRef } from "react";
import "../../styles/onboarding-tour.css";

interface OnboardingTourProps {
  isActive: boolean;
  onClose: () => void;
}

interface TourStep {
  id: string;
  selector: string;
  title: string;
  content: string;
  ctaText: string;
  position?: "top" | "bottom" | "left" | "right";
}

const tourSteps: TourStep[] = [
  {
    id: "ai-search-bar",
    selector: ".ai-search-input",
    title: "Start here",
    content:
      "Type naturally, just like you'd ask a friend for travel advice. You can delete or edit suggestions that appear automatically.",
    ctaText: "Next",
    position: "bottom",
  },
  {
    id: "ai-suggestions",
    selector: ".suggestion-chip",
    title: "Need ideas?",
    content:
      "Tap any of these curated suggestions to autofill the search bar. You can mix and match multiple prompts too!",
    ctaText: "Next",
    position: "top",
  },
  {
    id: "search-toggle",
    selector: ".search-toggle-wrapper",
    title: "Prefer the traditional way?",
    content:
      "Switch to Regular Search to manually select destination, dates, and guest count.",
    ctaText: "Next",
    position: "bottom",
  },
  {
    id: "regular-search",
    selector: "form:not(.airbnb-header-search)",
    title: "Manual search fields",
    content:
      "Enter your travel details here and browse results manually — ideal if you already know your destination.",
    ctaText: "Done",
    position: "bottom",
  },
];

const OnboardingTour: React.FC<OnboardingTourProps> = ({
  isActive,
  onClose,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Reset to first step whenever the tour becomes active
  useEffect(() => {
    if (isActive) {
      setCurrentStep(0);
    }
  }, [isActive]);

  // Find target element and calculate positions
  useEffect(() => {
    if (!isActive) return;

    const findTargetElement = () => {
      const step = tourSteps[currentStep];
      if (!step) return;

      let element = document.querySelector(step.selector) as HTMLElement;

      // Special handling for suggestions - find the first visible one
      if (step.selector === ".suggestion-chip") {
        const suggestions = document.querySelectorAll(".suggestion-chip");
        element = suggestions[0] as HTMLElement;
      }

      // Special handling for regular search fields - find the form that's not the AI search form
      if (step.selector === "form:not(.airbnb-header-search)") {
        const forms = document.querySelectorAll("form");
        for (let i = 0; i < forms.length; i++) {
          const form = forms[i] as HTMLElement;
          if (!form.classList.contains("airbnb-header-search")) {
            element = form;
            break;
          }
        }
      }

      if (element) {
        setTargetElement(element);
        calculateTooltipPosition(element, step.position || "bottom");

        // Scroll element into view if needed
        element.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "center",
        });
      } else {
        // If element not found, try again after a longer delay
        setTimeout(findTargetElement, 500);
      }
    };

    // Small delay to ensure DOM is ready
    const timer = setTimeout(findTargetElement, 100);
    return () => clearTimeout(timer);
  }, [currentStep, isActive]);

  const calculateTooltipPosition = (element: HTMLElement, position: string) => {
    const rect = element.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft =
      window.pageXOffset || document.documentElement.scrollLeft;

    const tooltipWidth = 320; // Approximate tooltip width
    const tooltipHeight = 200; // Approximate tooltip height
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let top = 0;
    let left = 0;

    switch (position) {
      case "bottom":
        top = rect.bottom + scrollTop + 20;
        left = rect.left + scrollLeft + rect.width / 2;

        // Ensure tooltip doesn't go off screen horizontally
        if (left + tooltipWidth / 2 > viewportWidth) {
          left = viewportWidth - tooltipWidth / 2 - 20;
        }
        if (left - tooltipWidth / 2 < 0) {
          left = tooltipWidth / 2 + 20;
        }
        break;

      case "top":
        top = rect.top + scrollTop - tooltipHeight - 20;
        left = rect.left + scrollLeft + rect.width / 2;

        // Ensure tooltip doesn't go off screen horizontally
        if (left + tooltipWidth / 2 > viewportWidth) {
          left = viewportWidth - tooltipWidth / 2 - 20;
        }
        if (left - tooltipWidth / 2 < 0) {
          left = tooltipWidth / 2 + 20;
        }
        break;

      case "left":
        top = rect.top + scrollTop + rect.height / 2;
        left = rect.left + scrollLeft - tooltipWidth - 20;

        // If tooltip would go off screen, position it to the right instead
        if (left < 0) {
          left = rect.right + scrollLeft + 20;
        }
        break;

      case "right":
        top = rect.top + scrollTop + rect.height / 2;
        left = rect.right + scrollLeft + 20;

        // If tooltip would go off screen, position it to the left instead
        if (left + tooltipWidth > viewportWidth) {
          left = rect.left + scrollLeft - tooltipWidth - 20;
        }
        break;
    }

    // Ensure tooltip doesn't go off screen vertically
    if (top < scrollTop + 20) {
      top = scrollTop + 20;
    }
    if (top + tooltipHeight > scrollTop + viewportHeight - 20) {
      top = scrollTop + viewportHeight - tooltipHeight - 20;
    }

    setTooltipPosition({ top, left });
  };

  const handleNext = () => {
    if (currentStep < tourSteps.length - 1) {
      const nextStep = currentStep + 1;

      // Special handling: if we're moving to the regular search step,
      // ensure regular search mode is active
      if (tourSteps[nextStep].id === "regular-search") {
        // Find and click the regular search toggle
        const regularSearchToggle = document.querySelector(
          ".toggle-option:not(.ai-option)"
        ) as HTMLElement;
        if (regularSearchToggle) {
          regularSearchToggle.click();
          // Wait a bit for the UI to update before proceeding
          setTimeout(() => {
            setCurrentStep(nextStep);
          }, 300);
          return;
        }
      }

      setCurrentStep(nextStep);
    } else {
      onClose();
    }
  };

  const handleSkip = () => {
    onClose();
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isActive) {
        onClose();
      }
    };

    window.addEventListener("keydown", handleEscape);
    return () => window.removeEventListener("keydown", handleEscape);
  }, [isActive, onClose]);

  // Prevent body scroll when tour is active
  useEffect(() => {
    if (isActive) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isActive]);

  if (!isActive || !targetElement) return null;

  const currentTourStep = tourSteps[currentStep];
  const targetRect = targetElement.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  // Special offset for suggestion chips
  const highlightOffset = currentTourStep.id === "ai-suggestions" ? -24 : 0;

  return (
    <div className="onboarding-tour-overlay">
      {/* Dark overlay with spotlight */}
      <div
        className="onboarding-spotlight"
        style={{
          clipPath: `polygon(0% 0%, 0% 100%, ${
            targetRect.left + scrollLeft - 8
          }px 100%, ${targetRect.left + scrollLeft - 8}px ${
            targetRect.top + scrollTop - 8 + highlightOffset
          }px, ${targetRect.right + scrollLeft + 8}px ${
            targetRect.top + scrollTop - 8 + highlightOffset
          }px, ${targetRect.right + scrollLeft + 8}px ${
            targetRect.bottom + scrollTop + 8 + highlightOffset
          }px, ${targetRect.left + scrollLeft - 8}px ${
            targetRect.bottom + scrollTop + 8 + highlightOffset
          }px, ${targetRect.left + scrollLeft - 8}px 100%, 100% 100%, 100% 0%)`,
        }}
      />

      {/* Highlighted element border */}
      <div
        className="onboarding-highlight"
        style={{
          top: targetRect.top + scrollTop - 8 + highlightOffset,
          left: targetRect.left + scrollLeft - 8,
          width: targetRect.width + 16,
          height: targetRect.height + 16,
        }}
      />

      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className={`onboarding-tooltip onboarding-tooltip-${
          currentTourStep.position || "bottom"
        }`}
        style={{
          top: tooltipPosition.top,
          left: tooltipPosition.left,
        }}
      >
        <div className="onboarding-tooltip-content">
          <h3 className="onboarding-tooltip-title">{currentTourStep.title}</h3>
          <p className="onboarding-tooltip-text">{currentTourStep.content}</p>

          <div className="onboarding-tooltip-actions">
            <button className="onboarding-skip-button" onClick={handleSkip}>
              Skip tour
            </button>
            <div className="onboarding-progress">
              <span className="onboarding-step-counter">
                {currentStep + 1} of {tourSteps.length}
              </span>
              <button className="onboarding-next-button" onClick={handleNext}>
                {currentTourStep.ctaText}
              </button>
            </div>
          </div>
        </div>

        {/* Tooltip arrow */}
        <div
          className={`onboarding-tooltip-arrow onboarding-tooltip-arrow-${
            currentTourStep.position || "bottom"
          }`}
        />
      </div>
    </div>
  );
};

export default OnboardingTour;
