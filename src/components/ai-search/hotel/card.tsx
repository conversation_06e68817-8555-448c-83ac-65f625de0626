"use client";

import { Card } from "../../../components/ui/card";
import { Button } from "../../../components/ui/button";
import type { Hotel } from "./types";
import {
  MapPin as MapPinIcon,
  Check as CheckIcon,
  Eye as EyeIcon,
  Star as StarIcon,
  Wifi as WifiIcon,
  Coffee as CoffeeIcon,
  Utensils as UtensilsIcon,
  <PERSON><PERSON><PERSON> as DumbbellIcon,
  Snowf<PERSON> as SnowflakeIcon,
  ArrowRight as ArrowRightIcon,
} from "lucide-react";

interface ChatHotelCardProps {
  hotel: Hotel;
  onSelect?: (hotel: Hotel) => void;
  onViewDetails?: (hotel: Hotel) => void;
}

export function ChatHotelCard({
  hotel,
  onSelect,
  onViewDetails,
}: ChatHotelCardProps) {
  // Get the main image URL
  const getMainImageUrl = (): string => {
    if (hotel.images && hotel.images.length > 0) {
      return hotel.images[0].url;
    }
    if (hotel.thumbnail) {
      return hotel.thumbnail;
    }
    return "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80";
  };

  // Format price for display
  const formatPrice = (): string => {
    if (hotel.available_rooms && hotel.available_rooms.length > 0) {
      // Find the lowest price room
      const lowestPriceRoom = hotel.available_rooms.reduce(
        (lowest, room) =>
          room.price.amount < lowest.price.amount ? room : lowest,
        hotel.available_rooms[0]
      );

      return `${lowestPriceRoom.price.currency_code} ${lowestPriceRoom.price.amount}`;
    }

    if (hotel.price) {
      return `${hotel.currency || "$"}${hotel.price}`;
    }

    return "Price on request";
  };

  // Get sample amenities (either from hotel or default ones)
  const getAmenities = (): string[] => {
    if (hotel.amenities && hotel.amenities.length > 0) {
      return hotel.amenities.slice(0, 3);
    }

    // Default amenities based on hotel stars
    const defaultAmenities = [
      "Free WiFi",
      "Breakfast",
      "Restaurant",
      "Fitness Center",
      "Air Conditioning",
      "Ski Storage",
      "Spa",
      "Room Service",
    ];

    // Return 3 random amenities
    return defaultAmenities.sort(() => 0.5 - Math.random()).slice(0, 3);
  };

  // Get amenity icon
  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();

    if (amenityLower.includes("wifi"))
      return <WifiIcon className="h-3 w-3 mr-1" />;
    if (amenityLower.includes("breakfast"))
      return <CoffeeIcon className="h-3 w-3 mr-1" />;
    if (amenityLower.includes("restaurant"))
      return <UtensilsIcon className="h-3 w-3 mr-1" />;
    if (amenityLower.includes("fitness") || amenityLower.includes("gym"))
      return <DumbbellIcon className="h-3 w-3 mr-1" />;
    if (amenityLower.includes("ski") || amenityLower.includes("snow"))
      return <SnowflakeIcon className="h-3 w-3 mr-1" />;

    return null;
  };

  // Render star rating
  const renderStars = () => {
    if (!hotel.stars) return null;

    return (
      <div className="flex items-center">
        {[...Array(Math.floor(hotel.stars))].map((_, i) => (
          <StarIcon
            key={i}
            className="h-3.5 w-3.5 text-yellow-400 fill-yellow-400"
          />
        ))}
        {hotel.stars % 1 > 0 && (
          <StarIcon className="h-3.5 w-3.5 text-yellow-400 fill-yellow-400 opacity-50" />
        )}
      </div>
    );
  };

  return (
    <Card
      className="overflow-hidden border shadow-sm hover:shadow-md transition-all duration-300 group rounded-xl h-full ai-search-hotel-card"
      style={{
        fontFamily:
          '"Funktional Grotesk", "Inter", "Helvetica Neue", Arial, sans-serif',
      }}
    >
      <div className="flex flex-col h-full">
        {/* Image Section */}
        <div className="relative aspect-[4/3] sm:aspect-[4/3] overflow-hidden">
          <img
            src={getMainImageUrl()}
            alt={hotel.name}
            className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
          />

          {/* Price Badge */}
          <div className="absolute top-0 right-0 bg-primary text-white m-2 sm:m-3 px-1.5 sm:px-2 py-0.5 sm:py-1 text-xs font-medium rounded-md">
            From {formatPrice()} / night
          </div>

          {/* Star Rating Badge */}
          {hotel.stars && (
            <div className="absolute top-0 left-0 bg-black/60 text-white m-2 sm:m-3 px-1.5 sm:px-2 py-0.5 sm:py-1 text-xs font-medium rounded-md flex items-center">
              {renderStars()}
            </div>
          )}
        </div>

        {/* Content Section */}
        <div className="p-3 sm:p-4 flex flex-col flex-grow">
          {/* Title and Location */}
          <div className="mb-2 sm:mb-3">
            <h3 className="text-base sm:text-lg font-semibold mb-1 line-clamp-1">
              {hotel.name}
            </h3>
            <div className="flex items-center text-xs sm:text-sm text-muted-foreground">
              <MapPinIcon className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1 sm:mr-1.5 flex-shrink-0" />
              <span className="truncate">{hotel.location || "Location"}</span>
            </div>
          </div>

          {/* Amenities */}
          <div className="mb-3 sm:mb-4 flex flex-wrap gap-1 sm:gap-1.5">
            {getAmenities().map((amenity, index) => (
              <span
                key={index}
                className="inline-flex items-center rounded-full border border-accent/50 px-2 sm:px-2.5 py-0.5 text-xs font-medium bg-accent/10"
              >
                {getAmenityIcon(amenity)}
                {amenity}
              </span>
            ))}
          </div>

          {/* Description */}
          {hotel.description && (
            <p className="text-sm text-muted-foreground mb-4 line-clamp-2 flex-grow">
              {hotel.description}
            </p>
          )}

          {/* Buttons */}
          <div className="flex flex-col gap-1.5 sm:gap-2 mt-auto">
            <Button
              className="w-full justify-between text-xs sm:text-sm min-h-[36px] sm:min-h-[40px]"
              variant="outline"
              size="sm"
              onClick={() => onViewDetails && onViewDetails(hotel)}
            >
              <span className="flex items-center">
                <EyeIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-1.5" />
                View Details
              </span>
              <ArrowRightIcon className="h-3 w-3 sm:h-4 sm:w-4 opacity-0 group-hover:opacity-100 transition-all" />
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}
