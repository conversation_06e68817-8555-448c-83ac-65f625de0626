// Define hotel types for AI search
export interface HotelImage {
  id?: string;
  url: string;
  alt?: string;
  isThumbnail?: boolean;
  rank?: number;
}

export interface HotelRoom {
  id: string;
  title: string;
  description: string;
  thumbnail?: string;
  metadata: {
    type: string;
    bed_type: string;
    hotel_id: string;
    amenities: string[];
    room_size: string;
    max_adults: number;
    max_infants: number;
    max_children: number;
    price_set_id: string;
    max_occupancy: number;
    max_extra_beds: number;
  };
  available_rooms: number;
  price: {
    amount: number;
    currency_code: string;
  };
}

export interface Hotel {
  id: string;
  name: string;
  handle: string;
  description: string;
  location: string;
  stars?: number;
  rating?: number;
  reviews?: number;
  price?: number;
  thumbnail?: string;
  images: HotelImage[];
  amenities?: string[];
  available_rooms?: HotelRoom[];
  currency?: string;
  check_in_time?: string;
  check_out_time?: string;
  is_featured?: boolean;
}
