import { Markdown } from "@copilotkit/react-ui";
import { useState } from "react";
import { Button } from "../ui/button";
import { Copy, RefreshCw, ThumbsUp, ThumbsDown, Loader2 } from "lucide-react";
import Avatar from "boring-avatars";

// Custom UserMessage component with boring-avatars
export function CustomUserMessage({ message, rawData, subComponent }: any) {
  return (
    <div className="flex items-start gap-2 sm:gap-4 px-3 sm:px-6 py-3 sm:py-4 flex-row-reverse">
      <Avatar
        size={32}
        name="User"
        variant="beam"
        colors={["#0070f3", "#0050b3", "#003380", "#001a40", "#000d20"]}
        className="sm:w-10 sm:h-10"
      />

      {/* Message */}
      <div className="bg-primary text-primary-foreground p-2 sm:p-3 rounded-lg max-w-[85%] sm:max-w-[80%] shadow-sm text-sm sm:text-base">
        {subComponent || message}
      </div>
    </div>
  );
}

// Custom AssistantMessage component with boring-avatars
export function CustomAssistantMessage(props: any) {
  const {
    message,
    isLoading,
    subComponent,
    onCopy,
    onRegenerate,
    onThumbsUp,
    onThumbsDown,
  } = props;

  console.log({ props });
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    if (message && onCopy) {
      navigator.clipboard.writeText(message);
      setCopied(true);
      onCopy(message);
      setTimeout(() => setCopied(false), 2000);
    } else if (message) {
      navigator.clipboard.writeText(message);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleRegenerate = () => {
    if (onRegenerate) {
      onRegenerate();
    }
  };

  const handleThumbsUp = () => {
    if (onThumbsUp && message) {
      onThumbsUp(message);
    }
  };

  const handleThumbsDown = () => {
    if (onThumbsDown && message) {
      onThumbsDown(message);
    }
  };

  const LoadingIcon = () => (
    <div className="flex items-center">
      <Loader2 className="h-4 w-4 animate-spin" />
    </div>
  );

  if (isLoading) {
    return <LoadingIcon />;
  }

  if (!message) {
    return null;
  }

  return (
    <div className="flex items-start gap-2 sm:gap-4 px-3 sm:px-6 py-3 sm:py-4">
      <Avatar
        size={32}
        name="Perfect Piste"
        variant="sunset"
        colors={["#FF5A5F", "#C13584", "#833AB4", "#405DE6", "#5851DB"]}
        className="sm:w-10 sm:h-10 flex-shrink-0"
      />

      {/* Message */}
      <div className="flex-1 min-w-0">
        <div className="bg-muted p-2 sm:p-3 rounded-lg max-w-[95%] sm:max-w-[80%] shadow-sm relative group ai-message-content">
          {message && (
            <div className="ai-markdown-content">
              <Markdown content={message || ""} />
            </div>
          )}
          <div
            className={`absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity gap-1 bg-background rounded-md shadow-sm p-1 hidden sm:flex`}
          >
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={handleRegenerate}
              aria-label="Regenerate"
              title="Regenerate"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={handleCopy}
              aria-label="Copy to clipboard"
              title="Copy to clipboard"
            >
              {copied ? (
                <span className="text-xs font-bold">✓</span>
              ) : (
                <Copy className="h-3 w-3" />
              )}
            </Button>
            {onThumbsUp && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={handleThumbsUp}
                aria-label="Thumbs up"
                title="Thumbs up"
              >
                <ThumbsUp className="h-3 w-3" />
              </Button>
            )}
            {onThumbsDown && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={handleThumbsDown}
                aria-label="Thumbs down"
                title="Thumbs down"
              >
                <ThumbsDown className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {subComponent && <div className="mt-2">{subComponent}</div>}
      </div>
    </div>
  );
}
