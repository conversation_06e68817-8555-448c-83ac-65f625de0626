import React from "react";
import { CustomAssistantMessage } from "./CustomMessages";
import SuggestionBox from "./suggestion-box";
import { ChatHotelList } from "./hotel/list";
import { ChatDestinationList } from "./destination/list";
import { Role } from "@copilotkit/runtime-client-gql";
import type { Hotel } from "./hotel/types";
import type { SkiDestination } from "./destination/types";

interface AssistantMessageWithSuggestionsProps {
  message?: any;
  isCurrentMessage?: boolean;
  isLoading?: boolean;
  subComponent?: React.ReactNode;
  onCopy?: (message: string) => void;
  onRegenerate?: () => void;
  onThumbsUp?: (message: string) => void;
  onThumbsDown?: (message: string) => void;
  suggestions: any;
  introText?: string;
  onSuggestionClick: (suggestion: string) => void;
  isLastMessage?: boolean; // Flag to indicate if this is the last assistant message
  hotels?: Hotel[]; // Array of hotels to display
  destinations?: SkiDestination[]; // Array of destinations to display
  destinationId?: string; // Optional destination ID for filtering hotels
  onSelectHotel?: (hotel: Hotel) => void; // Callback for hotel selection
  onSelectDestination?: (destination: SkiDestination) => void; // Callback for destination selection
}

export const AssistantMessageWithSuggestions: React.FC<
  AssistantMessageWithSuggestionsProps
> = (props) => {
  const {
    suggestions,
    introText,
    onSuggestionClick,
    isLastMessage = false,
    hotels = [],
    destinations = [],
    destinationId,
    onSelectHotel,
    onSelectDestination,
    ...assistantMessageProps
  } = props;

  // Handler for viewing hotel details
  const handleViewHotelDetails = (hotel: Hotel) => {
    console.log("View hotel details:", hotel);
    // You can add additional logic here if needed
  };

  // Handler for viewing destination details
  const handleViewDestinationDetails = (destination: SkiDestination) => {
    console.log("View destination details:", destination);
    // You can add additional logic here if needed
  };

  return (
    <div>
      <CustomAssistantMessage {...assistantMessageProps} />
      {props?.isCurrentMessage && (
        <div className="ml-8 sm:ml-14 mr-3 sm:mr-6 mt-1">
          {/* Display hotel listings if available */}
          {hotels && hotels.length > 0 ? (
            <div className="mt-3 sm:mt-4">
              <ChatHotelList
                onSelectHotel={onSelectHotel}
                onViewHotelDetails={handleViewHotelDetails}
                destinationId={destinationId}
                hotels={hotels}
              />
            </div>
          ) : destinations && destinations.length > 0 ? (
            /* Display destination listings if available and no hotels */
            <div className="mt-3 sm:mt-4">
              <ChatDestinationList
                onSelectDestination={onSelectDestination}
                onViewDestinationDetails={handleViewDestinationDetails}
                destinations={destinations}
              />
            </div>
          ) : (
            /* Display suggestion chips if no hotels or destinations are available */
            <SuggestionBox
              introText={introText}
              suggestions={suggestions?.suggestions}
              onSuggestionClick={onSuggestionClick}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default AssistantMessageWithSuggestions;
