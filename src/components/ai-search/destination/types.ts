// Define ski difficulty levels
export type SkiDifficulty =
  | "beginner"
  | "intermediate"
  | "advanced"
  | "expert"
  | "all-levels";

// Define price range options
export type PriceRange = "budget" | "mid-range" | "luxury" | "ultra-luxury";

export interface SkiDestination {
  id: string;
  name: string;
  location: string; // Changed from optional to required
  country: string; // Changed from optional to required
  description: string; // Changed from optional to required
  difficulty: SkiDifficulty; // Changed from optional to required with specific type
  price: string; // Changed from optional to required
  priceRange: PriceRange; // Changed from string to specific type
  imageUrl?: string;
  images?: Array<{ url: string; alt?: string }>;
  amenities?: string[];
  elevation?: string;
  trails?: number;
  snowDepth?: string;
  weather?: string;
  temperature?: string;
  newSnow?: string;
}
