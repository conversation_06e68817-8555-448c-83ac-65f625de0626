"use client";

import { Card } from "../../../components/ui/card";
import { Button } from "../../../components/ui/button";
import type { SkiDestination, SkiDifficulty, PriceRange } from "./types";
import {
  MapPin as MapPinIcon,
  Check as CheckIcon,
  Eye as EyeIcon,
} from "lucide-react";

interface ChatDestinationCardProps {
  destination: SkiDestination;
  onSelect?: (destination: SkiDestination) => void;
  onViewDetails?: (destination: SkiDestination) => void;
}

export function ChatDestinationCard({
  destination,
  onSelect,
  onViewDetails,
}: ChatDestinationCardProps) {
  return (
    <Card className="overflow-hidden h-[320px] border-0 shadow-md hover:shadow-xl transition-all duration-300 group rounded-xl">
      <div className="flex flex-col h-full">
        {/* Full Image Section */}
        <div className="relative h-full w-full overflow-hidden rounded-xl">
          {destination.images && destination.images.length > 0 ? (
            <img
              src={destination.images[0].url}
              alt={destination.name}
              className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
            />
          ) : destination.imageUrl ? (
            <img
              src={destination.imageUrl}
              alt={destination.name}
              className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
            />
          ) : (
            <div className="flex items-center justify-center h-full bg-gradient-to-r from-blue-600 to-indigo-600">
              <div className="h-16 w-16 text-white opacity-75 text-4xl">⛰️</div>
            </div>
          )}

          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/30 to-black/80"></div>

          {/* Content at bottom of image */}
          <div className="absolute bottom-0 left-0 right-0 z-10 p-4">
            {/* Title and location */}
            <div className="mb-3">
              <h3 className="text-base font-bold text-white truncate drop-shadow-md">
                {destination.name}
              </h3>
              <div className="flex items-center text-sm text-white/90 mt-1">
                <MapPinIcon className="h-3.5 w-3.5 mr-1.5 flex-shrink-0" />
                <span className="truncate">
                  {destination.location || "Location"}
                  {destination.country ? `, ${destination.country}` : ""}
                </span>
              </div>
            </div>

            {/* Buttons */}
            <div className="flex gap-2">
              <Button
                className="flex-1 rounded-lg font-medium"
                size="default"
                onClick={() => onSelect && onSelect(destination)}
              >
                <CheckIcon className="h-4 w-4 mr-1.5" />
                Select
              </Button>
              <Button
                className="flex-1 rounded-lg font-medium bg-white/90 hover:bg-white text-black hover:text-black border-white/80 hover:border-white"
                variant="outline"
                size="default"
                onClick={() => onViewDetails && onViewDetails(destination)}
              >
                <EyeIcon className="h-4 w-4 mr-1.5" />
                Details
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
