"use client";

import { useState } from "react";
import { ChatDestinationCard } from "./card";
import type { SkiDestination } from "./types";
import { Skeleton } from "../../../components/ui/skeleton";
import { Button } from "../../../components/ui/button";
import { Card, CardContent, CardFooter } from "../../../components/ui/card";
import { AlertCircle } from "lucide-react";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "../../../components/ui/alert";
import DestinationDetailModal from "../../destinations/DestinationDetailModal";
import "../../../styles/destination-modal.css";

interface ChatDestinationListProps {
  onSelectDestination?: (destination: SkiDestination) => void;
  onViewDestinationDetails?: (destination: SkiDestination) => void;
  destinations?: SkiDestination[]; // Add destinations array from props
}

export function ChatDestinationList({
  onSelectDestination,
  onViewDestinationDetails,
  destinations = [], // Use destinations from props with empty array as default
}: ChatDestinationListProps) {
  // Modal state
  const [selectedDestination, setSelectedDestination] =
    useState<SkiDestination | null>(null);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [detailsLoading, setDetailsLoading] = useState<boolean>(false);

  // We no longer need loading state since we're not fetching data
  const [isLoading] = useState(false);
  const [error] = useState<string | null>(null);

  const handleSelectDestination = (destination: SkiDestination) => {
    if (onSelectDestination) {
      onSelectDestination(destination);
    }
  };

  const handleViewDestinationDetails = (destination: SkiDestination) => {
    // Set the selected destination and open modal
    setSelectedDestination(destination);
    setModalOpen(true);
    setDetailsLoading(true);

    // Simulate loading for a short time (in a real app, you might fetch additional details here)
    setTimeout(() => {
      setDetailsLoading(false);
    }, 500);

    // Also call the parent handler if provided
    if (onViewDestinationDetails) {
      onViewDestinationDetails(destination);
    }
  };

  // Close the modal
  const handleCloseModal = () => {
    setModalOpen(false);
  };

  if (isLoading) {
    return (
      <div className="w-full">
        <p className="text-sm mb-4">Loading destinations...</p>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {[...Array(6)].map((_, index) => (
            <Card key={index} className="overflow-hidden h-full">
              <div className="flex flex-col h-full">
                {/* Image Skeleton */}
                <div className="h-40">
                  <Skeleton className="h-full w-full" />
                </div>

                {/* Content Skeleton */}
                <CardContent className="flex-1 p-4 space-y-3">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />

                  <div className="grid grid-cols-2 gap-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                </CardContent>

                {/* Footer Skeleton */}
                <CardFooter className="pt-0 px-4 pb-4">
                  <Skeleton className="h-8 w-full" />
                </CardFooter>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error Loading Destinations</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (destinations.length === 0) {
    return (
      <div className="w-full">
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No Destinations Found</AlertTitle>
          <AlertDescription>
            No ski destinations are currently available. Please check back later
            or try a different search.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="w-full chat-destination-list-container">
      <div className="mb-4 sm:mb-6">
        <h3
          className="text-lg sm:text-xl font-bold mb-2"
          style={{
            fontFamily:
              '"Funktional Grotesk", "Inter", "Helvetica Neue", Arial, sans-serif',
          }}
        >
          Luxury Ski Destinations
        </h3>
        <p
          className="text-xs sm:text-sm text-muted-foreground max-w-3xl"
          style={{
            fontFamily:
              '"Funktional Grotesk", "Inter", "Helvetica Neue", Arial, sans-serif',
          }}
        >
          Discover these handpicked premium destinations for your perfect winter
          escape. Each location offers a unique blend of luxury amenities and
          world-class skiing.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {destinations.slice(0, 8).map((destination) => (
          <ChatDestinationCard
            key={destination.id}
            destination={destination}
            onSelect={() => handleSelectDestination(destination)}
            onViewDetails={() => handleViewDestinationDetails(destination)}
          />
        ))}
      </div>

      {destinations.length > 8 && (
        <div className="mt-6 text-center">
          <p className="text-sm text-muted-foreground">
            Showing 8 of {destinations.length} available destinations
          </p>
          <Button
            variant="outline"
            size="sm"
            className="text-sm mt-2 px-4"
            onClick={() => {
              // This is just for UI purposes - in a real app, you might show more destinations
              // or navigate to a full listing page
            }}
          >
            View All Destinations
          </Button>
        </div>
      )}

      {/* Destination Detail Modal */}
      {selectedDestination && (
        <DestinationDetailModal
          isOpen={modalOpen}
          onClose={handleCloseModal}
          destinationDetails={selectedDestination}
          loading={detailsLoading}
        />
      )}
    </div>
  );
}
