import { useEffect } from 'react';

/**
 * Component to ensure first message visibility in AI search interface
 * This component handles the scroll position to make sure the first message
 * is always visible when the chat interface loads
 */
export function FirstMessageVisibilityFix() {
  useEffect(() => {
    const ensureFirstMessageVisibility = () => {
      // Find all possible chat messages containers in the AI search interface
      const selectors = [
        '.ai-search-mobile-container .copilotkit-chat-messages-container',
        '.ai-search-mobile-container .copilotKitMessagesContainer',
        '.ai-search-mobile-container div[class*="copilotkit-chat-messages-container"]',
        '.ai-search-mobile-container div[class*="copilotKitMessagesContainer"]',
        '.ai-search-mobile-container .copilotkit-chat-messages',
        '.ai-search-mobile-container .copilotKitChatMessages',
        '.ai-search-mobile-container div[class*="copilotkit-chat-messages"]',
        '.ai-search-mobile-container div[class*="copilotKitChatMessages"]'
      ];

      selectors.forEach(selector => {
        const containers = document.querySelectorAll(selector);
        containers.forEach(container => {
          if (container instanceof HTMLElement) {
            // Scroll to top to ensure first message is visible
            container.scrollTop = 0;
            
            // Set scroll behavior for smooth scrolling
            container.style.scrollBehavior = 'smooth';
            
            // Ensure the container is properly positioned
            container.style.position = 'relative';
            
            // Remove any transforms that might hide content
            container.style.transform = 'none';
          }
        });
      });

      // Also ensure first message elements are properly positioned
      const messageSelectors = [
        '.ai-search-mobile-container .copilotkit-chat-messages > div:first-child',
        '.ai-search-mobile-container .copilotKitChatMessages > div:first-child',
        '.ai-search-mobile-container div[class*="copilotkit-chat-messages"] > div:first-child',
        '.ai-search-mobile-container div[class*="copilotKitChatMessages"] > div:first-child',
        '.ai-search-mobile-container .copilotkit-chat-messages > div:first-of-type',
        '.ai-search-mobile-container .copilotKitChatMessages > div:first-of-type',
        '.ai-search-mobile-container div[class*="copilotkit-chat-messages"] > div:first-of-type',
        '.ai-search-mobile-container div[class*="copilotKitChatMessages"] > div:first-of-type'
      ];

      messageSelectors.forEach(selector => {
        const messages = document.querySelectorAll(selector);
        messages.forEach(message => {
          if (message instanceof HTMLElement) {
            // Reset any problematic positioning
            message.style.position = 'static';
            message.style.top = '0';
            message.style.left = '0';
            message.style.right = '0';
            message.style.bottom = '0';
            message.style.margin = '0';
            message.style.padding = '0';
            message.style.transform = 'none';
            message.style.zIndex = 'auto';
            
            // Ensure the message is visible
            message.style.visibility = 'visible';
            message.style.opacity = '1';
            message.style.display = 'block';
          }
        });
      });
    };

    // Run immediately
    ensureFirstMessageVisibility();

    // Run after a short delay to catch dynamically loaded content
    const timeouts = [100, 250, 500, 1000];
    timeouts.forEach(delay => {
      setTimeout(ensureFirstMessageVisibility, delay);
    });

    // Set up a MutationObserver to watch for DOM changes
    const observer = new MutationObserver((mutations) => {
      let shouldRun = false;
      
      mutations.forEach((mutation) => {
        // Check if new nodes were added that might be chat messages
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node instanceof HTMLElement) {
              // Check if the added node is a chat message or container
              const isChatElement = node.classList.toString().includes('copilot') ||
                                  node.querySelector('[class*="copilot"]') !== null;
              if (isChatElement) {
                shouldRun = true;
              }
            }
          });
        }
        
        // Check if attributes changed that might affect visibility
        if (mutation.type === 'attributes') {
          const target = mutation.target as HTMLElement;
          if (target.classList.toString().includes('copilot')) {
            shouldRun = true;
          }
        }
      });

      if (shouldRun) {
        // Debounce the execution
        setTimeout(ensureFirstMessageVisibility, 50);
      }
    });

    // Start observing the AI search container
    const aiSearchContainer = document.querySelector('.ai-search-mobile-container');
    if (aiSearchContainer) {
      observer.observe(aiSearchContainer, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style']
      });
    }

    // Cleanup function
    return () => {
      observer.disconnect();
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  // This component doesn't render anything visible
  return null;
}

export default FirstMessageVisibilityFix;
