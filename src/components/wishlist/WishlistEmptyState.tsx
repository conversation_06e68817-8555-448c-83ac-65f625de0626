import React from 'react';

const WishlistEmptyState: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
      <div className="w-20 h-20 mb-6 text-[#C3C3C3]">
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="80" 
          height="80" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="1" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        >
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
      </div>
      
      <h3 className="font-baskervville text-2xl text-[#000000] mb-3">Your wishlist is empty</h3>
      
      <p className="font-baskervville text-base text-[#000000]/70 max-w-md mb-8">
        Save your favorite properties and experiences by clicking the heart icon while browsing our collection.
      </p>
      
      <a 
        href="/stays" 
        className="inline-flex items-center justify-center px-6 py-3 bg-[#285DA6] text-white rounded-md font-karla font-bold text-xs uppercase tracking-[0.05em] hover:bg-[#285DA6]/90 transition-colors"
      >
        EXPLORE PROPERTIES
      </a>
    </div>
  );
};

export default WishlistEmptyState;
