import React from 'react';
import type { WishlistItem as WishlistItemType } from './WishlistContext';

interface WishlistItemProps {
  item: WishlistItemType;
  onRemove: () => void;
}

const WishlistItem: React.FC<WishlistItemProps> = ({ item, onRemove }) => {
  return (
    <div className="bg-white border border-[#C3C3C3]/20 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
      {/* Item Image */}
      <div className="relative h-48 overflow-hidden">
        <img 
          src={item.image} 
          alt={item.name} 
          className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
        />
        <button 
          onClick={(e) => {
            e.preventDefault();
            onRemove();
          }}
          className="absolute top-3 right-3 bg-white rounded-full p-2 shadow-md hover:bg-[#285DA6] hover:text-white transition-colors duration-300"
          aria-label="Remove from wishlist"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="16" 
            height="16" 
            viewBox="0 0 24 24" 
            fill="currentColor" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
            className="text-[#285DA6] hover:text-white transition-colors duration-300"
          >
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
          </svg>
        </button>
      </div>
      
      {/* Item Details */}
      <div className="p-5">
        <h3 className="font-baskervville text-xl mb-2 text-[#000000]">{item.name}</h3>
        <p className="font-baskervville text-base text-[#000000]/70 mb-4">{item.location}</p>
        
        <div className="flex justify-between items-center">
          <a 
            href={`/stays/${item.id}`} 
            className="font-karla font-bold text-xs uppercase tracking-[0.05em] text-[#285DA6] flex items-center hover:underline"
          >
            VIEW DETAILS
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              className="ml-1"
            >
              <line x1="5" y1="12" x2="19" y2="12"></line>
              <polyline points="12 5 19 12 12 19"></polyline>
            </svg>
          </a>
          
          <span className="text-xs text-[#C3C3C3]">
            Added {new Date(item.addedAt).toLocaleDateString()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default WishlistItem;
