import React from "react";

interface SafeWishlistIconProps {
  className?: string;
}

// This is a simplified version of WishlistIcon that doesn't use the context
// It's used as a fallback when the WishlistProvider isn't available
const SafeWishlistIcon: React.FC<SafeWishlistIconProps> = ({
  className = "",
}) => {
  // State to store wishlist count
  const [wishlistCount, setWishlistCount] = React.useState(0);

  // Function to handle click and navigate to wishlist page
  const handleClick = () => {
    window.location.href = '/wishlist';
  };

  // Effect to load wishlist count from localStorage
  React.useEffect(() => {
    const loadWishlistCount = () => {
      try {
        const wishlistData = localStorage.getItem('wishlist');
        if (wishlistData) {
          const wishlist = JSON.parse(wishlistData);
          setWishlistCount(wishlist.length);
        }
      } catch (error) {
        console.error('Error loading wishlist count:', error);
      }
    };

    // Load count on mount
    loadWishlistCount();

    // Set up event listener for storage changes
    const handleStorageChange = () => {
      loadWishlistCount();
    };

    window.addEventListener('storage', handleStorageChange);

    // Custom event for wishlist updates within the same page
    window.addEventListener('wishlistUpdated', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('wishlistUpdated', handleStorageChange);
    };
  }, []);

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        className="flex items-center justify-center p-1.5 text-foreground hover:text-primary transition-colors"
        aria-label="Wishlist"
        onClick={handleClick}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="transition-transform duration-200"
        >
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
        {wishlistCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-[#285DA6] text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
            {wishlistCount}
          </span>
        )}
      </button>
    </div>
  );
};

export default SafeWishlistIcon;
