import React, { useState, useRef, useEffect } from 'react';
import { useWishlist } from './useWishlist';

interface WishlistIconProps {
  className?: string;
}

const WishlistIcon: React.FC<WishlistIconProps> = ({ className = '' }) => {
  const { wishlist } = useWishlist();
  const [showTooltip, setShowTooltip] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Close tooltip when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target as Node)) {
        setShowTooltip(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`relative ${className}`} ref={tooltipRef}>
      <button
        type="button"
        className="flex items-center justify-center p-2 text-foreground hover:text-primary transition-colors"
        onClick={() => setShowTooltip(!showTooltip)}
        aria-label="Wishlist"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="transition-transform duration-200"
        >
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
        {wishlist.length > 0 && (
          <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full h-4 w-4 flex items-center justify-center">
            {wishlist.length}
          </span>
        )}
      </button>

      {showTooltip && (
        <div className="absolute right-0 mt-2 w-64 bg-background border border-border rounded-md shadow-lg z-10 animate-slide-down">
          <div className="p-3 border-b border-border">
            <h3 className="font-karla font-medium text-sm">Your Wishlist</h3>
          </div>
          <div className="max-h-80 overflow-y-auto">
            {wishlist.length === 0 ? (
              <div className="p-4 text-center text-sm text-foreground/70">
                Your wishlist is empty
              </div>
            ) : (
              <ul>
                {wishlist.map((item) => (
                  <li key={item.id} className="p-3 border-b border-border/30 last:border-0">
                    <div className="flex items-center gap-3">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-12 h-12 object-cover rounded-md"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="font-karla text-sm font-medium truncate">{item.name}</h4>
                        <p className="text-xs text-foreground/70 truncate">{item.location}</p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
          {wishlist.length > 0 && (
            <div className="p-3 border-t border-border">
              <a
                href="/wishlist"
                className="block w-full py-2 px-4 bg-primary text-primary-foreground text-center text-sm rounded-md hover:bg-primary/90 transition-colors"
              >
                View Wishlist
              </a>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WishlistIcon;
