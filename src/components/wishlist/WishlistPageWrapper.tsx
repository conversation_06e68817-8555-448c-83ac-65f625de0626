import React from 'react';
import { useWishlist } from './useWishlist';
import WishlistEmptyState from './WishlistEmptyState';
import WishlistItem from './WishlistItem';

const WishlistPageWrapper: React.FC = () => {
  const { wishlist, removeFromWishlist, clearWishlist } = useWishlist();

  return (
    <div className="max-w-6xl mx-auto">
      {/* Page Header */}
      <div className="mb-12">
        <h2 className="font-baskervville text-3xl uppercase tracking-[0.1em] text-[#000000] mb-4">MY WISHLIST</h2>
        <p className="font-baskervville text-base text-[#000000]/80 max-w-3xl">
          Your saved properties and experiences. Explore your favorites and plan your perfect ski holiday.
        </p>
      </div>

      {/* Wishlist Content */}
      {wishlist.length === 0 ? (
        <WishlistEmptyState />
      ) : (
        <div>
          <div className="flex justify-between items-center mb-8">
            <p className="font-baskervville text-base text-[#000000]/80">
              {wishlist.length} {wishlist.length === 1 ? 'item' : 'items'} saved
            </p>
            <button
              onClick={clearWishlist}
              className="font-karla font-bold text-xs uppercase tracking-[0.05em] text-[#285DA6] hover:underline"
            >
              CLEAR ALL
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {wishlist.map((item) => (
              <WishlistItem 
                key={item.id} 
                item={item} 
                onRemove={() => removeFromWishlist(item.id)} 
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WishlistPageWrapper;
