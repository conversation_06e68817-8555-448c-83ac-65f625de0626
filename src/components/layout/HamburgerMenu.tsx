import { useState, useEffect, useRef } from "react";
import "../../styles/hamburger-menu.css";

interface NavItem {
  label: string;
  href: string;
}

interface HamburgerMenuProps {
  navItems: NavItem[];
  currentPath: string;
}

const HamburgerMenu = ({ navItems, currentPath }: HamburgerMenuProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
    // Toggle body scroll
    if (!isOpen) {
      document.body.style.overflow = "hidden";
      // Lower the z-index of header when menu is open
      const headerWrapper = document.querySelector(".header-wrapper");
      if (headerWrapper) {
        headerWrapper.setAttribute("style", "z-index: 99 !important");
      }
    } else {
      document.body.style.overflow = "";
      // Restore the z-index of header when menu is closed
      const headerWrapper = document.querySelector(".header-wrapper");
      if (headerWrapper) {
        headerWrapper.removeAttribute("style");
      }
    }
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        document.body.style.overflow = "";
        // Restore header z-index
        const headerWrapper = document.querySelector(".header-wrapper");
        if (headerWrapper) {
          headerWrapper.removeAttribute("style");
        }
      }
    };

    // Add event listener when menu is open
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      // Ensure header z-index is restored on unmount
      if (isOpen) {
        document.body.style.overflow = "";
        const headerWrapper = document.querySelector(".header-wrapper");
        if (headerWrapper) {
          headerWrapper.removeAttribute("style");
        }
      }
    };
  }, [isOpen]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsOpen(false);
        document.body.style.overflow = "";
        // Restore header z-index
        const headerWrapper = document.querySelector(".header-wrapper");
        if (headerWrapper) {
          headerWrapper.removeAttribute("style");
        }
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscKey);
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [isOpen]);

  // Add event listener to the hamburger button
  useEffect(() => {
    const hamburgerButton = document.getElementById("hamburger-button");

    if (hamburgerButton) {
      hamburgerButton.addEventListener("click", toggleMenu);
    }

    return () => {
      if (hamburgerButton) {
        hamburgerButton.removeEventListener("click", toggleMenu);
      }
    };
  }, [isOpen]);

  return (
    <div ref={menuRef} style={{ position: "relative", zIndex: 200 }}>
      {/* Full-screen menu overlay */}
      <div
        className={`hamburger-menu-overlay ${isOpen ? "open" : ""}`}
        aria-hidden={!isOpen}
      >
        <div className="hamburger-menu-container">
          <div className="hamburger-menu-header">
            <button
              className="hamburger-close-button"
              onClick={toggleMenu}
              aria-label="Close menu"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <nav className="hamburger-menu-nav">
            <ul>
              {navItems.map((item) => (
                <li key={item.href}>
                  <a
                    href={item.href}
                    className={currentPath === item.href ? "active" : ""}
                  >
                    {item.label}
                  </a>
                </li>
              ))}
            </ul>
          </nav>

          <div className="hamburger-menu-footer">
            <div className="hamburger-menu-social">
              <a href="#" aria-label="Instagram">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
              </a>
              <a href="#" aria-label="Facebook">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                </svg>
              </a>
              <a href="#" aria-label="Twitter">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                </svg>
              </a>
            </div>
            <div className="hamburger-menu-contact">
              <a href="tel:+1234567890">+1 (234) 567-890</a>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HamburgerMenu;
