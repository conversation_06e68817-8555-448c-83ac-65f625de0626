import React from "react";

interface FlagIconProps {
  className?: string;
}

export const UKFlag: React.FC<FlagIconProps> = ({ className = "w-5 h-5" }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 60 30"
    className={className}
    aria-label="United Kingdom"
  >
    <clipPath id="uk">
      <path d="M0 0v30h60V0z" />
    </clipPath>
    <g clipPath="url(#uk)">
      <path fill="#012169" d="M0 0v30h60V0z" />
      <path stroke="#fff" strokeWidth="6" d="M0 0l60 30m0-30L0 30" />
      <path
        stroke="#C8102E"
        strokeWidth="4"
        d="M0 0l60 30m0-30L0 30"
        clipPath="url(#uk)"
      />
      <path stroke="#fff" strokeWidth="10" d="M30 0v30M0 15h60" />
      <path stroke="#C8102E" strokeWidth="6" d="M30 0v30M0 15h60" />
    </g>
  </svg>
);

export const FranceFlag: React.FC<FlagIconProps> = ({
  className = "w-5 h-5",
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 900 600"
    className={className}
    aria-label="France"
  >
    <rect width="900" height="600" fill="#ED2939" />
    <rect width="600" height="600" fill="#fff" />
    <rect width="300" height="600" fill="#002395" />
  </svg>
);

export const GermanyFlag: React.FC<FlagIconProps> = ({
  className = "w-5 h-5",
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 5 3"
    className={className}
    aria-label="Germany"
  >
    <rect width="5" height="3" y="0" x="0" fill="#000" />
    <rect width="5" height="2" y="1" x="0" fill="#D00" />
    <rect width="5" height="1" y="2" x="0" fill="#FFCE00" />
  </svg>
);

export const ItalyFlag: React.FC<FlagIconProps> = ({
  className = "w-5 h-5",
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 3 2"
    className={className}
    aria-label="Italy"
  >
    <rect width="1" height="2" x="0" y="0" fill="#008C45" />
    <rect width="1" height="2" x="1" y="0" fill="#F4F5F0" />
    <rect width="1" height="2" x="2" y="0" fill="#CD212A" />
  </svg>
);

export const JapanFlag: React.FC<FlagIconProps> = ({
  className = "w-5 h-5",
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 900 600"
    className={className}
    aria-label="Japan"
  >
    <rect width="900" height="600" fill="#FFFFFF" />
    <circle cx="450" cy="300" r="180" fill="#BC002D" />
  </svg>
);
