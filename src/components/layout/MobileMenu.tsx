import React, { useState, useEffect } from "react";
import LanguageSelector from "./LanguageSelector";
import SafeWishlistIcon from "../wishlist/SafeWishlistIcon";
import SafeCartIcon from "../cart/SafeCartIcon";
import MobileDropdown from "../user/MobileDropdown";

interface NavItem {
  label: string;
  href: string;
}

interface MobileMenuProps {
  navItems: NavItem[];
  currentPath: string;
}

const MobileMenu: React.FC<MobileMenuProps> = ({ navItems, currentPath }) => {
  const [isOpen, setIsOpen] = useState(false);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isOpen && !target.closest(".mobile-menu-container")) {
        setIsOpen(false);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [isOpen]);

  // Close menu when window is resized to desktop size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768 && isOpen) {
        setIsOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [isOpen]);

  // Prevent scrolling when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
      document.body.classList.add("mobile-menu-open");
    } else {
      document.body.style.overflow = "";
      document.body.classList.remove("mobile-menu-open");
    }

    return () => {
      document.body.style.overflow = "";
      document.body.classList.remove("mobile-menu-open");
    };
  }, [isOpen]);

  return (
    <div className="mobile-menu-container md:hidden">
      {/* Hamburger Button */}
      <button
        className="flex flex-col justify-center items-center w-8 h-8 space-y-1.5 focus:outline-none"
        onClick={(e) => {
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
        aria-label="Toggle menu"
        aria-expanded={isOpen}
      >
        <span
          className={`hamburger-line block w-6 h-0.5 bg-foreground transition-transform duration-300 ease-in-out ${
            isOpen ? "rotate-45 translate-y-2" : ""
          }`}
        ></span>
        <span
          className={`hamburger-line block w-6 h-0.5 bg-foreground transition-opacity duration-300 ease-in-out ${
            isOpen ? "opacity-0" : "opacity-100"
          }`}
        ></span>
        <span
          className={`hamburger-line block w-6 h-0.5 bg-foreground transition-transform duration-300 ease-in-out ${
            isOpen ? "-rotate-45 -translate-y-2" : ""
          }`}
        ></span>
      </button>

      {/* Mobile Menu Overlay */}
      <div
        className={`mobile-menu-overlay fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300 md:hidden ${
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        onClick={() => setIsOpen(false)}
      ></div>

      {/* Mobile Menu Panel */}
      <div
        className={`mobile-menu-panel fixed top-[61px] right-0 w-4/5 max-w-sm h-[calc(100vh-61px)] bg-background border-l border-border z-50 transform transition-transform duration-300 ease-in-out overflow-y-auto md:hidden ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <nav className="flex flex-col p-6">
          {navItems.map((item, index) => (
            <a
              key={index}
              href={item.href}
              className={`mobile-menu-item font-karla text-sm uppercase tracking-[0.05em] py-4 relative ${
                index < navItems.length - 1 ? "border-b border-border/30" : ""
              } ${
                currentPath === item.href
                  ? "text-[#285DA6] font-medium border-l-2 border-[#285DA6] pl-3"
                  : "text-foreground hover:text-[#285DA6] hover:border-l-2 hover:border-[#285DA6] hover:pl-3 transition-all"
              }`}
              onClick={() => setIsOpen(false)}
            >
              {item.label}
            </a>
          ))}

          {/* Wishlist, Cart, Search in Mobile Menu */}
          <div className="mt-6 pt-4 border-t border-border/30">
            <div className="flex flex-col gap-4">
              <div className="flex items-center justify-between">
                <span className="font-karla text-sm text-foreground/70">
                  Search
                </span>
                <a
                  href="/search"
                  className="flex items-center justify-center w-8 h-8 rounded-full bg-white/90 shadow-sm hover:shadow-md border border-gray-100/50 hover:border-blue-100/50 hover:text-[#285DA6] transition-all duration-300"
                  onClick={() => setIsOpen(false)}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                  </svg>
                </a>
              </div>

              <div className="flex items-center justify-between">
                <span className="font-karla text-sm text-foreground/70">
                  Wishlist
                </span>
                <SafeWishlistIcon />
              </div>

              <div className="flex items-center justify-between">
                <span className="font-karla text-sm text-foreground/70">
                  Cart
                </span>
                <SafeCartIcon />
              </div>

              <div className="flex items-center justify-between">
                <span className="font-karla text-sm text-foreground/70">
                  Language
                </span>
                <LanguageSelector />
              </div>

              <div className="flex items-center justify-between">
                <span className="font-karla text-sm text-foreground/70">
                  Account
                </span>
                {/* Use the mobile dropdown component */}
                <MobileDropdown />
              </div>
            </div>
          </div>
        </nav>
      </div>
    </div>
  );
};

export default MobileMenu;
