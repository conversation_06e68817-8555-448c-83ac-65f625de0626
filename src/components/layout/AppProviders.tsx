import React, { type ReactNode } from "react";
import { WishlistProvider } from "../wishlist/WishlistContext";
import { CartProvider } from "../cart/CartContext";
import { UserProvider } from "../../contexts/UserContext";
import { CopilotKit } from "@copilotkit/react-core";
import AIHelpModalController from "../ai-search/AIHelpModalController";

interface AppProvidersProps {
  children: ReactNode;
}

const COPILOT_API_KEY = "ck_pub_500a447fa9899d90759a9bbe408414d6";

const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <UserProvider>
      <WishlistProvider>
        <CartProvider>
          <CopilotKit publicApiKey={COPILOT_API_KEY}>
            <>
              <AIHelpModalController />
              {children}
            </>
          </CopilotKit>
        </CartProvider>
      </WishlistProvider>
    </UserProvider>
  );
};

export default AppProviders;
