---
import LanguageSelector from "./LanguageSelector";
import SimpleDropdown from "../user/SimpleDropdown";
import HamburgerMenu from "./HamburgerMenu";
import UserProvider from "../auth/UserProvider";
import ExpandableHeaderSearch from "../search/ExpandableHeaderSearch";

const path = Astro.url.pathname;
const isHomePage = path === "/";
const isAISearchPage = path === "/ai-search";
const isSearchPage = path === "/search";

// Check if we're on the search page with AI search parameter
const searchParams = Astro.url.searchParams;
const isAISearchMode = searchParams.get("ai_search") === "true";

// Navigation items to be passed to the hamburger menu
const navItems = [
  {
    label: "Search",
    href: "/search",
  },
  {
    label: "Inspiration",
    href: "/inspiration",
  },
  {
    label: "Destinations",
    href: "/destinations",
  },
  {
    label: "About",
    href: "/about",
  },
];
---

<!-- Hamburger Menu Component -->
<HamburgerMenu navItems={navItems} currentPath={path} client:load />

<div
  class={`header-wrapper fixed w-full top-0 z-[101] border-none shadow-none ${isHomePage ? "homepage-header" : ""}`}
>
  <!-- Expandable Search Component -->
  <div
    id="expandable-search-container"
    class={`expandable-search-wrapper ${isHomePage ? "on-homepage" : "search-visible"}`}
  >
    <ExpandableHeaderSearch
      client:load
      isVisible={!isHomePage}
      isExpandedView={isAISearchPage || isAISearchMode}
    />
  </div>

  <!-- Primary Header -->
  <header
    class="primary-header perfect-piste-header py-4 transition-all duration-300 relative border-none shadow-none bg-transparent"
    data-astro-cid-eb5n4ob6
  >
    <div class="header-container" data-astro-cid-eb5n4ob6>
      <div class="flex justify-between">
        <!-- Left Section: Logo -->
        <div class="flex items-center">
          <a href="/" class="flex items-center">
            <img
              src="/images/perfect-piste-logo.png"
              alt="Perfect Piste Logo"
              class="h-10"
            />
          </a>
        </div>

        <!-- Right Section: Language, User Avatar, Search, Hamburger -->
        <div class="flex items-center space-x-5">
          <!-- Desktop Navigation Items -->
          <div class="hidden md:flex items-center space-x-5">
            <!-- Language Selector -->
            <div class="premium-icon-container">
              <LanguageSelector client:load />
            </div>

            <!-- Wishlist Heart Icon -->
            <a
              href="/wishlist"
              class="flex items-center justify-center transition-all duration-300 relative group wishlist-link"
            >
              <div
                class="premium-icon-container flex items-center justify-center"
              >
                <svg
                  id="wishlist-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="heart-icon"
                  aria-hidden="true"
                >
                  <path
                    d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
                  ></path>
                </svg>
                <span class="sr-only">Wishlist</span>
                <!-- Wishlist Badge - This will be shown dynamically when items are in wishlist -->
                <!-- <span
                  id="wishlist-badge"
                  class="absolute -top-1 -right-1 bg-[#285DA6] text-white text-xs w-5 h-5 items-center justify-center rounded-full hidden wishlist-badge shadow-md border border-white/80"
                  >0</span
                > -->
              </div>
            </a>

            <!-- User Profile -->
            <div class="premium-icon-wrapper">
              <UserProvider>
                <SimpleDropdown client:load />
              </UserProvider>
            </div>
          </div>

          <!-- Hamburger Menu -->
          <button
            id="hamburger-button"
            class="premium-icon-container flex items-center justify-center"
            aria-label="Open menu"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="hamburger-icon"
              aria-hidden="true"
            >
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </header>
</div>

<style>
  /* Header styles to match search page */
  .perfect-piste-header[data-astro-cid-eb5n4ob6] {
    padding: 20px 0;
    transition: all 0.3s ease;
    position: relative;
    height: 85px;
    box-sizing: border-box;
    border: none; /* Remove border */
    box-shadow: none; /* Remove shadow */
  }

  /* All header styles - transparent by default */
  .perfect-piste-header[data-astro-cid-eb5n4ob6] {
    background-color: transparent;
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }

  .header-container[data-astro-cid-eb5n4ob6] {
    display: flex;
    flex-direction: column;
    max-width: 1760px;
    margin: 0 auto;
    padding: 0 60px;
    height: 100%;
    box-sizing: border-box;
  }

  @media (max-width: 768px) {
    .header-container[data-astro-cid-eb5n4ob6] {
      padding: 0 16px;
    }

    .perfect-piste-header[data-astro-cid-eb5n4ob6] {
      padding: 20px 0;
      height: auto;
      min-height: 85px;
    }
  }

  /* Expanded header state */
  .primary-header.expanded-header.perfect-piste-header[data-astro-cid-eb5n4ob6] {
    height: 132px !important;
    border: none !important;
    box-shadow: none !important;
  }

  /* Scrolled header state */
  .primary-header.header-scrolled.perfect-piste-header[data-astro-cid-eb5n4ob6] {
    height: 85px;
    padding: 20px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
    border-bottom: none !important;
    transition: all 0.3s ease;
  }

  /* White background class added via JavaScript when scrolled */
  .primary-header.bg-white {
    background-color: white !important;
    -webkit-backdrop-filter: blur(8px) !important;
    backdrop-filter: blur(8px) !important;
  }

  /* Logo in transparent header */
  .homepage-header:not(.header-scrolled) .primary-header .logo-container img,
  .homepage-header:not(.header-scrolled) .primary-header .logo-container svg {
    filter: brightness(0) invert(1); /* Make logo white */
  }

  /* Elegant icon container styles */
  .premium-icon-container {
    position: relative;
    width: 38px;
    height: 38px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    transition: all 0.3s ease;
    border: 1px solid rgba(53, 102, 171, 0.2);
  }

  /* White borders only on homepage initially */
  .homepage-header:not(.header-scrolled) .premium-icon-container {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  /* Ensure consistent styling for all icons */
  .premium-icon-wrapper button.premium-icon-container {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background: transparent;
    border: 1px solid rgba(53, 102, 171, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    cursor: pointer;
  }

  /* White borders only on homepage initially */
  .homepage-header:not(.header-scrolled)
    .premium-icon-wrapper
    button.premium-icon-container {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  /* Wishlist link colors */
  .wishlist-link {
    color: #3566ab;
  }

  .wishlist-link:hover {
    color: #3566ab;
  }

  .homepage-header:not(.header-scrolled) .wishlist-link {
    color: white;
  }

  .homepage-header:not(.header-scrolled) .wishlist-link:hover {
    color: white;
  }

  .header-scrolled .wishlist-link {
    color: #3566ab;
  }

  /* Icon styles */
  .heart-icon,
  .hamburger-icon,
  .user-icon {
    color: #3566ab;
    stroke: #3566ab;
    transition: all 0.3s ease;
    stroke-width: 1.5;
  }

  /* White icons only on homepage initially */
  .homepage-header:not(.header-scrolled) .heart-icon,
  .homepage-header:not(.header-scrolled) .hamburger-icon,
  .homepage-header:not(.header-scrolled) .user-icon {
    color: white;
    stroke: white;
  }

  /* Scrolled state for icons */
  .header-scrolled .premium-icon-container,
  .header-scrolled button.premium-icon-container,
  .header-scrolled .premium-icon-wrapper button.premium-icon-container {
    border-color: rgba(53, 102, 171, 0.2);
    background-color: rgba(255, 255, 255, 0.8);
  }

  .header-scrolled .premium-icon-container svg,
  .header-scrolled button.premium-icon-container svg,
  .header-scrolled .premium-icon-wrapper button.premium-icon-container svg {
    color: #3566ab;
    stroke: #3566ab;
  }

  /* Hover effects */
  .premium-icon-container:hover,
  button.premium-icon-container:hover,
  .premium-icon-wrapper button.premium-icon-container:hover {
    border-color: rgba(53, 102, 171, 0.5);
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
  }

  .premium-icon-container:hover .heart-icon,
  .premium-icon-container:hover .hamburger-icon,
  .premium-icon-container:hover .user-icon,
  button.premium-icon-container:hover .user-icon,
  .premium-icon-wrapper button.premium-icon-container:hover .user-icon {
    transform: scale(1.05);
  }

  .header-scrolled .premium-icon-container:hover,
  .header-scrolled button.premium-icon-container:hover,
  .header-scrolled .premium-icon-wrapper button.premium-icon-container:hover {
    background-color: rgba(255, 255, 255, 0.95);
    border-color: rgba(53, 102, 171, 0.8);
    box-shadow: 0 2px 8px rgba(53, 102, 171, 0.15);
  }

  /* Active state */
  .premium-icon-container:active,
  button.premium-icon-container:active,
  .premium-icon-wrapper button.premium-icon-container:active {
    transform: translateY(0);
    transition: all 0.1s ease;
  }

  .header-scrolled .premium-icon-container:active,
  .header-scrolled button.premium-icon-container:active,
  .header-scrolled .premium-icon-wrapper button.premium-icon-container:active {
    background-color: rgba(53, 102, 171, 0.05);
  }

  /* Premium icon wrapper */
  .premium-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Additional styles for specific elements if needed */

  /* End of header icon styles */

  /* Add pulse animation for wishlist badge */
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  .wishlist-badge.flex {
    animation: pulse 1.5s infinite;
    box-shadow: 0 0 0 rgba(40, 93, 166, 0.4);
  }
</style>

<script>
  // Script to handle wishlist badge
  document.addEventListener("DOMContentLoaded", () => {
    // Function to update wishlist badge
    const updateWishlistBadge = () => {
      const wishlistBadge = document.getElementById("wishlist-badge");
      if (wishlistBadge) {
        try {
          // Try to get wishlist items from localStorage
          const wishlistItems = JSON.parse(
            localStorage.getItem("wishlist") || "[]"
          );
          const count = wishlistItems.length;

          // Update badge
          wishlistBadge.textContent = count.toString();

          // Show/hide badge based on count
          if (count > 0) {
            wishlistBadge.classList.remove("hidden");
            wishlistBadge.classList.add("flex");
          } else {
            wishlistBadge.classList.add("hidden");
            wishlistBadge.classList.remove("flex");
          }
        } catch (error) {
          console.error("Error updating wishlist badge:", error);
          wishlistBadge.classList.add("hidden");
        }
      }
    };

    // Update badge on page load
    updateWishlistBadge();

    // Listen for storage events to update badge when wishlist changes
    window.addEventListener("storage", (event) => {
      if (event.key === "wishlist") {
        updateWishlistBadge();
      }
    });

    // Custom event for wishlist updates within the same page
    document.addEventListener("wishlistUpdated", updateWishlistBadge);
  });

  // Script to handle header styling on scroll
  document.addEventListener("DOMContentLoaded", () => {
    // Add a global event listener for searchModeChanged events
    document.addEventListener("searchModeChanged", () => {});
    const primaryHeader = document.querySelector(".primary-header");
    const expandableSearchContainer = document.getElementById(
      "expandable-search-container"
    );
    const headerWrapper = document.querySelector(".header-wrapper");

    // Get the expandable search component instance
    const expandableSearch = document.querySelector("[data-expandable-search]");

    // Check if we're on the homepage, search page, or AI search page
    const isHomePage = window.location.pathname === "/";
    const isSearchPage = window.location.pathname === "/search";
    const isAISearchPage = window.location.pathname === "/ai-search";

    // Set user icon color explicitly to ensure it's correct in all states
    const setUserIconColor = (scrolled: boolean) => {
      const userIcon = document.getElementById("user-profile-icon");
      if (userIcon) {
        if (scrolled || !isHomePage) {
          userIcon.setAttribute("stroke", "#3566ab");
        } else if (isHomePage && !scrolled) {
          userIcon.setAttribute("stroke", "white");
        }
      }
    };

    // Check if we're on the search page with AI search parameter
    const searchParams = new URLSearchParams(window.location.search);
    const isAISearchMode = searchParams.get("ai_search") === "true";

    // Handle header styling on scroll
    const handleScroll = () => {
      const scrolled = window.scrollY > 50; // Increased threshold for better UX
      const heroHeight = 370; // Approximate height of hero section
      const pastHero = window.scrollY > heroHeight;

      // Add scrolled class to primary header for styling
      if (scrolled) {
        primaryHeader?.classList.add("header-scrolled");
        headerWrapper?.classList.add("header-scrolled");

        // Add bg-white class when scrolled
        primaryHeader?.classList.add("bg-white");
        primaryHeader?.classList.add("backdrop-blur-md");

        // Add bg-white to nav items
        document
          .querySelectorAll(
            ".header-wrapper .rounded-full, .header-wrapper .rounded-md"
          )
          .forEach((item) => {
            item.classList.add("bg-white/90");
          });

        // Update user icon color when scrolled
        setUserIconColor(true);

        // Remove expanded header class if needed, but only if not on search or AI search page
        if (!(isSearchPage || isAISearchPage || isAISearchMode)) {
          primaryHeader?.classList.remove("expanded-header");
          headerWrapper?.classList.remove("header-expanded");
        }

        // Reset search bar to default state when scrolling
        if (expandableSearchContainer && expandableSearch) {
          // Reset search container expanded state
          expandableSearchContainer.classList.remove("expanded");

          // Handle homepage-specific behavior
          if (isHomePage) {
            // Show search when scrolled past hero section
            if (pastHero) {
              expandableSearchContainer.classList.add("search-visible");
              expandableSearchContainer.classList.remove("on-homepage");
              expandableSearch.setAttribute("data-visible", "true");
            } else {
              // Keep search hidden when in hero section
              expandableSearchContainer.classList.remove("search-visible");
              expandableSearchContainer.classList.add("on-homepage");
              expandableSearch.setAttribute("data-visible", "false");
            }
          } else {
            // For non-homepage, always show search
            expandableSearchContainer.classList.add("search-visible");
            expandableSearch.classList.remove("hidden");
            expandableSearch.classList.add("visible");
            expandableSearch.setAttribute("data-visible", "true");
          }

          // Reset search input
          const searchInput = document.querySelector(
            ".search-input"
          ) as HTMLInputElement;
          if (searchInput) {
            searchInput.blur();
          }

          // Reset search tabs
          const searchTabs = document.querySelectorAll(".search-tab");
          searchTabs.forEach((tab) => {
            tab.classList.remove("active");
          });

          // Reset any active search filters
          const searchFilters = document.querySelectorAll(".search-filter");
          searchFilters.forEach((filter) => {
            filter.classList.remove("active");
          });
        }
      } else {
        primaryHeader?.classList.remove("header-scrolled");
        headerWrapper?.classList.remove("header-scrolled");

        // Remove bg-white class when not scrolled
        primaryHeader?.classList.remove("bg-white");
        primaryHeader?.classList.remove("backdrop-blur-md");

        // Remove bg-white from nav items
        document
          .querySelectorAll(
            ".header-wrapper .rounded-full, .header-wrapper .rounded-md"
          )
          .forEach((item) => {
            item.classList.remove("bg-white/90");
          });

        // Reset user icon color when not scrolled
        setUserIconColor(false);

        // Handle homepage-specific behavior when not scrolled
        if (isHomePage && expandableSearchContainer && expandableSearch) {
          // Keep search hidden on homepage when at the top
          expandableSearchContainer.classList.remove("search-visible");
          expandableSearchContainer.classList.add("on-homepage");
          expandableSearch.setAttribute("data-visible", "false");
        }
      }

      // For non-homepage, always keep expandable search visible
      if (!isHomePage && expandableSearchContainer && expandableSearch) {
        expandableSearchContainer.classList.add("search-visible");
        expandableSearch.setAttribute("data-visible", "true");
      }
    };

    // Set initial visibility for expandable search
    if (expandableSearchContainer) {
      if (isHomePage) {
        // Initially hide search on homepage
        expandableSearchContainer.classList.remove("search-visible");
        expandableSearchContainer.classList.add("on-homepage");
        if (expandableSearch) {
          expandableSearch.setAttribute("data-visible", "false");
        }
      } else {
        // Show search on all other pages
        expandableSearchContainer.classList.add("search-visible");
        if (expandableSearch) {
          expandableSearch.setAttribute("data-visible", "true");
        }

        // If on search or AI search page, ensure the header is expanded
        if (isSearchPage || isAISearchPage || isAISearchMode) {
          headerWrapper?.classList.add("header-expanded");
          primaryHeader?.classList.add("expanded-header");
        }
      }
    }

    // Initial check
    handleScroll();

    // Set initial user icon color based on scroll position and page type
    setUserIconColor(window.scrollY > 50 || !isHomePage);

    // For non-homepage, ensure icons are blue from the start
    if (!isHomePage) {
      document
        .querySelectorAll(".heart-icon, .hamburger-icon, .user-icon")
        .forEach((icon) => {
          icon.setAttribute("stroke", "#3566ab");
        });

      document
        .querySelectorAll(".premium-icon-container")
        .forEach((container) => {
          (container as HTMLElement).style.borderColor =
            "rgba(53, 102, 171, 0.2)";
        });
    }

    // Add scroll listener with throttling for better performance
    let isScrolling = false;
    window.addEventListener("scroll", () => {
      if (!isScrolling) {
        window.requestAnimationFrame(() => {
          handleScroll();
          isScrolling = false;
        });
        isScrolling = true;
      }
    });

    // Handle click outside to close expanded search
    document.addEventListener("click", (event) => {
      // Check if header is in expanded state and not on search or AI search page
      if (
        headerWrapper?.classList.contains("header-expanded") &&
        !(isSearchPage || isAISearchPage || isAISearchMode)
      ) {
        // Check if click is outside the search container
        const target = event.target as HTMLElement;
        if (
          !expandableSearchContainer?.contains(target) &&
          !target.closest("[data-expandable-search]")
        ) {
          // Close the expanded search
          headerWrapper.classList.remove("header-expanded");

          // Also remove expanded-header class from primary header
          const primaryHeader = document.querySelector(".primary-header");
          if (primaryHeader) {
            primaryHeader.classList.remove("expanded-header");
          }
        }
      }
    });

    // Listen for escape key to close expanded search
    document.addEventListener("keydown", (event) => {
      if (
        event.key === "Escape" &&
        headerWrapper?.classList.contains("header-expanded") &&
        !(isAISearchPage || isAISearchMode)
      ) {
        headerWrapper.classList.remove("header-expanded");

        // Also remove expanded-header class from primary header
        const primaryHeader = document.querySelector(".primary-header");
        if (primaryHeader) {
          primaryHeader.classList.remove("expanded-header");
        }
      }
    });
  });
</script>
