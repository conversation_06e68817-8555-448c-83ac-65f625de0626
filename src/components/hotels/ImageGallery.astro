---
interface Props {
  hotelName: string;
  images: string[];
}

const { hotelName, images } = Astro.props;
---

<section id="gallery" class="py-16 bg-background/50">
  <div class="container-custom">
    <div class="text-center mb-10">
      <p class="section-micro-headline">Photo Gallery</p>
      <h2 class="section-title">Experience the Luxury</h2>
    </div>

    <div class="relative">
      <!-- Main Carousel -->
      <div class="carousel w-full">
        <div class="carousel-container overflow-hidden rounded-lg shadow-glow">
          <div
            class="carousel-track flex transition-transform duration-500"
            id="image-track"
          >
            {
              images.map((image, index) => (
                <div class="carousel-item flex-none w-full">
                  <div class="h-[60vh]">
                    <img
                      src={image}
                      alt={`${hotelName} - Image ${index + 1}`}
                      class="w-full h-full object-cover"
                    />
                  </div>
                </div>
              ))
            }
          </div>
        </div>

        <!-- Carousel Controls -->
        <button
          class="carousel-prev absolute top-1/2 left-4 -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white rounded-full p-3 shadow-md z-10 transition-all duration-300"
          aria-label="Previous slide"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M15 18l-6-6 6-6"></path>
          </svg>
        </button>

        <button
          class="carousel-next absolute top-1/2 right-4 -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white rounded-full p-3 shadow-md z-10 transition-all duration-300"
          aria-label="Next slide"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M9 18l6-6-6-6"></path>
          </svg>
        </button>
      </div>

      <!-- Thumbnail Gallery -->
      <div class="mt-4 overflow-hidden">
        <div class="flex gap-2 overflow-x-auto pb-2 carousel-thumbnails">
          {
            images.map((image, index) => (
              <button
                class="carousel-thumbnail flex-none w-24 h-16 rounded-lg overflow-hidden border-2 border-transparent hover:border-primary transition-all duration-300 focus:outline-none focus:border-primary"
                data-index={index}
                aria-label={`Go to slide ${index + 1}`}
              >
                <img
                  src={image}
                  alt={`${hotelName} - Thumbnail ${index + 1}`}
                  class="w-full h-full object-cover"
                />
              </button>
            ))
          }
        </div>
      </div>

      <!-- Carousel Indicators (dots) -->
      <div class="carousel-indicators flex justify-center mt-4 gap-2">
        {
          images.map((_, index) => (
            <button
              class="carousel-indicator w-3 h-3 rounded-full bg-foreground/20 transition-colors"
              data-index={index}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))
        }
      </div>
    </div>
  </div>
</section>
