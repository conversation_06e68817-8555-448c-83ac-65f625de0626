import React from "react";
import { formatDate } from "../../utils/dateUtils";

interface SearchSummaryProps {
  hotelName: string;
  location: string;
  checkInDate: Date | null;
  checkOutDate: Date | null;
  guests: {
    adults: number;
    children: number;
    infants: number;
  };
  nights: number;
  onOpenDatePicker: () => void;
}

const SearchSummary: React.FC<SearchSummaryProps> = ({
  hotelName,
  location,
  checkInDate,
  checkOutDate,
  guests,
  nights,
  onOpenDatePicker,
}) => {
  // Format the dates for display
  const formattedCheckIn = checkInDate ? formatDate(checkInDate) : "Select date";
  const formattedCheckOut = checkOutDate ? formatDate(checkOutDate) : "Select date";

  // Calculate total guests
  const totalGuests = guests.adults + guests.children + guests.infants;
  
  // Format guest text
  const guestText = `${totalGuests} ${totalGuests === 1 ? "Guest" : "Guests"}`;
  const guestDetails = `${guests.adults} ${guests.adults === 1 ? "Adult" : "Adults"}${
    guests.children > 0 ? `, ${guests.children} ${guests.children === 1 ? "Child" : "Children"}` : ""
  }${guests.infants > 0 ? `, ${guests.infants} ${guests.infants === 1 ? "Infant" : "Infants"}` : ""}`;

  return (
    <div className="bg-white border border-[#3566ab]/10 rounded-xl shadow-md mb-8">
      <div className="p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <h2 className="text-xl font-baskervville text-[#3566ab] mb-2 md:mb-0">
            Your Stay at {hotelName}
          </h2>
          <button
            onClick={onOpenDatePicker}
            className="px-4 py-2 bg-[#3566ab]/10 text-[#3566ab] rounded-lg hover:bg-[#3566ab]/20 transition-colors font-medium text-sm flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-2"
            >
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
            Change Dates
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-[#3566ab]/5 p-4 rounded-lg">
            <div className="flex items-center mb-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#3566ab] mr-2"
              >
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              <span className="text-sm font-medium">Location</span>
            </div>
            <p className="text-foreground/80">{location}</p>
          </div>

          <div className="bg-[#3566ab]/5 p-4 rounded-lg">
            <div className="flex items-center mb-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#3566ab] mr-2"
              >
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
              <span className="text-sm font-medium">Dates</span>
            </div>
            <p className="text-foreground/80">
              {formattedCheckIn} - {formattedCheckOut}
            </p>
            <p className="text-xs text-foreground/60 mt-1">{nights} nights</p>
          </div>

          <div className="bg-[#3566ab]/5 p-4 rounded-lg">
            <div className="flex items-center mb-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#3566ab] mr-2"
              >
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
              <span className="text-sm font-medium">Guests</span>
            </div>
            <p className="text-foreground/80">{guestText}</p>
            <p className="text-xs text-foreground/60 mt-1">{guestDetails}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchSummary;
