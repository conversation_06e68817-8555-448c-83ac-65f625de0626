---
// This component contains all the JavaScript functionality for the hotel details page
---

<script>
  // Client-side functionality
  document.addEventListener("DOMContentLoaded", () => {
    // Image Carousel functionality
    const track = document.getElementById("image-track") as HTMLElement;
    const items = track?.querySelectorAll(".carousel-item");
    const prevBtn = document.querySelector(".carousel-prev");
    const nextBtn = document.querySelector(".carousel-next");
    const indicators = document.querySelectorAll(".carousel-indicator");
    let currentIndex = 0;
    const itemCount = items?.length || 0;

    // Set the first indicator as active
    if (indicators.length > 0) {
      indicators[0].classList.add("bg-primary");
    }

    // Function to update the carousel position
    const updateCarousel = (index: number) => {
      if (!track || !items) return;

      // Update the transform to show the current slide
      track.style.transform = `translateX(-${index * 100}%)`;

      // Update indicators
      indicators.forEach((indicator, i) => {
        if (i === index) {
          indicator.classList.remove("bg-foreground/20");
          indicator.classList.add("bg-primary");
        } else {
          indicator.classList.remove("bg-primary");
          indicator.classList.add("bg-foreground/20");
        }
      });

      currentIndex = index;
    };

    // Event listeners for prev/next buttons
    prevBtn?.addEventListener("click", () => {
      const newIndex = (currentIndex - 1 + itemCount) % itemCount;
      updateCarousel(newIndex);
    });

    nextBtn?.addEventListener("click", () => {
      const newIndex = (currentIndex + 1) % itemCount;
      updateCarousel(newIndex);
    });

    // Event listeners for indicators
    indicators.forEach((indicator, index) => {
      indicator.addEventListener("click", () => {
        updateCarousel(index);
      });
    });

    // Room collapsible functionality
    const collapsibleTriggers = document.querySelectorAll(
      ".collapsible-trigger"
    );

    collapsibleTriggers.forEach((trigger) => {
      trigger.addEventListener("click", () => {
        const collapsible = trigger.closest(".collapsible");
        if (!collapsible) return;

        const content = collapsible.querySelector(".collapsible-content");
        const showDetails = trigger.querySelector(".show-details");
        const hideDetails = trigger.querySelector(".hide-details");
        const chevronDown = trigger.querySelector(".chevron-down");
        const chevronUp = trigger.querySelector(".chevron-up");

        // Toggle content visibility
        content?.classList.toggle("hidden");

        // Toggle button text and icon
        showDetails?.classList.toggle("hidden");
        hideDetails?.classList.toggle("hidden");
        chevronDown?.classList.toggle("hidden");
        chevronUp?.classList.toggle("hidden");
      });
    });

    // Room carousel functionality
    const roomCarouselPrevBtns = document.querySelectorAll(
      ".room-carousel-prev"
    );
    const roomCarouselNextBtns = document.querySelectorAll(
      ".room-carousel-next"
    );
    const roomTracks = document.querySelectorAll(".room-carousel-track");

    // Initialize room carousel positions
    const roomPositions: Record<string, number> = {};
    roomTracks.forEach((track) => {
      const roomId = track.getAttribute("data-room-id");
      if (roomId) {
        roomPositions[roomId] = 0;
      }
    });

    roomCarouselPrevBtns.forEach((btn) => {
      btn.addEventListener("click", () => {
        const roomId = btn.getAttribute("data-room-id");
        if (!roomId) return;

        const track = document.querySelector(
          `.room-carousel-track[data-room-id="${roomId}"]`
        ) as HTMLElement;
        if (!track) return;

        const itemWidth = 100 / 3; // For lg:w-1/3

        if (roomPositions[roomId] > 0) {
          roomPositions[roomId]--;
          track.style.transform = `translateX(-${roomPositions[roomId] * itemWidth}%)`;
        }
      });
    });

    roomCarouselNextBtns.forEach((btn) => {
      btn.addEventListener("click", () => {
        const roomId = btn.getAttribute("data-room-id");
        if (!roomId) return;

        const track = document.querySelector(
          `.room-carousel-track[data-room-id="${roomId}"]`
        ) as HTMLElement;
        if (!track) return;

        // Get the items for calculating position
        const itemCount = track.querySelectorAll("div.flex-none").length;
        const itemWidth = 100 / 3; // For lg:w-1/3
        const maxPosition = Math.max(0, itemCount - 3);

        if (roomPositions[roomId] < maxPosition) {
          roomPositions[roomId]++;
          track.style.transform = `translateX(-${roomPositions[roomId] * itemWidth}%)`;
        }
      });
    });

    // Select room functionality
    const selectRoomButtons = document.querySelectorAll(".select-room-btn");
    const bookNowBtn = document.querySelector(".book-now-btn");

    // Set the first room as selected by default
    const firstRoomBtn = selectRoomButtons[0] as HTMLElement;
    if (firstRoomBtn) {
      const roomName = firstRoomBtn.getAttribute("data-room-name");
      const roomPrice = firstRoomBtn.getAttribute("data-room-price");

      // Update booking box with first room info
      const bookingBoxPrice = document.querySelector(".booking-box-price");
      const bookingBoxRoomType = document.querySelector(
        ".booking-box-room-type"
      );

      if (bookingBoxPrice && roomPrice) {
        bookingBoxPrice.textContent = `$${roomPrice}`;
      }

      if (bookingBoxRoomType && roomName) {
        bookingBoxRoomType.textContent = roomName;
      }

      // Mark first room as selected
      firstRoomBtn.classList.remove("bg-primary");
      firstRoomBtn.classList.add("bg-accent");
      firstRoomBtn.textContent = "Selected";
    }

    selectRoomButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const roomId = button.getAttribute("data-room-id");
        const roomName = button.getAttribute("data-room-name");
        const roomPrice = button.getAttribute("data-room-price");
        const roomCurrency = button.getAttribute("data-room-currency") || "USD";
        const maxAdults = button.getAttribute("data-max-adults") || "2";
        const maxChildren = button.getAttribute("data-max-children") || "0";
        const maxInfants = button.getAttribute("data-max-infants") || "0";

        // Get check-in and check-out dates from the page if available
        const checkInElement = document.querySelector(".check-in-date");
        const checkOutElement = document.querySelector(".check-out-date");

        const checkIn = checkInElement?.textContent?.trim() || "";
        const checkOut = checkOutElement?.textContent?.trim() || "";

        // If we have all the necessary data, redirect to review-booking
        if (roomId && roomPrice && checkIn && checkOut) {
          // Create URL for review booking page with all necessary parameters
          const summaryUrl = new URL("/review-booking", window.location.origin);

          // Get hotel ID from the URL
          const urlPath = window.location.pathname;
          const hotelId = urlPath.split("/").pop() || "";

          // Add all required parameters
          summaryUrl.searchParams.append("hotelId", hotelId);
          summaryUrl.searchParams.append("roomId", roomId);
          summaryUrl.searchParams.append("checkIn", checkIn);
          summaryUrl.searchParams.append("checkOut", checkOut);
          summaryUrl.searchParams.append("checkInTime", "14:00");
          summaryUrl.searchParams.append("checkOutTime", "11:00");
          summaryUrl.searchParams.append("totalAmount", roomPrice);
          summaryUrl.searchParams.append("currencyCode", roomCurrency);
          summaryUrl.searchParams.append("guestCount", maxAdults);
          summaryUrl.searchParams.append("infantCount", maxInfants);
          summaryUrl.searchParams.append("mealPlan", "none");
          summaryUrl.searchParams.append("roomQuantity", "1");

          // Redirect to the review booking page
          window.location.href = summaryUrl.toString();
          return;
        }

        // If we don't have all the necessary data, fall back to the original behavior
        // Update the booking box with selected room info
        const bookingBoxPrice = document.querySelector(".booking-box-price");
        const bookingBoxRoomType = document.querySelector(
          ".booking-box-room-type"
        );

        if (bookingBoxPrice && roomPrice) {
          bookingBoxPrice.textContent = `$${roomPrice}`;
        }

        if (bookingBoxRoomType && roomName) {
          bookingBoxRoomType.textContent = roomName;
        }

        // Update price summary
        const nightsElement = document.querySelector(".nights-total");
        const finalTotalElement = document.querySelector(".final-total");

        if (nightsElement && finalTotalElement && roomPrice) {
          const pricePerNight = parseFloat(roomPrice);
          const nights = 3; // Default to 3 nights
          const total = pricePerNight * nights;

          nightsElement.textContent = `$${total}`;
          finalTotalElement.textContent = `$${total}`;
        }

        // Highlight the selected room
        document.querySelectorAll(".select-room-btn").forEach((btn) => {
          btn.classList.remove("bg-accent");
          btn.classList.add("bg-primary");
          btn.textContent = "Select";
        });

        button.classList.remove("bg-primary");
        button.classList.add("bg-accent");
        button.textContent = "Selected";

        // Scroll to booking box on mobile
        if (window.innerWidth < 768) {
          const bookingBox = document.querySelector(".booking-box");
          if (bookingBox) {
            bookingBox.scrollIntoView({ behavior: "smooth" });
          }
        }
      });
    });

    // Book Now button functionality
    bookNowBtn?.addEventListener("click", () => {
      // Get the selected room data
      const selectedRoomBtn = document.querySelector(
        ".select-room-btn.bg-accent"
      );

      if (selectedRoomBtn) {
        // Trigger a click on the selected room button to use its redirect logic
        selectedRoomBtn.dispatchEvent(new Event("click"));
      } else {
        // If no room is selected, show a message
        alert("Please select a room first.");
      }
    });
  });
</script>
