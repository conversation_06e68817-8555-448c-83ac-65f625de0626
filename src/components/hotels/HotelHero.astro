---
interface Props {
  name: string;
  location: string;
  rating: number;
  mainImage: string;
}

const { name, location, rating, mainImage } = Astro.props;
---

<div class="container-custom">
  <section class="relative h-[80vh] overflow-hidden mb-16 rounded-lg">
    <div class="absolute inset-0 w-full h-full">
      <img
        src={mainImage}
        alt={name}
        class="w-full h-full object-cover rounded-lg"
      />
      <div
        class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"
      >
      </div>
    </div>

    <div class="absolute inset-0 flex items-center justify-center">
      <div class="container-custom text-center max-w-4xl px-4">
        <div class="animate-fade-in">
          <h1
            class="text-4xl md:text-5xl lg:text-7xl text-white font-baskervville mb-6 leading-tight"
          >
            {name}
          </h1>

          <p
            class="text-xl text-white/90 mb-8 animate-fade-in animate-delay-200 max-w-2xl mx-auto"
          >
            Experience luxury in the heart of {location}
          </p>

          <div
            class="flex items-center justify-center gap-2 mb-8 animate-fade-in animate-delay-200"
          >
            {
              Array.from({ length: 5 }).map((_, i) => (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill={
                    i < Math.floor(rating)
                      ? "currentColor"
                      : i === Math.floor(rating) && rating % 1 > 0
                        ? "url(#half-star)"
                        : "none"
                  }
                  stroke="currentColor"
                  stroke-width="1"
                  class={
                    i < Math.floor(rating) ||
                    (i === Math.floor(rating) && rating % 1 > 0)
                      ? "text-primary"
                      : "text-white/30"
                  }
                >
                  <defs>
                    <linearGradient
                      id="half-star"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="0%"
                    >
                      <stop offset="50%" stop-color="currentColor" />
                      <stop
                        offset="50%"
                        stop-color="transparent"
                        stop-opacity="1"
                      />
                    </linearGradient>
                  </defs>
                  <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
                </svg>
              ))
            }
          </div>

          <div
            class="flex flex-wrap gap-4 justify-center animate-fade-in animate-delay-400"
          >
            <a
              href="#booking"
              class="btn-primary py-4 px-10 text-base rounded-full"
            >
              Book Your Stay
            </a>
            <a
              href="#gallery"
              class="btn-outline text-white border-white hover:bg-white/10 py-4 px-10 text-base rounded-full"
            >
              Explore Property
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="absolute bottom-0 left-0 right-0 flex justify-center pb-8">
      <a
        href="#gallery"
        class="w-12 h-12 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center text-white border border-white/20 animate-bounce hover:bg-white/20 transition-colors"
        aria-label="Scroll down"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="m6 9 6 6 6-6"></path>
        </svg>
      </a>
    </div>
  </section>

  <style>
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .animate-fade-in {
      animation: fadeIn 0.8s ease-out forwards;
      opacity: 0;
    }

    .animate-delay-200 {
      animation-delay: 0.2s;
    }

    .animate-delay-400 {
      animation-delay: 0.4s;
    }

    @keyframes bounce {
      0%,
      100% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-10px);
      }
    }

    .animate-bounce {
      animation: bounce 2s infinite;
    }
  </style>
</div>
