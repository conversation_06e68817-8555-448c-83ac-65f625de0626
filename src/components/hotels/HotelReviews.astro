---
interface Review {
  author: string;
  date: string;
  rating: number;
  comment: string;
  avatar?: string;
}

interface Props {
  hotelName: string;
  reviews?: Review[];
}

const defaultReviews: Review[] = [
  {
    author: "<PERSON>",
    date: "March 2024",
    rating: 5,
    comment: "Absolutely stunning property with impeccable service. The staff went above and beyond to make our stay memorable. The ski-in/ski-out access made our vacation effortless.",
    avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
  },
  {
    author: "<PERSON>",
    date: "February 2024",
    rating: 5,
    comment: "The attention to detail at this hotel is remarkable. From the welcome champagne to the turndown service, everything was perfect. The spa facilities are world-class.",
    avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
  },
  {
    author: "<PERSON>",
    date: "January 2024",
    rating: 4,
    comment: "Beautiful property in an excellent location. The rooms are spacious and well-appointed. The only minor issue was that the restaurant was fully booked during our stay.",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
  }
];

const { hotelName, reviews = defaultReviews } = Astro.props;
---

<section id="reviews" class="py-16">
  <div class="container-custom">
    <div class="text-center mb-10">
      <p class="section-micro-headline">Guest Experiences</p>
      <h2 class="section-title">What Our Guests Say</h2>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      {reviews.map(review => (
        <div class="bg-background border border-border/30 rounded-xl p-8 shadow-sm hover:shadow-glow transition-all duration-300">
          <div class="flex justify-between items-start mb-6">
            <div class="flex items-center">
              {review.avatar ? (
                <img 
                  src={review.avatar} 
                  alt={review.author} 
                  class="w-12 h-12 rounded-full object-cover mr-4"
                />
              ) : (
                <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium text-lg mr-4">
                  {review.author.charAt(0)}
                </div>
              )}
              <div>
                <h4 class="font-medium">{review.author}</h4>
                <p class="text-sm text-foreground/60">{review.date}</p>
              </div>
            </div>
            
            <div class="flex">
              {Array.from({ length: review.rating }).map(() => (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  stroke="none"
                  class="text-primary"
                >
                  <polygon
                    points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                  ></polygon>
                </svg>
              ))}
            </div>
          </div>
          
          <p class="text-foreground/80">"{review.comment}"</p>
        </div>
      ))}
    </div>
    
    <div class="mt-10 text-center">
      <a href="#" class="btn-outline">
        View All Reviews
      </a>
    </div>
  </div>
</section>
