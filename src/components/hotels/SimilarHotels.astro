---
import HotelCard from "../cards/HotelCard";

interface Hotel {
  id: number | string;
  name: string;
  location: string;
  rating: number;
  price: number;
  currency: string;
  imageUrl: string;
  description: string;
  tags: string[];
}

interface Props {
  similarHotels: (Hotel | undefined)[];
}

const { similarHotels } = Astro.props;
---

<section class="py-20 bg-gray-50">
  <div class="container-custom">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-12">
      <div>
        <h2 class="text-3xl font-baskervville mb-2">
          <span class="text-[#285DA6]">Similar</span> Properties You May Like
        </h2>
        <p class="text-foreground/70 max-w-xl">
          Discover more accommodations that match your preferences and style
        </p>
      </div>
      <a
        href="/stays"
        class="inline-flex items-center font-karla text-xs uppercase tracking-wider text-[#285DA6] border border-[#285DA6]/20 px-5 py-2.5 rounded-md transition-all hover:bg-[#285DA6] hover:text-white whitespace-nowrap mt-6 sm:mt-0 shadow-sm"
      >
        View All Properties
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2 group-hover:translate-x-1 transition-transform duration-300"
        >
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </a>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      {
        similarHotels
          .filter((hotel) => hotel !== undefined)
          .map((hotel) => (
            <div class="block">
              <HotelCard
                client:visible
                id={hotel.id}
                name={hotel.name}
                location={hotel.location}
                rating={hotel.rating}
                imageUrl={hotel.imageUrl}
                description={hotel.description}
                tags={hotel.tags}
                onCardClick={() => {
                  window.location.href = `/stays/${hotel.id}`;
                }}
              />
            </div>
          ))
      }
    </div>
  </div>
</section>
