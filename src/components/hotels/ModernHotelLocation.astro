---
interface Props {
  location: string;
  mapUrl?: string;
}

const { location, mapUrl = "https://maps.google.com/maps?q=" + encodeURIComponent(location) + "&output=embed" } = Astro.props;
---

<section id="location" class="py-24 bg-background/30">
  <div class="container-custom max-w-7xl">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-baskervville mb-4">Location</h2>
      <div class="w-20 h-1 bg-primary mx-auto mb-6"></div>
      <p class="text-foreground/70 max-w-2xl mx-auto">
        Located in the heart of {location}, our property offers easy access to local attractions and amenities.
      </p>
    </div>
    
    <div class="rounded-2xl overflow-hidden shadow-sm h-[500px]">
      <iframe 
        src={mapUrl}
        width="100%" 
        height="100%" 
        style="border:0;" 
        allowfullscreen="" 
        loading="lazy" 
        referrerpolicy="no-referrer-when-downgrade"
        title="Hotel location map"
      ></iframe>
    </div>
    
    <div class="mt-12 flex justify-center">
      <a 
        href={`https://maps.google.com/maps?q=${encodeURIComponent(location)}`} 
        target="_blank" 
        rel="noopener noreferrer"
        class="inline-flex items-center text-primary hover:underline font-medium"
      >
        Get Directions
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2"
        >
          <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
          <polyline points="15 3 21 3 21 9"></polyline>
          <line x1="10" y1="14" x2="21" y2="3"></line>
        </svg>
      </a>
    </div>
  </div>
</section>
