---
interface Props {
  amenities: string[];
}

const { amenities } = Astro.props;

// Map common amenities to icons
const getAmenityIcon = (amenity: string) => {
  const lowerAmenity = amenity.toLowerCase();

  if (lowerAmenity.includes("wifi") || lowerAmenity.includes("internet")) {
    return `<path d="M5 12.55a11 11 0 0 1 14.08 0"></path><path d="M1.42 9a16 16 0 0 1 21.16 0"></path><path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path><line x1="12" y1="20" x2="12.01" y2="20"></line>`;
  } else if (
    lowerAmenity.includes("pool") ||
    lowerAmenity.includes("swimming")
  ) {
    return `<path d="M2 12h20"></path><path d="M2 20h20"></path><path d="M6 8l4-4 4 4 4-4"></path><path d="M18 16l-4-4-4 4-4-4"></path>`;
  } else if (
    lowerAmenity.includes("spa") ||
    lowerAmenity.includes("massage") ||
    lowerAmenity.includes("wellness")
  ) {
    return `<path d="M6.8 11a6 6 0 1 0 10.396 0l-5.197-8-5.2 8Z"></path><path d="M6 11h12"></path>`;
  } else if (
    lowerAmenity.includes("breakfast") ||
    lowerAmenity.includes("restaurant") ||
    lowerAmenity.includes("dining")
  ) {
    return `<path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path><path d="M7 2v20"></path><path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>`;
  } else if (lowerAmenity.includes("gym") || lowerAmenity.includes("fitness")) {
    return `<path d="m6.5 6.5 11 11"></path><path d="m21 21-1-1"></path><path d="m3 3 1 1"></path><path d="m18 22 4-4"></path><path d="m2 6 4-4"></path><path d="m3 10 7-7"></path><path d="m14 21 7-7"></path>`;
  } else if (
    lowerAmenity.includes("parking") ||
    lowerAmenity.includes("garage")
  ) {
    return `<rect x="3" y="5" width="18" height="14" rx="2"></rect><path d="M10 9h4"></path><path d="M12 9v6"></path>`;
  } else if (
    lowerAmenity.includes("air") ||
    lowerAmenity.includes("conditioning")
  ) {
    return `<path d="M8 10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path><path d="M16 10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path><path d="M24 10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path><path d="M2 16h20"></path><path d="M12 10v6"></path>`;
  } else if (lowerAmenity.includes("bar") || lowerAmenity.includes("lounge")) {
    return `<path d="m8 22 8-8"></path><path d="M12 11v11"></path><path d="m19 3-7 8-7-8Z"></path>`;
  } else if (lowerAmenity.includes("ski") || lowerAmenity.includes("snow")) {
    return `<line x1="17" y1="3" x2="5" y2="21"></line><line x1="19" y1="5" x2="7" y2="23"></line><path d="M22 2l-1.5 1.5"></path><path d="M10.5 14.5L14 18"></path><path d="M16 8l-1.5 1.5"></path><path d="M2 22l1.5-1.5"></path>`;
  } else if (
    lowerAmenity.includes("tv") ||
    lowerAmenity.includes("television")
  ) {
    return `<rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect><polyline points="17 2 12 7 7 2"></polyline>`;
  } else if (
    lowerAmenity.includes("balcony") ||
    lowerAmenity.includes("terrace") ||
    lowerAmenity.includes("view")
  ) {
    return `<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="3" y1="9" x2="21" y2="9"></line><line x1="9" y1="21" x2="9" y2="9"></line>`;
  } else {
    return `<path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline>`;
  }
};
---

<section id="amenities" class="py-16 bg-background/50">
  <div class="container-custom">
    <div class="text-center mb-10">
      <p class="section-micro-headline">Hotel Features</p>
      <h2 class="section-title">Luxury Amenities</h2>
    </div>

    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {
        amenities.map((amenity) => (
          <div class="bg-background border border-border/30 rounded-lg p-4 flex flex-col items-center text-center hover:shadow-glow transition-all duration-300">
            <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="text-primary"
                set:html={getAmenityIcon(amenity)}
              ></svg>
            </div>
            <span class="text-sm font-medium">{amenity}</span>
          </div>
        ))
      }
    </div>
  </div>
</section>
