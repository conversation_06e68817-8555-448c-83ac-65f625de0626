---
// Props for the component
interface Props {
  id: number | string;
  name: string;
  location: string;
  rating?: number;
  price?: number;
  currency?: string;
  imageUrl: string;
  description?: string;
  tags?: string[] | string[][];
  index?: number;
  resort?: string;
  country?: string;
}

const { id, name, location, imageUrl, resort = "", country = "" } = Astro.props;

// Extract country from location if not provided
const displayCountry = country || location.split(",").pop()?.trim() || location;
// Use location as resort if not provided
const displayResort = resort || location.split(",")[0]?.trim() || "";
---

<div
  class="overflow-hidden transition-all duration-300 group hover:-translate-y-1 rounded-lg"
>
  <a href={`/stays/${id}`} class="block relative">
    <div class="aspect-[3/4] overflow-hidden rounded-lg">
      <img
        src={imageUrl}
        alt={name}
        class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105 rounded-lg"
      />
    </div>

    <div
      class="absolute inset-0 bg-black/40 flex flex-col items-center justify-center text-center"
    >
      <p class="text-sm uppercase tracking-wider text-white mb-2 font-karla">
        {displayCountry}
      </p>
      <h3 class="text-2xl font-baskervville leading-tight mb-2 text-white">
        {name}
      </h3>
      <p class="text-sm text-white/90 font-baskervville">{displayResort}</p>
    </div>
  </a>
</div>
