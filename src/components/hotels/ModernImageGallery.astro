---
interface Props {
  hotelName: string;
  images: string[];
}

const { hotelName, images } = Astro.props;
---

<section id="gallery" class="py-24 mb-8">
  <div class="container-custom max-w-7xl">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-baskervville mb-4">Photo Gallery</h2>
      <div class="w-20 h-1 bg-primary mx-auto mb-6"></div>
    </div>

    <div class="relative">
      <!-- Main Carousel -->
      <div class="carousel w-full">
        <div class="carousel-container overflow-hidden rounded-lg">
          <div
            class="carousel-track flex transition-transform duration-700"
            id="image-track"
          >
            {
              images.map((image, index) => (
                <div class="carousel-item flex-none w-full">
                  <div class="h-[70vh]">
                    <img
                      src={image}
                      alt={`${hotelName} - Image ${index + 1}`}
                      class="w-full h-full object-cover"
                    />
                  </div>
                </div>
              ))
            }
          </div>
        </div>

        <!-- Carousel Controls -->
        <button
          class="carousel-prev absolute top-1/2 left-6 -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white rounded-full p-4 shadow-lg z-10 transition-all duration-300"
          aria-label="Previous slide"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M15 18l-6-6 6-6"></path>
          </svg>
        </button>

        <button
          class="carousel-next absolute top-1/2 right-6 -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white rounded-full p-4 shadow-lg z-10 transition-all duration-300"
          aria-label="Next slide"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M9 18l6-6-6-6"></path>
          </svg>
        </button>
      </div>

      <!-- Thumbnail Gallery -->
      <div class="mt-6 overflow-hidden px-6">
        <div
          class="flex gap-3 overflow-x-auto pb-2 carousel-thumbnails justify-center"
        >
          {
            images.slice(0, 5).map((image, index) => (
              <button
                class="carousel-thumbnail flex-none w-28 h-20 rounded-lg overflow-hidden border-2 border-transparent hover:border-primary transition-all duration-300 focus:outline-none focus:border-primary"
                data-index={index}
                aria-label={`Go to slide ${index + 1}`}
              >
                <img
                  src={image}
                  alt={`${hotelName} - Thumbnail ${index + 1}`}
                  class="w-full h-full object-cover"
                />
              </button>
            ))
          }
          {
            images.length > 5 && (
              <button
                class="carousel-thumbnail flex-none w-28 h-20 rounded-lg overflow-hidden border-2 border-transparent hover:border-primary transition-all duration-300 focus:outline-none focus:border-primary bg-black/50 flex items-center justify-center text-white font-medium"
                aria-label="View all photos"
              >
                +{images.length - 5} more
              </button>
            )
          }
        </div>
      </div>
    </div>
  </div>
</section>
