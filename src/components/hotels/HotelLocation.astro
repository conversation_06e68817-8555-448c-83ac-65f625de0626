---
interface Props {
  location: string;
  mapUrl?: string;
}

const { location, mapUrl = "https://maps.google.com/maps?q=" + encodeURIComponent(location) + "&output=embed" } = Astro.props;
---

<section id="location" class="py-16 bg-background/50">
  <div class="container-custom">
    <div class="text-center mb-10">
      <p class="section-micro-headline">Location</p>
      <h2 class="section-title">Where You'll Be Staying</h2>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div>
        <div class="rounded-xl overflow-hidden shadow-glow h-[400px]">
          <iframe 
            src={mapUrl}
            width="100%" 
            height="100%" 
            style="border:0;" 
            allowfullscreen="" 
            loading="lazy" 
            referrerpolicy="no-referrer-when-downgrade"
            title="Hotel location map"
          ></iframe>
        </div>
      </div>
      
      <div>
        <h3 class="text-2xl font-baskervville mb-6">Prime Location</h3>
        <div class="flex items-start mb-6">
          <div class="mt-1 mr-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="text-primary"
            >
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </div>
          <div>
            <h4 class="text-lg font-medium mb-2">Address</h4>
            <p class="text-foreground/70 mb-4">{location}</p>
            <a 
              href={`https://maps.google.com/maps?q=${encodeURIComponent(location)}`} 
              target="_blank" 
              rel="noopener noreferrer"
              class="inline-flex items-center text-primary hover:underline"
            >
              Get Directions
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-1"
              >
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                <polyline points="15 3 21 3 21 9"></polyline>
                <line x1="10" y1="14" x2="21" y2="3"></line>
              </svg>
            </a>
          </div>
        </div>
        
        <div class="space-y-4 mt-8">
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="text-primary mr-3"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            <span>15 minutes from the airport</span>
          </div>
          
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="text-primary mr-3"
            >
              <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
              <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
              <line x1="6" y1="1" x2="6" y2="4"></line>
              <line x1="10" y1="1" x2="10" y2="4"></line>
              <line x1="14" y1="1" x2="14" y2="4"></line>
            </svg>
            <span>5 minutes to local restaurants and shops</span>
          </div>
          
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="text-primary mr-3"
            >
              <path d="m2 16 20 6-6-20A20 20 0 0 0 2 16Z"></path>
              <path d="M12 12a2 2 0 1 0 0 4 2 2 0 1 0 0-4Z"></path>
            </svg>
            <span>Direct access to ski slopes</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
