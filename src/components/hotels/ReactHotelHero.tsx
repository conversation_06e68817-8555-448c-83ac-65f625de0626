import React, { useState, useEffect, useRef } from "react";

interface ReactHotelHeroProps {
  name: string;
  location: string;
  rating: number;
  mainImage: string;
  images: string[];
  isAiSearch?: boolean;
  hotelId?: string;
}

const ReactHotelHero: React.FC<ReactHotelHeroProps> = ({
  name,
  location,
  rating,
  mainImage,
  images,
  isAiSearch = false,
  hotelId = "",
}) => {
  const [hoveredImageIndex, setHoveredImageIndex] = useState<number | null>(
    null
  );
  const [isSaved, setIsSaved] = useState(false);
  const wishlistIconRef = useRef<SVGSVGElement>(null);
  const wishlistTextRef = useRef<HTMLSpanElement>(null);

  // Check if hotel is in wishlist on component mount
  useEffect(() => {
    updateWishlistButtonState();

    // Listen for wishlist updates from other components
    window.addEventListener("wishlistUpdated", updateWishlistButtonState);

    return () => {
      window.removeEventListener("wishlistUpdated", updateWishlistButtonState);
    };
  }, [hotelId]);

  // Check if hotel is already in wishlist
  const updateWishlistButtonState = () => {
    if (!wishlistIconRef.current || !wishlistTextRef.current) return;

    try {
      const wishlistData = localStorage.getItem("wishlist");
      if (wishlistData) {
        const wishlist = JSON.parse(wishlistData);
        const isInWishlist = wishlist.some(
          (item: { id: string }) => item.id === hotelId
        );

        if (isInWishlist) {
          wishlistIconRef.current.setAttribute("fill", "currentColor");
          wishlistTextRef.current.textContent = "Saved";
          setIsSaved(true);
        } else {
          wishlistIconRef.current.setAttribute("fill", "none");
          wishlistTextRef.current.textContent = "Save";
          setIsSaved(false);
        }
      }
    } catch (error) {
      console.error("Error checking wishlist:", error);
    }
  };

  // Toggle wishlist item
  const toggleWishlist = () => {
    try {
      let wishlist = [];
      const wishlistData = localStorage.getItem("wishlist");

      if (wishlistData) {
        wishlist = JSON.parse(wishlistData);
      }

      const isInWishlist = wishlist.some(
        (item: { id: string }) => item.id === hotelId
      );

      if (isInWishlist) {
        // Remove from wishlist
        wishlist = wishlist.filter(
          (item: { id: string }) => item.id !== hotelId
        );
      } else {
        // Add to wishlist
        wishlist.push({
          id: hotelId,
          name: name,
          location: location,
          image: mainImage,
          addedAt: new Date().toISOString(),
        });
      }

      localStorage.setItem("wishlist", JSON.stringify(wishlist));
      updateWishlistButtonState();

      // Dispatch custom event to notify other components about wishlist changes
      window.dispatchEvent(new CustomEvent("wishlistUpdated"));
    } catch (error) {
      console.error("Error updating wishlist:", error);
    }
  };

  // Function to open the photo modal
  const openPhotoModal = (index: number) => {
    // Check if the openPhotoModal function exists in the window object
    if (typeof (window as any).openPhotoModal === "function") {
      (window as any).openPhotoModal(index);
    } else {
      console.warn("Photo modal function not available");
    }
  };

  return (
    <div className="container-custom">
      {/* Title Section */}
      <div className="pt-10 pb-8">
        <h1 className="text-4xl font-baskervville mb-3 text-[#3566ab]">
          {name}
        </h1>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center bg-[#3566ab]/5 px-3 py-1.5 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="currentColor"
                stroke="none"
                className="text-[#3566ab]"
              >
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
              <span className="ml-1.5 font-medium text-[#3566ab]">
                {rating}
              </span>
            </div>
            <div className="flex items-center bg-[#3566ab]/5 px-3 py-1.5 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#3566ab] mr-1.5"
              >
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              <span className="text-foreground/80">{location}</span>
            </div>
          </div>
          {!isAiSearch && (
            <div className="flex gap-4">
              <button
                id="hero-share-button"
                className="flex items-center px-3 py-1.5 rounded-full bg-[#3566ab]/5 hover:bg-[#3566ab]/10 transition-colors"
                onClick={() => {
                  if (typeof (window as any).openShareModal === "function") {
                    (window as any).openShareModal();
                  }
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2 text-[#3566ab]"
                >
                  <path d="M7 11v13l7-7 7 7V11"></path>
                  <rect x="3" y="3" width="18" height="8" rx="1" ry="1"></rect>
                </svg>
                <span className="font-medium text-sm">Share</span>
              </button>
              <button
                id="add-to-wishlist"
                className="flex items-center px-3 py-1.5 rounded-full bg-[#3566ab]/5 hover:bg-[#3566ab]/10 transition-colors"
                onClick={toggleWishlist}
                aria-label={
                  isSaved ? "Remove from wishlist" : "Add to wishlist"
                }
              >
                <svg
                  ref={wishlistIconRef}
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2 text-[#3566ab] wishlist-icon"
                >
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
                <span
                  ref={wishlistTextRef}
                  className="font-medium text-sm wishlist-text"
                >
                  Save
                </span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Image Gallery */}
      <div className="relative">
        {/* View All Photos Button */}
        <button
          onClick={() => openPhotoModal(0)}
          className="absolute top-4 right-4 z-10 bg-white/90 backdrop-blur-sm rounded-lg px-4 py-2.5 shadow-lg flex items-center font-medium text-sm hover:bg-white transition-all duration-300 border border-white/20"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2 text-[#3566ab]"
          >
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <circle cx="8.5" cy="8.5" r="1.5"></circle>
            <polyline points="21 15 16 10 5 21"></polyline>
          </svg>
          View all photos
        </button>

        <div
          id="gallery"
          className="grid grid-cols-1 md:grid-cols-2 gap-3 rounded-xl overflow-hidden h-[500px] shadow-lg"
        >
          <div className="h-full relative group overflow-hidden rounded-l-xl">
            <img
              src={mainImage}
              alt={name}
              className="w-full h-full object-cover cursor-pointer transition-transform duration-700 ease-out group-hover:scale-105"
              onClick={() => openPhotoModal(0)}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
          <div className="grid grid-cols-2 gap-3 h-full">
            {images.slice(1, 5).map((image, index) => (
              <div
                key={index}
                className={`overflow-hidden relative group ${
                  index === 3
                    ? "rounded-br-xl"
                    : index === 1
                    ? "rounded-tr-xl"
                    : ""
                }`}
                onMouseEnter={() => setHoveredImageIndex(index)}
                onMouseLeave={() => setHoveredImageIndex(null)}
              >
                <img
                  src={image}
                  alt={`${name} - Image ${index + 2}`}
                  className="w-full h-full object-cover cursor-pointer transition-transform duration-700 ease-out group-hover:scale-105"
                  onClick={() => openPhotoModal(index + 1)}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                {hoveredImageIndex === index && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-white/80 backdrop-blur-sm rounded-full p-2 shadow-lg transform transition-transform duration-300 scale-90 hover:scale-100">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-[#3566ab]"
                      >
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                        <line x1="11" y1="8" x2="11" y2="14"></line>
                        <line x1="8" y1="11" x2="14" y2="11"></line>
                      </svg>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReactHotelHero;
