---
interface Review {
  author: string;
  date: string;
  rating: number;
  comment: string;
  avatar?: string;
}

interface Props {
  hotelName: string;
  reviews?: Review[];
}

const defaultReviews: Review[] = [
  {
    author: "<PERSON>",
    date: "March 2024",
    rating: 5,
    comment: "Absolutely stunning property with impeccable service. The staff went above and beyond to make our stay memorable.",
    avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
  },
  {
    author: "<PERSON>",
    date: "February 2024",
    rating: 5,
    comment: "The attention to detail at this hotel is remarkable. From the welcome champagne to the turndown service, everything was perfect.",
    avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
  }
];

const { hotelName, reviews = defaultReviews } = Astro.props;
---

<section id="reviews" class="py-24">
  <div class="container-custom max-w-5xl">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-baskervville mb-4">Guest Reviews</h2>
      <div class="w-20 h-1 bg-primary mx-auto mb-6"></div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      {reviews.map(review => (
        <div class="bg-background border border-border/10 rounded-2xl p-8 shadow-sm">
          <div class="flex items-start mb-6">
            {review.avatar ? (
              <img 
                src={review.avatar} 
                alt={review.author} 
                class="w-14 h-14 rounded-full object-cover mr-4"
              />
            ) : (
              <div class="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium text-lg mr-4">
                {review.author.charAt(0)}
              </div>
            )}
            <div>
              <h4 class="font-medium text-lg">{review.author}</h4>
              <p class="text-sm text-foreground/60">{review.date}</p>
              <div class="flex mt-1">
                {Array.from({ length: 5 }).map((_, i) => (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill={i < review.rating ? "currentColor" : "none"}
                    stroke="currentColor"
                    stroke-width="1"
                    class={i < review.rating ? "text-primary" : "text-foreground/30"}
                  >
                    <polygon
                      points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                    ></polygon>
                  </svg>
                ))}
              </div>
            </div>
          </div>
          
          <p class="text-foreground/80 italic">"{review.comment}"</p>
        </div>
      ))}
    </div>
  </div>
</section>
