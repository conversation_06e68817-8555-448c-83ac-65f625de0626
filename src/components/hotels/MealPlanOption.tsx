import React from "react";
import { type RoomType } from "../../utils/types";

interface MealPlanOptionProps {
  roomType: RoomType;
  mealPlanKey: string;
  hotelCurrency: string;
  checkInDate?: string;
  checkOutDate?: string;
}

const MealPlanOption: React.FC<MealPlanOptionProps> = ({
  roomType,
  mealPlanKey,
  hotelCurrency,
  checkInDate,
  checkOutDate,
}) => {
  // Meal plan labels
  const mealPlanLabels: Record<string, string> = {
    none: "Room Only",
    bb: "Bed & Breakfast",
    hb: "Half Board",
    fb: "Full Board",
  };

  // Meal plan descriptions
  const mealPlanDescriptions: Record<string, string> = {
    none: "No meals included with your stay",
    bb: "Breakfast included with your stay",
    hb: "Breakfast and dinner included",
    fb: "All meals (breakfast, lunch, dinner) included",
  };

  // Meal plan short descriptions
  const mealPlanShortDescriptions: Record<string, string> = {
    none: "No meals",
    bb: "Breakfast only",
    hb: "Breakfast + Dinner",
    fb: "All meals included",
  };

  // Meal plan icons
  const mealPlanIcons: Record<string, JSX.Element> = {
    none: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="18"
        height="18"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
        <line x1="9" y1="9" x2="15" y2="15"></line>
        <line x1="15" y1="9" x2="9" y2="15"></line>
      </svg>
    ),
    bb: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="18"
        height="18"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
        <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
        <line x1="6" y1="1" x2="6" y2="4"></line>
        <line x1="10" y1="1" x2="10" y2="4"></line>
        <line x1="14" y1="1" x2="14" y2="4"></line>
      </svg>
    ),
    hb: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="18"
        height="18"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
        <path d="M7 2v20"></path>
        <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>
      </svg>
    ),
    fb: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="18"
        height="18"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
        <path d="M7 2v20"></path>
        <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>
        <path d="M12 7h5"></path>
        <path d="M12 11h5"></path>
        <path d="M12 15h5"></path>
      </svg>
    ),
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: hotelCurrency === "$" ? "USD" : hotelCurrency || "USD",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Get price for this meal plan
  const getMealPlanPrice = () => {
    try {
      if (
        roomType.priceDetails?.meal_plans &&
        roomType.priceDetails.meal_plans[mealPlanKey as any]
      ) {
        const mealPlanPrice =
          roomType.priceDetails.meal_plans[mealPlanKey as any];
        if (
          mealPlanPrice &&
          typeof mealPlanPrice === "object" &&
          mealPlanPrice.per_night_amount
        ) {
          return mealPlanPrice.per_night_amount;
        }
      }
      return roomType.price || 0;
    } catch (error) {
      console.error("Error getting meal plan price:", error);
      return roomType.price || 0;
    }
  };

  // Handle selection - Add to cart and redirect to cart page
  const handleSelect = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    try {
      // Generate a unique ID for the cart item
      const cartItemId = `${roomType.id}_${mealPlanKey}_${Date.now()}`;

      // Get the price for this meal plan
      const price = getMealPlanPrice();

      // Get the currency code
      const currencyCode =
        hotelCurrency === "$" ? "USD" : hotelCurrency || "USD";

      // Create the cart item
      const cartItem = {
        id: cartItemId,
        hotelId: roomType.hotelId || roomType.id.toString().split("_")[0] || "",
        roomId: roomType.id.toString(),
        roomType: roomType.name,
        hotelName: roomType.hotelName || "Luxury Hotel", // Use hotel name from roomType if available
        checkIn:
          checkInDate ||
          new Date(Date.now() + 86400000).toISOString().split("T")[0], // Tomorrow if not specified
        checkOut:
          checkOutDate ||
          new Date(Date.now() + 86400000 * 2).toISOString().split("T")[0], // Day after tomorrow if not specified
        checkInTime: roomType.checkInTime || "14:00",
        checkOutTime: roomType.checkOutTime || "11:00",
        guests: roomType.maxGuests || 2,
        infants: 0,
        price: price,
        currency: currencyCode,
        mealPlan: mealPlanKey,
        addedAt: new Date().toISOString(),
        image:
          roomType.thumbnail ||
          (roomType.images && roomType.images.length > 0
            ? typeof roomType.images[0] === "string"
              ? roomType.images[0]
              : (roomType.images[0] as any)?.url
            : "/images/room-placeholder.jpg"),
        regionId: "reg_01JP9R0NP6B5DXGDYHFSSW0FK1", // Default region ID
        available_rooms: roomType.availableRooms || 1,
        quantity: 1,
      };

      // Add the item to the cart using localStorage
      const existingCartData = localStorage.getItem("cart");
      let existingCart = [];

      if (existingCartData) {
        try {
          existingCart = JSON.parse(existingCartData);

          // Check if this is an array
          if (!Array.isArray(existingCart)) {
            existingCart = [];
          }
        } catch (error) {
          console.error("Failed to parse cart data:", error);
          existingCart = [];
        }
      }

      // Add the new item to the cart
      existingCart.push(cartItem);

      // Save the updated cart
      localStorage.setItem("cart", JSON.stringify(existingCart));

      // Navigate to the cart page
      window.location.href = "/cart";
    } catch (error) {
      console.error("Error adding item to cart:", error);
      alert(
        "There was an error adding this item to your cart. Please try again."
      );
    }
  };

  // Get refundable status
  const isRefundable = () => {
    try {
      if (
        roomType.priceDetails?.meal_plans &&
        roomType.priceDetails.meal_plans[mealPlanKey as any]
      ) {
        const mealPlanPrice =
          roomType.priceDetails.meal_plans[mealPlanKey as any];
        if (mealPlanPrice && typeof mealPlanPrice === "object") {
          return mealPlanPrice.refundable !== false;
        }
      }
      return true;
    } catch (error) {
      console.error("Error checking refundable status:", error);
      return true;
    }
  };

  return (
    <div
      className="relative w-full bg-white rounded-xl border border-[#3566ab]/10 hover:border-l-4 hover:border-l-[#3566ab] p-4 hover:shadow-lg hover:bg-gradient-to-br hover:from-[#3566ab]/5 hover:to-transparent transition-all duration-300 cursor-pointer"
      onClick={handleSelect}
    >
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-grow">
          {/* Meal Plan Header */}
          <div className="flex items-center mb-3">
            <div className="text-[#3566ab] mr-2">
              {mealPlanIcons[mealPlanKey] || mealPlanIcons.none}
            </div>
            <h5 className="font-medium text-[#3566ab]">
              {mealPlanLabels[mealPlanKey] || "Room Only"}
            </h5>
          </div>

          {/* Meal Plan Description */}
          <p className="text-sm text-foreground/70 mb-3">
            {mealPlanShortDescriptions[mealPlanKey] || "No meals"}
          </p>

          {/* Refundable Status */}
          <div className="flex items-center text-sm mb-3">
            {isRefundable() ? (
              <span className="text-green-600 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-1"
                >
                  <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                  <path d="m9 12 2 2 4-4"></path>
                </svg>
                Refundable
              </span>
            ) : (
              <span className="text-amber-600 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-1"
                >
                  <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                  <path d="M12 8v4"></path>
                  <path d="M12 16h.01"></path>
                </svg>
                Non-Refundable
              </span>
            )}
          </div>
        </div>

        {/* Price and Button */}
        <div className="flex flex-row md:flex-col justify-between items-end md:items-end md:min-w-[150px]">
          <div className="text-right">
            <div className="text-xs text-foreground/60">Per night</div>
            <div className="text-lg font-medium text-[#3566ab]">
              {formatCurrency(getMealPlanPrice())}
            </div>
          </div>

          <div className="mt-2">
            <button
              onClick={handleSelect}
              className="px-4 py-2.5 bg-[#3566ab] text-white rounded-md hover:bg-[#3566ab]/90 hover:shadow-md hover:translate-y-[-1px] transition-all duration-300 font-medium text-sm shadow-sm w-full"
            >
              Select
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MealPlanOption;
