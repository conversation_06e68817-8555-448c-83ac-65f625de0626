---
interface Props {
  name: string;
  location: string;
  rating: number;
}

const { name, location, rating } = Astro.props;
---

<div
  id="sticky-hotel-header"
  class="fixed top-0 left-0 w-full bg-white z-[1100] shadow-md backdrop-blur-sm bg-white/95 transform -translate-y-full transition-all duration-300 opacity-0"
>
  <!-- Desktop Layout -->
  <div class="hidden md:block py-6">
    <div class="container-custom">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <h2 class="text-xl font-baskervville mr-4">{name}</h2>
          <div class="flex items-center space-x-4">
            <div class="flex items-center text-[#285DA6]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="currentColor"
                stroke="none"
              >
                <polygon
                  points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                ></polygon>
              </svg>
              <span class="ml-1 text-sm font-medium">{rating}</span>
            </div>

            <div class="flex items-center text-sm">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="text-[#285DA6] mr-1"
              >
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              <span>{location}</span>
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-6">
          <a
            href="#rooms"
            class="text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors hover:border-b-2 hover:border-[#285DA6] pb-1"
            >Rooms</a
          >
          <a
            href="#about"
            class="text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors hover:border-b-2 hover:border-[#285DA6] pb-1"
            >About</a
          >
          <a
            href="#amenities"
            class="text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors hover:border-b-2 hover:border-[#285DA6] pb-1"
            >Amenities</a
          >
          <a
            href="#location"
            class="text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors hover:border-b-2 hover:border-[#285DA6] pb-1"
            >Location</a
          >

          <!-- Separator -->
          <div class="h-5 w-px bg-gray-300 mx-2"></div>

          <!-- Save Button -->
          <button
            id="sticky-add-to-wishlist"
            class="flex items-center text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="mr-1 sticky-wishlist-icon"
            >
              <path
                d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
              ></path>
            </svg>
            <span class="sticky-wishlist-text">Save</span>
          </button>

          <!-- Share Button -->
          <button
            id="sticky-share-button"
            class="flex items-center text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="mr-1"
            >
              <path d="M7 11v13l7-7 7 7V11"></path>
              <rect x="3" y="3" width="18" height="8" rx="1" ry="1"></rect>
            </svg>
            Share
          </button>

          <a
            href="#room-selection"
            class="flex items-center px-4 py-2 bg-[#285DA6] text-white rounded-md text-sm font-karla uppercase tracking-wider hover:bg-[#285DA6]/90 transition-colors shadow-sm"
          >
            Book Now
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Layout -->
  <div class="md:hidden py-5 px-4">
    <div class="flex items-center justify-between">
      <!-- Left: Hotel Name (truncated) -->
      <div class="flex-1 min-w-0">
        <h2 class="text-base font-baskervville text-gray-900 truncate pr-2">
          {name}
        </h2>
        <div class="flex items-center mt-0.5">
          <div class="flex items-center text-[#285DA6] mr-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 24 24"
              fill="currentColor"
              stroke="none"
            >
              <polygon
                points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
              ></polygon>
            </svg>
            <span class="ml-1 text-xs font-medium">{rating}</span>
          </div>
          <div class="flex items-center text-xs text-gray-600 truncate">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="10"
              height="10"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="text-[#285DA6] mr-1 flex-shrink-0"
            >
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
            <span class="truncate">{location}</span>
          </div>
        </div>
      </div>

      <!-- Right: Action Buttons -->
      <div class="flex items-center space-x-2 flex-shrink-0">
        <!-- Mobile Save Button -->
        <button
          id="sticky-add-to-wishlist-mobile"
          class="p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition-colors"
          title="Save"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="text-[#285DA6] sticky-wishlist-icon-mobile"
          >
            <path
              d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
            ></path>
          </svg>
        </button>

        <!-- Mobile Share Button -->
        <button
          id="sticky-share-button-mobile"
          class="p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition-colors"
          title="Share"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="text-[#285DA6]"
          >
            <path d="M7 11v13l7-7 7 7V11"></path>
            <rect x="3" y="3" width="18" height="8" rx="1" ry="1"></rect>
          </svg>
        </button>

        <!-- Mobile Menu Button -->
        <button
          id="mobile-menu-toggle"
          class="p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition-colors"
          title="Menu"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="text-[#285DA6]"
          >
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
          </svg>
        </button>

        <!-- Mobile Book Now Button -->
        <a
          href="#room-selection"
          class="px-3 py-1.5 bg-[#285DA6] text-white rounded-md text-xs font-karla uppercase tracking-wider hover:bg-[#285DA6]/90 transition-colors shadow-sm"
        >
          Book
        </a>
      </div>
    </div>

    <!-- Mobile Navigation Menu (Hidden by default) -->
    <div id="mobile-nav-menu" class="hidden mt-3 pt-3 border-t border-gray-200">
      <div class="grid grid-cols-2 gap-2">
        <a
          href="#rooms"
          class="mobile-nav-link flex items-center justify-center py-2 px-3 text-xs font-karla uppercase tracking-wider text-[#285DA6] bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
          >Rooms</a
        >
        <a
          href="#about"
          class="mobile-nav-link flex items-center justify-center py-2 px-3 text-xs font-karla uppercase tracking-wider text-[#285DA6] bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
          >About</a
        >
        <a
          href="#amenities"
          class="mobile-nav-link flex items-center justify-center py-2 px-3 text-xs font-karla uppercase tracking-wider text-[#285DA6] bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
          >Amenities</a
        >
        <a
          href="#location"
          class="mobile-nav-link flex items-center justify-center py-2 px-3 text-xs font-karla uppercase tracking-wider text-[#285DA6] bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
          >Location</a
        >
      </div>
    </div>
  </div>
</div>

<script is:inline>
  document.addEventListener("DOMContentLoaded", () => {
    const stickyHeader = document.getElementById("sticky-hotel-header");

    // Desktop elements
    const stickyWishlistButton = document.getElementById(
      "sticky-add-to-wishlist"
    );
    const stickyWishlistIcon = stickyWishlistButton?.querySelector(
      ".sticky-wishlist-icon"
    );
    const stickyWishlistText = stickyWishlistButton?.querySelector(
      ".sticky-wishlist-text"
    );
    const stickyShareButton = document.getElementById("sticky-share-button");

    // Mobile elements
    const mobileWishlistButton = document.getElementById(
      "sticky-add-to-wishlist-mobile"
    );
    const mobileWishlistIcon = mobileWishlistButton?.querySelector(
      ".sticky-wishlist-icon-mobile"
    );
    const mobileShareButton = document.getElementById(
      "sticky-share-button-mobile"
    );
    const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
    const mobileNavMenu = document.getElementById("mobile-nav-menu");

    // Use a fixed scroll threshold that's more reliable
    const scrollThreshold = 300; // Show after scrolling 300px - reduced for better UX

    const handleScroll = () => {
      if (window.scrollY > scrollThreshold) {
        stickyHeader?.classList.remove("-translate-y-full", "opacity-0");
        stickyHeader?.classList.add("translate-y-0", "opacity-100");
      } else {
        stickyHeader?.classList.add("-translate-y-full", "opacity-0");
        stickyHeader?.classList.remove("translate-y-0", "opacity-100");
      }
    };

    // Call once on load to check initial scroll position
    handleScroll();

    // Add scroll event listener with throttling for better performance
    let isScrolling = false;
    window.addEventListener("scroll", () => {
      if (!isScrolling) {
        window.requestAnimationFrame(() => {
          handleScroll();
          isScrolling = false;
        });
        isScrolling = true;
      }
    });

    // Mobile menu toggle functionality
    mobileMenuToggle?.addEventListener("click", () => {
      if (mobileNavMenu?.classList.contains("hidden")) {
        mobileNavMenu.classList.remove("hidden");
        mobileMenuToggle.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#285DA6]">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        `;
      } else {
        mobileNavMenu.classList.add("hidden");
        mobileMenuToggle.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#285DA6]">
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
          </svg>
        `;
      }
    });

    // Add smooth scrolling to anchor links
    document
      .querySelectorAll('#sticky-hotel-header a[href^="#"]')
      .forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();

          const targetId = this.getAttribute("href");
          if (!targetId) return;

          const targetElement = document.querySelector(targetId);
          if (!targetElement) return;

          // Close mobile menu if open
          if (!mobileNavMenu?.classList.contains("hidden")) {
            mobileNavMenu.classList.add("hidden");
            mobileMenuToggle.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#285DA6]">
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
              </svg>
            `;
          }

          // Update active state for navigation items
          document
            .querySelectorAll('#sticky-hotel-header a[href^="#"]')
            .forEach((link) => {
              link.classList.remove("border-b-2", "border-[#285DA6]");
            });
          this.classList.add("border-b-2", "border-[#285DA6]");

          // Calculate offset based on target
          let offset = 80; // Default offset

          // Special handling for different sections
          if (targetId === "#booking") {
            offset = 100; // Extra offset for booking section
          } else if (targetId === "#rooms") {
            offset = 70; // Less offset for rooms section
          }

          window.scrollTo({
            top: targetElement.offsetTop - offset,
            behavior: "smooth",
          });
        });
      });

    // Get hotel data from the page
    const hotelId = window.location.pathname.split("/").pop();
    const hotelName = document.querySelector("h1")?.textContent || "";
    const hotelLocation =
      document.querySelector(".flex.items-center span")?.textContent || "";
    const hotelImage =
      document.querySelector("#gallery img")?.getAttribute("src") || "";

    // Check if hotel is already in wishlist
    const updateWishlistButtonState = () => {
      try {
        const wishlistData = localStorage.getItem("wishlist");
        if (wishlistData) {
          const wishlist = JSON.parse(wishlistData);
          const isInWishlist = wishlist.some((item) => item.id === hotelId);

          // Update desktop button
          if (
            stickyWishlistButton &&
            stickyWishlistIcon &&
            stickyWishlistText
          ) {
            if (isInWishlist) {
              stickyWishlistIcon.setAttribute("fill", "currentColor");
              stickyWishlistText.textContent = "Saved";
            } else {
              stickyWishlistIcon.setAttribute("fill", "none");
              stickyWishlistText.textContent = "Save";
            }
          }

          // Update mobile button
          if (mobileWishlistIcon) {
            if (isInWishlist) {
              mobileWishlistIcon.setAttribute("fill", "currentColor");
            } else {
              mobileWishlistIcon.setAttribute("fill", "none");
            }
          }
        }
      } catch (error) {
        console.error("Error checking wishlist:", error);
      }
    };

    // Toggle wishlist item
    const toggleWishlist = () => {
      try {
        let wishlist = [];
        const wishlistData = localStorage.getItem("wishlist");

        if (wishlistData) {
          wishlist = JSON.parse(wishlistData);
        }

        const isInWishlist = wishlist.some((item) => item.id === hotelId);

        if (isInWishlist) {
          // Remove from wishlist
          wishlist = wishlist.filter((item) => item.id !== hotelId);
        } else {
          // Add to wishlist
          wishlist.push({
            id: hotelId,
            name: hotelName,
            location: hotelLocation,
            image: hotelImage,
            addedAt: new Date().toISOString(),
          });
        }

        localStorage.setItem("wishlist", JSON.stringify(wishlist));
        updateWishlistButtonState();

        // Dispatch custom event to notify other components about wishlist changes
        window.dispatchEvent(new CustomEvent("wishlistUpdated"));
      } catch (error) {
        console.error("Error updating wishlist:", error);
      }
    };

    // Add click event listeners for wishlist buttons (desktop and mobile)
    stickyWishlistButton?.addEventListener("click", toggleWishlist);
    mobileWishlistButton?.addEventListener("click", toggleWishlist);

    // Initialize wishlist button state
    updateWishlistButtonState();

    // Add click event listeners for share buttons (desktop and mobile)
    stickyShareButton?.addEventListener("click", () => {
      // Check if the openShareModal function exists (from ShareModal component)
      if (typeof window.openShareModal === "function") {
        window.openShareModal();
      }
    });

    mobileShareButton?.addEventListener("click", () => {
      // Check if the openShareModal function exists (from ShareModal component)
      if (typeof window.openShareModal === "function") {
        window.openShareModal();
      }
    });

    // Listen for wishlist updates from other components
    window.addEventListener("wishlistUpdated", updateWishlistButtonState);
  });
</script>
