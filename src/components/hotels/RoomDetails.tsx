import React, { useState, useRef } from "react";
import { type RoomType } from "../../utils/types";

interface RoomDetailsProps {
  room: RoomType;
  isSelected: boolean;
}

const RoomDetails: React.FC<RoomDetailsProps> = ({ room, isSelected }) => {
  const sliderRef = useRef<HTMLDivElement>(null);

  // Format amenities for display
  const getAmenityName = (amenity: any) => {
    return typeof amenity === "string"
      ? amenity
      : amenity && amenity.name
      ? amenity.name
      : "Amenity";
  };

  // Get room images
  const getRoomImages = () => {
    if (Array.isArray(room.images) && room.images.length > 0) {
      return room.images.map((image, index) => ({
        src:
          typeof image === "string"
            ? image
            : image && image.url
            ? image.url
            : "",
        alt: `${room.name} - Image ${index + 1}`,
      }));
    }
    return [];
  };

  const roomImages = getRoomImages();

  // Navigation functions for the slider
  const scrollAmount = 300; // Adjust this value based on your design

  const goToNextImage = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (sliderRef.current) {
      sliderRef.current.scrollBy({ left: scrollAmount, behavior: "smooth" });
    }
  };

  const goToPrevImage = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (sliderRef.current) {
      sliderRef.current.scrollBy({ left: -scrollAmount, behavior: "smooth" });
    }
  };

  return (
    <div
      className={`collapsible-content bg-white rounded-b-xl transition-all duration-300 ease-out ${
        isSelected
          ? "border-left-2 border-right-2 border-bottom-2 border-[#3566ab] bg-[#3566ab]/[0.02]"
          : "border border-[#3566ab]/10 border-t-0"
      }`}
    >
      <div className="px-8 pb-8 pt-2 space-y-10">
        {/* Room Description */}
        <div className="pt-2">
          <h4 className="text-xl font-baskervville mb-4 text-[#3566ab] flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-2"
            >
              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
            </svg>
            Room Description
          </h4>
          <p className="text-sm text-foreground/80 leading-relaxed">
            {room.description || ""}
          </p>
        </div>

        {/* Room Details */}
        <div>
          <h4 className="text-xl font-baskervville mb-4 text-[#3566ab] flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-2"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="3" y1="9" x2="21" y2="9"></line>
              <line x1="9" y1="21" x2="9" y2="9"></line>
            </svg>
            Room Details
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gradient-to-br from-[#3566ab]/5 to-[#3566ab]/10 rounded-xl p-6 shadow-sm">
              <h5 className="text-sm font-karla uppercase tracking-wider mb-4 text-[#3566ab] border-b border-[#3566ab]/10 pb-2">
                Room Specifications
              </h5>
              <ul className="space-y-3">
                <li className="flex items-center text-sm">
                  <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#3566ab]"
                    >
                      <path d="M2 4v16"></path>
                      <path d="M22 4v16"></path>
                      <path d="M2 12h20"></path>
                      <path d="M12 2v20"></path>
                    </svg>
                  </div>
                  <span className="text-foreground/80">
                    Room Size:{" "}
                    <span className="font-medium text-foreground">
                      {room.size || "65"} sq.m
                    </span>
                  </span>
                </li>
                <li className="flex items-center text-sm">
                  <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#3566ab]"
                    >
                      <path d="M2 3h20v10H2z"></path>
                      <path d="M2 17h20v4H2z"></path>
                    </svg>
                  </div>
                  <span className="text-foreground/80">
                    Bed Type:{" "}
                    <span className="font-medium text-foreground">
                      {(room.bedType &&
                        room.bedType.charAt(0).toUpperCase() +
                          room.bedType.slice(1)) ||
                        "King-size"}
                    </span>
                  </span>
                </li>
                <li className="flex items-center text-sm">
                  <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#3566ab]"
                    >
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                    </svg>
                  </div>
                  <span className="text-foreground/80">
                    Max Adults:{" "}
                    <span className="font-medium text-foreground">
                      {room.maxAdults || "2"}
                    </span>
                  </span>
                </li>
                <li className="flex items-center text-sm">
                  <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#3566ab]"
                    >
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                      <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                      <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                  </div>
                  <span className="text-foreground/80">
                    Max Children:{" "}
                    <span className="font-medium text-foreground">
                      {room.maxChildren || "1"}
                    </span>
                  </span>
                </li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-[#3566ab]/5 to-[#3566ab]/10 rounded-xl p-6 shadow-sm">
              <h5 className="text-sm font-karla uppercase tracking-wider mb-4 text-[#3566ab] border-b border-[#3566ab]/10 pb-2">
                Room Amenities
              </h5>
              <div className="flex flex-wrap gap-2">
                {Array.isArray(room.amenities) && room.amenities.length > 0 ? (
                  room.amenities.map((amenity, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-karla bg-white text-foreground/80 shadow-sm border border-[#3566ab]/5"
                    >
                      {getAmenityName(amenity)}
                    </span>
                  ))
                ) : (
                  <span className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-karla bg-white text-foreground/80 shadow-sm border border-[#3566ab]/5">
                    No amenities listed
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Room Gallery Slider */}
      <div className="border-t border-[#3566ab]/10 p-8">
        <h4 className="text-xl font-baskervville mb-5 text-[#3566ab] flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2"
          >
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <circle cx="8.5" cy="8.5" r="1.5"></circle>
            <polyline points="21 15 16 10 5 21"></polyline>
          </svg>
          Room Gallery
        </h4>

        {roomImages.length > 0 ? (
          <div className="relative">
            {/* Image slider */}
            <div className="relative overflow-hidden rounded-xl shadow-md">
              <div
                ref={sliderRef}
                className="overflow-x-auto scrollbar-hide"
                style={{
                  msOverflowStyle: "none" /* IE and Edge */,
                  scrollbarWidth: "none" /* Firefox */,
                  WebkitOverflowScrolling: "touch",
                }}
              >
                <div className="flex gap-4 pb-2">
                  {roomImages.map((image, index) => (
                    <div
                      key={index}
                      className="flex-none w-full sm:w-1/2 md:w-1/3 lg:w-1/3 rounded-xl overflow-hidden"
                    >
                      <div className="aspect-[4/3] relative group">
                        <img
                          src={image.src}
                          alt={image.alt}
                          className="object-cover w-full h-full transition-transform duration-700 ease-out group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Navigation buttons */}
              {roomImages.length > 3 && (
                <>
                  <button
                    onClick={goToPrevImage}
                    className="absolute left-4 top-1/2 -translate-y-1/2 z-20 bg-white/90 backdrop-blur-sm hover:bg-white text-[#3566ab] rounded-full p-2.5 shadow-lg transition-all duration-300 border border-white/20"
                    aria-label="Previous images"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M15 18l-6-6 6-6"></path>
                    </svg>
                  </button>
                  <button
                    onClick={goToNextImage}
                    className="absolute right-4 top-1/2 -translate-y-1/2 z-20 bg-white/90 backdrop-blur-sm hover:bg-white text-[#3566ab] rounded-full p-2.5 shadow-lg transition-all duration-300 border border-white/20"
                    aria-label="Next images"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M9 18l6-6-6-6"></path>
                    </svg>
                  </button>
                </>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-10 text-foreground/60 bg-[#3566ab]/5 rounded-xl">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="40"
              height="40"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mx-auto mb-3 text-[#3566ab]/30"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <circle cx="8.5" cy="8.5" r="1.5"></circle>
              <polyline points="21 15 16 10 5 21"></polyline>
            </svg>
            No room images available
          </div>
        )}
      </div>
    </div>
  );
};

export default RoomDetails;
