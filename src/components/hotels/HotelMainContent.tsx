import React, { useState, useEffect } from "react";
import RoomSelection from "./RoomSelection";
import HotelInformation from "./HotelInformation";
import { getHotelAvailabilityOnly } from "../../utils/dataService";
import { type RoomType } from "../../utils/types";

interface HotelData {
  id: string | number;
  name: string;
  description: string;
  location: string;
  price: number;
  currency: string;
  check_in_time?: string;
  check_out_time?: string;
  images: any[];
  [key: string]: any;
}

interface HotelMainContentProps {
  hotelData: HotelData;
  roomTypes: RoomType[];
  hotelId: string | number;
  nights: number;
  urlAdults?: string | null;
  urlChildren?: string | null;
  urlInfants?: string | null;
}

const HotelMainContent: React.FC<HotelMainContentProps> = ({
  hotelData,
  roomTypes,
  hotelId,
  nights,
  urlAdults,
  urlChildren,
  urlInfants,
}) => {
  // State for room types and dates
  const [currentRoomTypes, setCurrentRoomTypes] = useState(roomTypes);

  // Initialize with default dates (tomorrow and tomorrow + nights)
  const [checkInDate, setCheckInDate] = useState<Date | null>(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow;
  });

  const [checkOutDate, setCheckOutDate] = useState<Date | null>(() => {
    const checkoutDate = new Date();
    checkoutDate.setDate(checkoutDate.getDate() + 1 + (nights || 2));
    return checkoutDate;
  });

  const [isLoading, setIsLoading] = useState(false);

  // Initialize with default room types
  useEffect(() => {
    setCurrentRoomTypes(roomTypes);
  }, [roomTypes]);

  // State to track current guest counts
  const [currentGuests, setCurrentGuests] = useState({
    adults: urlAdults ? parseInt(urlAdults) : 1,
    children: urlChildren ? parseInt(urlChildren) : 0,
    infants: urlInfants ? parseInt(urlInfants) : 0,
  });

  // Function to update dates and room types
  // This function only calls the availability API when dates change
  // It doesn't fetch hotel details again, which optimizes API usage
  // Unified function to update room types when dates or guest count changes
  const updateRoomAvailability = async (params: {
    newCheckInDate?: Date | null;
    newCheckOutDate?: Date | null;
    adults?: number;
    children?: number;
    infants?: number;
  }) => {
    // Determine which dates to use (new dates or existing dates)
    const checkIn = params.newCheckInDate || checkInDate;
    const checkOut = params.newCheckOutDate || checkOutDate;

    // If we don't have valid dates, we can't proceed
    if (!checkIn || !checkOut) {
      return;
    }

    // Determine which guest counts to use (new counts or existing counts)
    const adults =
      params.adults !== undefined ? params.adults : currentGuests.adults;
    const children =
      params.children !== undefined ? params.children : currentGuests.children;
    const infants =
      params.infants !== undefined ? params.infants : currentGuests.infants;

    // If guest counts were provided, update the current guests state
    if (
      params.adults !== undefined ||
      params.children !== undefined ||
      params.infants !== undefined
    ) {
      setCurrentGuests({
        adults,
        children,
        infants,
      });
    }

    // If new dates were provided, update the date states
    if (params.newCheckInDate) {
      setCheckInDate(params.newCheckInDate);
    }
    if (params.newCheckOutDate) {
      setCheckOutDate(params.newCheckOutDate);
    }

    setIsLoading(true);
    try {
      // Use the optimized function that only calls the availability API
      const result = await getHotelAvailabilityOnly(
        hotelId,
        checkIn,
        checkOut,
        adults,
        children,
        infants
      );

      // Convert the result to the expected RoomType format
      const updatedRoomTypes = result.roomTypes.map((room: any) => ({
        id: room.id,
        name: room.title || room.name,
        title: room.title,
        price: room.price,
        maxGuests: room.maxGuests,
        maxAdults: room.max_adults || room.maxAdults,
        maxChildren: room.max_children || room.maxChildren,
        maxInfants: room.max_infants || room.maxInfants,
        availableRooms: room.available_rooms || room.availableRooms,
        bedType: room.bed_type || room.bedType,
        size: room.room_size || room.size,
        description: room.description,
        thumbnail: room.thumbnail || undefined,
        images: room.images,
        amenities: room.amenities,
        available: room.available,
        priceDetails: room.price,
      }));

      setCurrentRoomTypes(updatedRoomTypes);
      setIsLoading(false);
    } catch (error) {
      console.error("Error updating room availability:", error);
      setIsLoading(false);
    }
  };

  // Wrapper functions to maintain backward compatibility
  const updateDatesAndRoomTypes = async (
    newCheckInDate: Date | null,
    newCheckOutDate: Date | null
  ) => {
    await updateRoomAvailability({
      newCheckInDate,
      newCheckOutDate,
    });
  };

  const updateRoomTypesForGuestChange = async (
    adults: number,
    children: number,
    infants: number
  ) => {
    await updateRoomAvailability({
      adults,
      children,
      infants,
    });
  };

  return (
    <div className="container-custom py-8 mb-8">
      <div className="mt-8">
        {/* Room Selection Component */}
        <RoomSelection
          roomTypes={currentRoomTypes}
          hotelData={hotelData}
          isLoading={isLoading}
          checkInDate={checkInDate}
          checkOutDate={checkOutDate}
          onRoomTypesUpdate={setCurrentRoomTypes}
        />

        {/* Hotel Information Component */}
        <HotelInformation hotelData={hotelData} hotelId={hotelId} />
      </div>
    </div>
  );
};

export default HotelMainContent;
