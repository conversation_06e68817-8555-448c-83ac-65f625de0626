---
interface Props {
  hotel: {
    name: string;
    description: string;
    amenities: string[];
    check_in_time?: string;
    check_out_time?: string;
    location?: string;
  };
}

const { hotel } = Astro.props;

// Format amenities for display
const formatAmenities = (amenities: string[]) => {
  // Add more example amenities if there are fewer than 12
  const defaultAmenities = [
    'WiFi', 'Pool', 'Spa', 'Gym', 'Restaurant', 'Bar',
    'Room Service', 'Concierge', 'Parking', 'Air Conditioning',
    'Ski Storage', 'Shuttle Service', 'Laundry', 'Breakfast',
    'Mountain View', 'Balcony'
  ];

  // Combine existing amenities with defaults, removing duplicates
  const combinedAmenities = [...new Set([...amenities, ...defaultAmenities])];

  // Take only the first 12 amenities for the highlights section
  return combinedAmenities.slice(0, 12);
};

const highlightAmenities = formatAmenities(hotel.amenities);

// Create a Google Maps URL with the hotel location
const locationForMap = hotel.location || 'Switzerland';
---

<!-- ABOUT HOTEL -->
<div class="border-b border-border/20 pb-8 mb-8">
  <h2 class="text-2xl font-baskervville mb-6 uppercase">About {hotel.name}</h2>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-8 mb-8">
    <div class="flex flex-col md:items-center">
      <div class="mb-4 flex justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      </div>
      <div class="text-center">
        <h3 class="font-medium mb-1 uppercase text-sm">Accommodates</h3>
        <p class="text-foreground/70 text-xs">Up to 50 guests</p>
      </div>
    </div>

    <div class="flex flex-col md:items-center">
      <div class="mb-4 flex justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M2 3h20v10H2z"></path>
          <path d="M2 17h20v4H2z"></path>
        </svg>
      </div>
      <div class="text-center">
        <h3 class="font-medium mb-1 uppercase text-sm">Room Types</h3>
        <p class="text-foreground/70 text-xs">Standard, Deluxe, Suite</p>
      </div>
    </div>

    <div class="flex flex-col md:items-center">
      <div class="mb-4 flex justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
          <circle cx="12" cy="10" r="3"></circle>
        </svg>
      </div>
      <div class="text-center">
        <h3 class="font-medium mb-1 uppercase text-sm">Property Size</h3>
        <p class="text-foreground/70 text-xs">5000 sq.m / 53,820 sq.ft</p>
      </div>
    </div>
  </div>
</div>

<!-- ABOUT THIS HOTEL -->
<div class="border-b border-border/20 pb-8 mb-8">
  <h2 class="text-2xl font-baskervville mb-6 uppercase">About this hotel</h2>

  <p class="text-foreground/80 leading-relaxed mb-6">
    {hotel.description}
  </p>

  <button class="flex items-center text-primary font-medium">
    <span>Show more</span>
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1">
      <polyline points="6 9 12 15 18 9"></polyline>
    </svg>
  </button>
</div>

<!-- Room Types section will be inserted here from the HotelDetails component -->

<!-- WHAT THIS HOTEL OFFERS -->
<div class="border-b border-border/20 pb-8 mb-8">
  <h2 class="text-2xl font-baskervville mb-6 uppercase">What this hotel offers</h2>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
    {
      highlightAmenities.slice(0, 6).map((amenity) => (
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-4 text-primary">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
          <span class="text-foreground/80">{amenity}</span>
        </div>
      ))
    }
  </div>
</div>



<!-- Location teaser -->
<div class="border-b border-border/20 pb-8 mb-8">
  <h2 class="text-2xl font-baskervville mb-6 text-[#285DA6]" id="location">
    Where to find us
  </h2>
  <div class="rounded-xl overflow-hidden h-[300px]">
    <iframe
      src={`https://maps.google.com/maps?q=${encodeURIComponent(locationForMap)}&t=&z=13&ie=UTF8&iwloc=&output=embed`}
      width="100%"
      height="100%"
      style="border:0;"
      allowfullscreen=""
      loading="lazy"
      referrerpolicy="no-referrer-when-downgrade"
    ></iframe>
  </div>
</div>
