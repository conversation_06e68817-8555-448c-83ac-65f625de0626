---
import HotelDescription from "./HotelDescription.astro";
import HotelAmenities from "./HotelAmenities.astro";
import RoomTypes from "./RoomTypes.astro";
import CancellationPolicies from "./CancellationPolicies.astro";

interface CancellationPolicy {
  id: string;
  name: string;
  description: string;
  days_before_checkin: number;
  refund_type: string;
  refund_amount: number;
}

interface Props {
  hotel: {
    id: number | string;
    name: string;
    description: string;
    amenities: string[];
    cancellation_policies?: CancellationPolicy[];
    check_in_time?: string;
    check_out_time?: string;
  };
  roomTypes: any[];
}

const { hotel, roomTypes } = Astro.props;
---

<div class="lg:col-span-2">
  <HotelDescription hotelName={hotel.name} description={hotel.description} />

  {/* Check-in/Check-out Times */}
  {
    (hotel.check_in_time || hotel.check_out_time) && (
      <div class="mb-10">
        <h2 class="text-2xl font-baskervville mb-4">Check-in & Check-out</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          {hotel.check_in_time && (
            <div class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="text-primary mr-3"
              >
                <circle cx="12" cy="12" r="10" />
                <polyline points="12 6 12 12 16 14" />
              </svg>
              <div>
                <span class="text-sm text-foreground/60">Check-in</span>
                <p class="font-medium">{hotel.check_in_time}</p>
              </div>
            </div>
          )}

          {hotel.check_out_time && (
            <div class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="text-primary mr-3"
              >
                <circle cx="12" cy="12" r="10" />
                <polyline points="12 6 12 12 16 14" />
              </svg>
              <div>
                <span class="text-sm text-foreground/60">Check-out</span>
                <p class="font-medium">{hotel.check_out_time}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  <RoomTypes roomTypes={roomTypes} />
  <HotelAmenities amenities={hotel.amenities} />

  {/* Cancellation Policies */}
  {
    hotel.cancellation_policies && hotel.cancellation_policies.length > 0 && (
      <CancellationPolicies policies={hotel.cancellation_policies} />
    )
  }
</div>
