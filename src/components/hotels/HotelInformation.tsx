import React, { useState } from "react";
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "../ui/dialog";
import PoliciesModal from "../ui/PoliciesModal";

interface CancellationPolicy {
  id: string;
  name: string;
  description: string;
  days_before_checkin: number;
  refund_type: string;
  refund_amount: number;
}

interface HotelData {
  id: string | number;
  name: string;
  description: string;
  location: string;
  check_in_time?: string;
  check_out_time?: string;
  amenities?: string[];
  cancellation_policies?: CancellationPolicy[];
  safety_measures?: string[];
  rules?: string[];
  [key: string]: any;
}

interface HotelInformationProps {
  hotelData: HotelData;
  hotelId: string | number;
}

const HotelInformation: React.FC<HotelInformationProps> = ({
  hotelData,
  // hotelId is passed but not used in this component
  hotelId: _,
}) => {
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [showAllAmenities, setShowAllAmenities] = useState(false);
  const [showPoliciesModal, setShowPoliciesModal] = useState(false);

  // Function to toggle description visibility
  const toggleDescription = () => {
    setShowFullDescription(!showFullDescription);
  };

  return (
    <div className="mt-12">
      {/* ABOUT THIS HOTEL */}
      <div
        id="about"
        className="border-b border-border/20 pb-8 mb-8 pt-16 -mt-16"
      >
        <h2 className="text-2xl font-baskervville mb-6 uppercase">
          About this hotel
        </h2>
        <p className="text-foreground/80 leading-relaxed mb-6">
          {showFullDescription
            ? hotelData.description
            : `${hotelData.description?.substring(0, 860)}${
                hotelData.description?.length > 300 ? "..." : ""
              }`}
        </p>
        {hotelData.description?.length > 860 && (
          <button
            className="flex items-center text-primary font-medium"
            onClick={toggleDescription}
          >
            <span className="text-md text-[#285DA6]">
              {showFullDescription ? "Show less" : "Show more"}
            </span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="ml-1 text-[#285DA6]"
            >
              <polyline
                points={
                  showFullDescription ? "18 15 12 9 6 15" : "6 9 12 15 18 9"
                }
              ></polyline>
            </svg>
          </button>
        )}
      </div>

      {/* WHAT THIS HOTEL OFFERS */}
      <div
        id="amenities"
        className="border-b border-border/20 pb-8 mb-8 pt-16 -mt-16"
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-baskervville uppercase">
            What this hotel offers
          </h2>
          {hotelData?.amenities && hotelData?.amenities?.length > 9 && (
            <div className="flex justify-center">
              <button
                onClick={() => setShowAllAmenities(true)}
                className="flex items-center text-[#285DA6] hover:text-[#285DA6]/80 transition-colors font-medium"
              >
                <span className="mr-1">Show All Amenities</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </button>
            </div>
          )}
        </div>

        <div className="mb-6">
          {hotelData.amenities && hotelData.amenities.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Column 1 */}
                <div className="bg-[#285DA6]/5 rounded-lg p-4">
                  <ul className="space-y-2">
                    {hotelData.amenities.slice(0, 4).map((amenity, index) => (
                      <AmenityItem key={`col1-${index}`} text={amenity} />
                    ))}
                  </ul>
                </div>

                {/* Column 2 */}
                {hotelData.amenities.length > 3 && (
                  <div className="bg-[#285DA6]/5 rounded-lg p-4">
                    <ul className="space-y-2">
                      {hotelData.amenities.slice(4, 8).map((amenity, index) => (
                        <AmenityItem key={`col2-${index}`} text={amenity} />
                      ))}
                    </ul>
                  </div>
                )}

                {/* Column 3 */}
                {hotelData.amenities.length > 8 && (
                  <div className="bg-[#285DA6]/5 rounded-lg p-4">
                    <ul className="space-y-2">
                      {hotelData.amenities
                        .slice(8, 12)
                        .map((amenity, index) => (
                          <AmenityItem key={`col3-${index}`} text={amenity} />
                        ))}
                    </ul>
                  </div>
                )}
              </div>

              {/* Enhanced Amenities Modal */}
              <Dialog
                open={showAllAmenities}
                onOpenChange={setShowAllAmenities}
              >
                <DialogContent className="w-[90vw] max-w-[700px] max-h-[85vh] z-[1200] bg-gradient-to-br from-white via-white to-blue-50/30 border-0 shadow-2xl backdrop-blur-sm">
                  {/* Premium Header */}
                  <DialogHeader className="relative pb-6 border-b border-gradient-to-r from-transparent via-[#285DA6]/20 to-transparent">
                    <div className="flex items-center justify-center mb-4">
                      <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#285DA6] to-[#1e4a8c] flex items-center justify-center shadow-lg">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-white"
                        >
                          <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                          <polyline points="9 22 9 12 15 12 15 22"></polyline>
                        </svg>
                      </div>
                    </div>
                    <DialogTitle className="text-2xl sm:text-3xl font-baskervville text-center bg-gradient-to-r from-[#285DA6] to-[#1e4a8c] bg-clip-text text-transparent">
                      Hotel Amenities
                    </DialogTitle>
                    <p className="text-center text-gray-600 text-sm mt-2">
                      Discover all the facilities and services available at this
                      hotel
                    </p>
                  </DialogHeader>

                  {/* Enhanced Content Area */}
                  <div className="mt-6 space-y-4 max-h-[55vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-[#285DA6]/20 scrollbar-track-transparent">
                    {/* Amenities Count */}
                    <div className="flex items-center justify-center mb-4">
                      <div className="bg-gradient-to-r from-[#285DA6]/10 to-[#1e4a8c]/10 rounded-full px-4 py-2 border border-[#285DA6]/20">
                        <span className="text-sm font-medium text-[#285DA6]">
                          {hotelData.amenities.length} Amenities Available
                        </span>
                      </div>
                    </div>

                    {/* Enhanced Amenities Grid */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                      {hotelData.amenities.map((amenity, index) => (
                        <div
                          key={index}
                          className="group relative bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-[#285DA6]/10 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] hover:bg-white/90 hover:border-[#285DA6]/20"
                        >
                          {/* Amenity Icon Background */}
                          <div className="absolute top-2 right-2 w-8 h-8 rounded-full bg-gradient-to-br from-[#285DA6]/10 to-[#1e4a8c]/10 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="14"
                              height="14"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-[#285DA6]"
                            >
                              <polyline points="20 6 9 17 4 12"></polyline>
                            </svg>
                          </div>

                          {/* Enhanced Amenity Item */}
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-gradient-to-r from-[#285DA6] to-[#1e4a8c] mr-3 shadow-sm group-hover:scale-125 transition-transform duration-300"></div>
                            <span className="text-sm font-medium text-gray-900 group-hover:text-[#285DA6] transition-colors duration-300">
                              {amenity}
                            </span>
                          </div>

                          {/* Subtle gradient overlay on hover */}
                          <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-[#285DA6]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </>
          ) : (
            <div className="bg-[#285DA6]/5 rounded-lg p-4">
              <p className="text-sm text-foreground/80">No amenities listed</p>
            </div>
          )}
        </div>
      </div>

      {/* THINGS TO KNOW */}
      <div
        id="things-to-know"
        className="border-b border-border/20 pb-8 mb-8 pt-16 -mt-16"
      >
        <h2 className="text-2xl font-baskervville mb-6 uppercase">
          Things to know
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-6">
            {/* House Rules */}
            <div className="bg-[#285DA6]/5 rounded-lg p-5 border border-[#285DA6]/10 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-[#285DA6]/10 flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#285DA6]"
                  >
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                  </svg>
                </div>
                <h5 className="text-md uppercase tracking-wider text-[#285DA6] font-semibold">
                  Rules
                </h5>
              </div>
              <div className="space-y-3 pl-1">
                {/* Hotel Rules */}
                {hotelData?.rules && hotelData.rules.length > 0 && (
                  <div className="mb-4 pb-4 border-b border-[#285DA6]/10">
                    <div className="space-y-3">
                      {hotelData.rules.map((rule, index) => (
                        <div key={index} className="flex items-center">
                          <div className="w-1.5 h-1.5 rounded-full bg-[#285DA6] mr-2"></div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-foreground/90">
                              {rule}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex gap-16">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-[#285DA6]"
                      >
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Check-in</p>
                      <p className="text-sm text-foreground/70">
                        {hotelData.check_in_time || "After 3:00 PM"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-[#285DA6]"
                      >
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 8 14"></polyline>
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Check-out</p>
                      <p className="text-sm text-foreground/70">
                        {hotelData.check_out_time || "Before 11:00 AM"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Health & Safety */}
            <div className="bg-[#285DA6]/5 rounded-lg p-5 border border-[#285DA6]/10 shadow-sm">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-[#285DA6]/10 flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#285DA6]"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                </div>
                <h5 className="text-md uppercase tracking-wider text-[#285DA6] font-semibold">
                  Health & Safety
                </h5>
              </div>
              <ul className="space-y-2 pl-2">
                {hotelData?.safety_measures &&
                  hotelData.safety_measures?.length > 0 &&
                  hotelData.safety_measures?.map((measure, index) => (
                    <li key={index} className="flex items-center">
                      <div className="w-1.5 h-1.5 rounded-full bg-[#285DA6] mr-2"></div>
                      <span className="text-sm">{measure}</span>
                    </li>
                  ))}
              </ul>
            </div>
          </div>

          {/* Right Column */}
          <div>
            {/* Cancellation Policy */}
            <div className="bg-[#285DA6]/5 rounded-lg p-5 border border-[#285DA6]/10 shadow-sm h-full">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-[#285DA6]/10 flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#285DA6]"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                  </svg>
                </div>
                <h5 className="text-md uppercase tracking-wider text-[#285DA6] font-semibold">
                  Policies
                </h5>
              </div>
              {hotelData.cancellation_policies &&
              hotelData.cancellation_policies.length > 0 ? (
                <>
                  <div className="mb-4 pl-2">
                    {hotelData.cancellation_policies.map((policy, index) => (
                      <div
                        key={index}
                        className="mb-3 pb-3 border-b border-[#285DA6]/10 last:border-0"
                      >
                        <div className="flex items-center mb-1">
                          <div className="w-1.5 h-1.5 rounded-full bg-[#285DA6] mr-2"></div>
                          <p className="text-md font-medium text-foreground/90">
                            {policy.name}
                          </p>
                        </div>
                        <p className="text-sm text-foreground/70 ml-4 line-clamp-2">
                          {policy.description}
                        </p>
                      </div>
                    ))}
                  </div>
                  <button
                    onClick={() => setShowPoliciesModal(true)}
                    className="mt-2 px-4 py-2 bg-white border border-[#285DA6]/20 rounded-md text-sm text-[#285DA6] font-medium hover:bg-[#285DA6]/5 transition-colors flex items-center justify-center shadow-sm"
                  >
                    <span>Read Policies</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="ml-1"
                    >
                      <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                  </button>
                </>
              ) : (
                <p className="text-sm text-foreground/80 mb-2">
                  Please contact the hotel for cancellation policy details.
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Location teaser */}
      <div
        id="location"
        className="border-b border-border/20 pb-8 mb-8 pt-16 -mt-16"
      >
        <h2 className="text-2xl font-baskervville mb-6 text-[#285DA6]">
          Where to find us
        </h2>
        <div className="rounded-xl overflow-hidden h-[300px]">
          <iframe
            src={`https://maps.google.com/maps?q=${encodeURIComponent(
              hotelData.location || "Switzerland"
            )}&t=&z=13&ie=UTF8&iwloc=&output=embed`}
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
          ></iframe>
        </div>
      </div>

      {/* Cancellation Policies Modal */}
      <PoliciesModal
        isOpen={showPoliciesModal}
        onClose={() => setShowPoliciesModal(false)}
        policies={hotelData.cancellation_policies || []}
      />
    </div>
  );
};

// Helper component for amenity items with checkmark icon
const AmenityItem: React.FC<{ text: string }> = ({ text }) => (
  <li className="flex items-center text-md">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="text-[#285DA6] mr-2"
    >
      <polyline points="20 6 9 17 4 12"></polyline>
    </svg>
    <span>{text}</span>
  </li>
);

export default HotelInformation;
