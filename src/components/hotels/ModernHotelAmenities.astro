---
interface Props {
  amenities: string[];
}

const { amenities } = Astro.props;

// Map common amenities to icons
const getAmenityIcon = (amenity: string) => {
  const lowerAmenity = amenity.toLowerCase();
  
  if (lowerAmenity.includes('wifi') || lowerAmenity.includes('internet')) {
    return `<path d="M5 12.55a11 11 0 0 1 14.08 0"></path><path d="M1.42 9a16 16 0 0 1 21.16 0"></path><path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path><line x1="12" y1="20" x2="12.01" y2="20"></line>`;
  } else if (lowerAmenity.includes('pool') || lowerAmenity.includes('swimming')) {
    return `<path d="M2 12h20"></path><path d="M2 20h20"></path><path d="M6 8l4-4 4 4 4-4"></path><path d="M18 16l-4-4-4 4-4-4"></path>`;
  } else if (lowerAmenity.includes('spa') || lowerAmenity.includes('massage') || lowerAmenity.includes('wellness')) {
    return `<path d="M6.8 11a6 6 0 1 0 10.396 0l-5.197-8-5.2 8Z"></path><path d="M6 11h12"></path>`;
  } else if (lowerAmenity.includes('breakfast') || lowerAmenity.includes('restaurant') || lowerAmenity.includes('dining')) {
    return `<path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path><path d="M7 2v20"></path><path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>`;
  } else if (lowerAmenity.includes('gym') || lowerAmenity.includes('fitness')) {
    return `<path d="m6.5 6.5 11 11"></path><path d="m21 21-1-1"></path><path d="m3 3 1 1"></path><path d="m18 22 4-4"></path><path d="m2 6 4-4"></path><path d="m3 10 7-7"></path><path d="m14 21 7-7"></path>`;
  } else if (lowerAmenity.includes('parking') || lowerAmenity.includes('garage')) {
    return `<rect x="3" y="5" width="18" height="14" rx="2"></rect><path d="M10 9h4"></path><path d="M12 9v6"></path>`;
  } else if (lowerAmenity.includes('air') || lowerAmenity.includes('conditioning')) {
    return `<path d="M8 10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path><path d="M16 10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path><path d="M24 10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path><path d="M2 16h20"></path><path d="M12 10v6"></path>`;
  } else if (lowerAmenity.includes('bar') || lowerAmenity.includes('lounge')) {
    return `<path d="m8 22 8-8"></path><path d="M12 11v11"></path><path d="m19 3-7 8-7-8Z"></path>`;
  } else if (lowerAmenity.includes('ski') || lowerAmenity.includes('snow')) {
    return `<line x1="17" y1="3" x2="5" y2="21"></line><line x1="19" y1="5" x2="7" y2="23"></line><path d="M22 2l-1.5 1.5"></path><path d="M10.5 14.5L14 18"></path><path d="M16 8l-1.5 1.5"></path><path d="M2 22l1.5-1.5"></path>`;
  } else if (lowerAmenity.includes('tv') || lowerAmenity.includes('television')) {
    return `<rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect><polyline points="17 2 12 7 7 2"></polyline>`;
  } else if (lowerAmenity.includes('balcony') || lowerAmenity.includes('terrace') || lowerAmenity.includes('view')) {
    return `<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="3" y1="9" x2="21" y2="9"></line><line x1="9" y1="21" x2="9" y2="9"></line>`;
  } else {
    return `<path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline>`;
  }
};

// Group amenities into categories
const groupedAmenities = {
  'Essentials': amenities.filter(a => 
    a.toLowerCase().includes('wifi') || 
    a.toLowerCase().includes('air') || 
    a.toLowerCase().includes('tv') ||
    a.toLowerCase().includes('parking')
  ),
  'Wellness': amenities.filter(a => 
    a.toLowerCase().includes('spa') || 
    a.toLowerCase().includes('pool') || 
    a.toLowerCase().includes('gym') ||
    a.toLowerCase().includes('fitness') ||
    a.toLowerCase().includes('sauna')
  ),
  'Dining': amenities.filter(a => 
    a.toLowerCase().includes('breakfast') || 
    a.toLowerCase().includes('restaurant') || 
    a.toLowerCase().includes('bar') ||
    a.toLowerCase().includes('dining')
  ),
  'Other': amenities.filter(a => 
    !a.toLowerCase().includes('wifi') && 
    !a.toLowerCase().includes('air') && 
    !a.toLowerCase().includes('tv') &&
    !a.toLowerCase().includes('parking') &&
    !a.toLowerCase().includes('spa') && 
    !a.toLowerCase().includes('pool') && 
    !a.toLowerCase().includes('gym') &&
    !a.toLowerCase().includes('fitness') &&
    !a.toLowerCase().includes('sauna') &&
    !a.toLowerCase().includes('breakfast') && 
    !a.toLowerCase().includes('restaurant') && 
    !a.toLowerCase().includes('bar') &&
    !a.toLowerCase().includes('dining')
  )
};

// Remove empty categories
const categories = Object.entries(groupedAmenities).filter(([_, items]) => items.length > 0);
---

<section id="amenities" class="py-24">
  <div class="container-custom max-w-5xl">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-baskervville mb-4">Amenities</h2>
      <div class="w-20 h-1 bg-primary mx-auto mb-6"></div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-16">
      {categories.map(([category, items]) => (
        <div>
          <h3 class="text-xl font-baskervville mb-6 border-b border-border/30 pb-3">{category}</h3>
          <div class="space-y-4">
            {items.map((amenity) => (
              <div class="flex items-center">
                <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="text-primary"
                    set:html={getAmenityIcon(amenity)}
                  >
                  </svg>
                </div>
                <span class="text-foreground/80">{amenity}</span>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  </div>
</section>
