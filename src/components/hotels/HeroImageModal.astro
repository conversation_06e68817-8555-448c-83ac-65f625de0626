---
// This component is included in the AirbnbStyleHero.astro file
// It provides the JavaScript functionality for the hero image modal
---

<script is:inline>
  document.addEventListener("DOMContentLoaded", () => {
    // Get all hero images
    const heroImages = document.querySelectorAll('.hero-image');
    const modal = document.getElementById('hero-image-modal');
    const modalImage = document.getElementById('hero-modal-image');
    const closeBtn = document.getElementById('close-hero-modal');
    const prevBtn = document.getElementById('prev-hero-image');
    const nextBtn = document.getElementById('next-hero-image');
    const currentIndexEl = document.getElementById('current-hero-image-index');
    const totalImagesEl = document.getElementById('total-hero-images');
    
    // Get all image sources
    const imageSources = [];
    heroImages.forEach((image) => {
      const imgElement = image.querySelector('img');
      if (imgElement && imgElement.src) {
        imageSources.push(imgElement.src);
      }
    });
    
    // Set total images count
    if (totalImagesEl) {
      totalImagesEl.textContent = imageSources.length.toString();
    }
    
    let currentIndex = 0;
    
    // Function to update the modal image
    const updateModalImage = (index) => {
      if (!modalImage) return;
      
      currentIndex = index;
      modalImage.src = imageSources[index];
      
      if (currentIndexEl) {
        currentIndexEl.textContent = (index + 1).toString();
      }
    };
    
    // Add click event to each hero image
    heroImages.forEach((image, index) => {
      image.addEventListener('click', (e) => {
        e.preventDefault();
        if (!modal || !modalImage) return;
        
        // Open modal
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden'; // Prevent scrolling
        
        // Set the current image
        updateModalImage(index);
      });
    });
    
    // Close modal
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        if (!modal) return;
        
        modal.classList.add('hidden');
        document.body.style.overflow = ''; // Restore scrolling
      });
    }
    
    // Navigate to previous image
    if (prevBtn) {
      prevBtn.addEventListener('click', () => {
        const newIndex = (currentIndex - 1 + imageSources.length) % imageSources.length;
        updateModalImage(newIndex);
      });
    }
    
    // Navigate to next image
    if (nextBtn) {
      nextBtn.addEventListener('click', () => {
        const newIndex = (currentIndex + 1) % imageSources.length;
        updateModalImage(newIndex);
      });
    }
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (!modal || modal.classList.contains('hidden')) return;
      
      if (e.key === 'Escape') {
        modal.classList.add('hidden');
        document.body.style.overflow = '';
      } else if (e.key === 'ArrowLeft') {
        const newIndex = (currentIndex - 1 + imageSources.length) % imageSources.length;
        updateModalImage(newIndex);
      } else if (e.key === 'ArrowRight') {
        const newIndex = (currentIndex + 1) % imageSources.length;
        updateModalImage(newIndex);
      }
    });
    
    // Close modal when clicking outside the image
    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.classList.add('hidden');
          document.body.style.overflow = '';
        }
      });
    }
  });
</script>
