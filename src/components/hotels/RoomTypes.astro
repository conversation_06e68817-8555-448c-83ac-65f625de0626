---
interface RoomType {
  id: number | string;
  name: string;
  description: string;
  price: number;
  maxGuests: number;
  bedType: string;
  size: string;
  amenities: string[];
  images: string[];
  // Additional max-related data
  maxAdults?: number;
  maxChildren?: number;
  maxInfants?: number;
  maxExtraBeds?: number;
  // Price details
  priceDetails?: {
    currency_code?: string;
    per_night_amount?: number;
    total_amount?: number;
  };
  currencyCode?: string;
}

interface Props {
  roomTypes: RoomType[];
}

const { roomTypes } = Astro.props;
---

<div>
  <h2 class="text-2xl font-baskervville mb-6">
    Available Room Types
  </h2>
  <div class="space-y-6">
    {roomTypes.length > 0 && (
      <div class="text-xs text-right text-foreground/60 mb-2">
        All prices shown in {roomTypes[0]?.priceDetails?.currency_code || roomTypes[0]?.currencyCode || "USD"}
      </div>
    )}
    {roomTypes.length === 0 ? (
      <div class="p-6 border border-border rounded-lg bg-background/50 text-center">
        <p class="text-foreground/70 mb-2">No rooms are currently available for the selected dates.</p>
        <p class="text-sm">Please try different dates or contact the hotel directly for assistance.</p>
      </div>
    ) : (
      roomTypes.map((room) => (
        <div class="border border-border rounded-lg overflow-hidden">
          <div class="collapsible">
            <div class="grid grid-cols-1 md:grid-cols-3 p-4">
              <div class="md:col-span-1">
                <div class="bg-muted overflow-hidden rounded-lg">
                  <img
                    src={room.images[0]}
                    alt={room.name}
                    class="w-full h-full object-cover rounded-lg"
                    style="aspect-ratio: 4/3;"
                  />
                </div>
              </div>
              <div class="md:col-span-2 p-4">
                <div class="flex justify-between items-start mb-2">
                  <h3 class="text-xl font-baskervville">{room.name}</h3>
                  <div class="text-xl font-baskervville">
                    {room.priceDetails?.currency_code || room.currencyCode || "$"} {room.price}
                    <span class="text-sm text-foreground/70">
                      /night
                    </span>
                  </div>
                </div>
                <p class="text-foreground/70 mb-4">
                  {room.description}
                </p>
                <div class="grid grid-cols-2 gap-2 mb-4">
                  <div class="flex items-center text-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="mr-1"
                    >
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                    </svg>
                    <span>Up to {room.maxGuests} guests</span>
                  </div>
                  <div class="flex items-center text-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="mr-1"
                    >
                      <path d="M2 4v16" />
                      <path d="M22 4v16" />
                      <path d="M2 12h20" />
                      <path d="M12 4v16" />
                    </svg>
                    <span>{room.bedType.charAt(0).toUpperCase() + room.bedType.slice(1)}</span>
                  </div>
                  <div class="flex items-center text-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="mr-1"
                    >
                      <path d="M3 3v18h18" />
                      <path d="M3 15L9 9l4 4 8-8" />
                    </svg>
                    <span>{room.size} sq.m</span>
                  </div>
                </div>
                <div class="flex justify-between items-center">
                  <button
                    class="select-room-btn bg-primary text-primary-foreground hover:opacity-80 px-4 py-2 font-small transition-colors"
                    data-room-id={room.id}
                    data-room-name={room.name}
                    data-room-price={room.price}
                    data-room-currency={room.priceDetails?.currency_code || room.currencyCode || "USD"}
                    data-max-adults={room.maxAdults || room.maxGuests || 2}
                    data-max-children={room.maxChildren || 0}
                    data-max-infants={room.maxInfants || 0}
                    data-max-occupancy={room.maxGuests || 2}
                  >
                    Select
                  </button>
                  <button class="collapsible-trigger flex items-center text-sm text-primary hover:text-primary/80 transition-colors" data-room-id={room.id}>
                    <span class="show-details">Show details</span>
                    <span class="hide-details hidden">Hide details</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="chevron-down ml-1"
                    >
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="chevron-up ml-1 hidden"
                    >
                      <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- Collapsible Content -->
            <div class="collapsible-content hidden px-6 pb-6">
              <!-- Room Capacity Details -->
              <div class="mb-6">
                <h4 class="text-md font-medium mb-3">Room Max Capacity</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Max Adults */}
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="text-primary mr-2"
                    >
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                    </svg>
                    <span class="text-sm">Adults: {room.maxAdults || room.maxGuests || 2}</span>
                  </div>

                  {/* Max Children */}
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="text-primary mr-2"
                    >
                      <path d="M14 21h-5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5" />
                      <path d="M9 3v18" />
                      <path d="M14 8h2.5a2 2 0 0 1 1.6.8L21 12l-2.9 3.2a2 2 0 0 1-1.6.8H14" />
                    </svg>
                    <span class="text-sm">Children: {room.maxChildren || 0}</span>
                  </div>

                  {/* Max Infants */}
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="text-primary mr-2"
                    >
                      <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z" />
                      <path d="M12 18v4" />
                      <path d="M8 18h8" />
                    </svg>
                    <span class="text-sm">Infants: {room.maxInfants || 0}</span>
                  </div>

                  {/* Max Extra Beds */}
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="text-primary mr-2"
                    >
                      <path d="M2 4v16" />
                      <path d="M22 4v16" />
                      <path d="M2 12h20" />
                      <path d="M12 4v16" />
                    </svg>
                    <span class="text-sm">Extra beds: {room.maxExtraBeds || 0}</span>
                  </div>
                </div>
              </div>

              <!-- Room Description -->
              <div class="mb-6">
                <h4 class="text-md font-medium mb-3">Room Description</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                 {room.description}
                </div>
              </div>

              <!-- Room Gallery -->
              <div class="mt-6">
                <h4 class="text-md font-medium mb-3">Room Gallery</h4>
                <div class="room-gallery relative w-full">
                  <div class="room-carousel-container overflow-hidden">
                    <div class="room-carousel-track flex gap-4" data-room-id={room.id}>
                      {room.images.map((image, index) => (
                        <div class="flex-none w-full md:w-1/2 lg:w-1/3 p-1">
                          <div class="relative aspect-square rounded-lg overflow-hidden">
                            <img
                              src={image}
                              alt={`${room.name} - Image ${index + 1}`}
                              class="object-cover w-full h-full"
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div class="flex justify-center mt-2">
                    <button class="room-carousel-prev mx-1 bg-background border border-border rounded-full w-8 h-8 flex items-center justify-center" data-room-id={room.id}>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path d="M15 18l-6-6 6-6" />
                      </svg>
                    </button>
                    <button class="room-carousel-next mx-1 bg-background border border-border rounded-full w-8 h-8 flex items-center justify-center" data-room-id={room.id}>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path d="M9 18l6-6-6-6" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))
    )}
  </div>
</div>
