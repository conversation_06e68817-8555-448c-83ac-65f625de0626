---
// Props for the component
interface Props {
  id: number | string;
  name: string;
  location: string;
  imageUrl: string;
  price: number;
  currency?: string;
  rating: number;
  poeticDesc?: string;
  tags?: string[] | string[][];
  index?: number;
}

const {
  id,
  name,
  location,
  imageUrl,
  price,
  currency = "$",
  rating,
  poeticDesc,
  tags = [],
  index = 0,
} = Astro.props;

// Handle tags array
let displayTags: string[] = [];
if (tags?.length > 0) {
  if (Array.isArray(tags[0])) {
    displayTags = (tags as string[][])[index % tags.length];
  } else {
    displayTags = tags as string[];
  }
}
---

<div
  class="bg-card overflow-hidden shadow-sm border border-border/10 rounded-md transition-all duration-300 hover:shadow-md hover:border-foreground/20 group hover:-translate-y-1"
>
  <a href={`/stays/${id}`} class="block">
    <div class="relative">
      <div class="aspect-[4/3] overflow-hidden">
        <img
          src={imageUrl}
          alt={name}
          class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110 rounded-t-md"
        />
      </div>
    </div>

    <div class="p-5">
      <div class="flex justify-between items-start mb-2">
        <h3 class="text-xl font-baskervville leading-tight">{name}</h3>
        <div class="flex items-center bg-primary/10 px-2 py-1 rounded-full">
          <span class="text-sm font-karla font-bold text-primary">
            {rating}
          </span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="w-4 h-4 text-primary ml-1"
          >
            <path
              fill-rule="evenodd"
              d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
              clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>

      {
        poeticDesc && (
          <p class="text-foreground/80 text-sm italic font-light font-baskervville mb-3 line-clamp-2">
            {poeticDesc}
          </p>
        )
      }

      <div class="flex items-center text-foreground/70 text-sm mb-3">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="mr-1.5"
        >
          <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
          <circle cx="12" cy="10" r="3"></circle>
        </svg>
        {location}
      </div>

      {
        displayTags?.length > 0 && (
          <div class="flex flex-wrap gap-2 mb-4">
            {displayTags.map((tag) => (
              <span class="hotel-tag text-xs px-2 py-1 rounded-full bg-foreground/5 text-foreground/70 border border-foreground/10">
                {tag}
              </span>
            ))}
          </div>
        )
      }

      <div
        class="flex justify-between items-center mt-4 pt-4 border-t border-border/20"
      >
        <div class="text-primary font-karla font-bold">
          {currency}
          {price}
          <span class="text-foreground/60 font-normal text-sm">
            {" "}
            / night
          </span>
        </div>

        <span
          class="text-sm text-foreground/70 hover:text-primary transition-colors flex items-center"
        >
          View Details
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="ml-1 transform group-hover:translate-x-1 transition-transform duration-300"
          >
            <line x1="5" y1="12" x2="19" y2="12"></line>
            <polyline points="12 5 19 12 12 19"></polyline>
          </svg>
        </span>
      </div>
    </div>
  </a>
</div>
