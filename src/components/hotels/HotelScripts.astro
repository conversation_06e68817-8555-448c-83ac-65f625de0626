---
// This component contains all the JavaScript functionality for the hotel details page
---

<script>
  // Client-side functionality
  document.addEventListener("DOMContentLoaded", () => {
    // Image Carousel functionality
    const track = document.getElementById("image-track") as HTMLElement;
    const items = track?.querySelectorAll(".carousel-item");
    const prevBtn = document.querySelector(".carousel-prev");
    const nextBtn = document.querySelector(".carousel-next");
    const indicators = document.querySelectorAll(".carousel-indicator");
    let currentIndex = 0;
    const itemCount = items?.length || 0;

    // Set the first indicator as active
    if (indicators.length > 0) {
      indicators[0].classList.add("bg-primary");
    }

    // Function to update the carousel position
    const updateCarousel = (index: number) => {
      if (!track || !items) return;

      // Update the transform to show the current slide
      track.style.transform = `translateX(-${index * 100}%)`;

      // Update indicators
      indicators.forEach((indicator, i) => {
        if (i === index) {
          indicator.classList.remove("bg-foreground/20");
          indicator.classList.add("bg-primary");
        } else {
          indicator.classList.remove("bg-primary");
          indicator.classList.add("bg-foreground/20");
        }
      });

      currentIndex = index;
    };

    // Event listeners for prev/next buttons
    prevBtn?.addEventListener("click", () => {
      const newIndex = (currentIndex - 1 + itemCount) % itemCount;
      updateCarousel(newIndex);
    });

    nextBtn?.addEventListener("click", () => {
      const newIndex = (currentIndex + 1) % itemCount;
      updateCarousel(newIndex);
    });

    // Event listeners for indicators
    indicators.forEach((indicator, index) => {
      indicator.addEventListener("click", () => {
        updateCarousel(index);
      });
    });

    // Fix date picker modal positioning
    const fixDatePickerModal = () => {
      const datePickerModal = document.getElementById("date-picker-modal");
      if (datePickerModal) {
        // Ensure the modal is properly positioned
        datePickerModal.style.position = "fixed";
        datePickerModal.style.top = "0";
        datePickerModal.style.left = "0";
        datePickerModal.style.right = "0";
        datePickerModal.style.bottom = "0";
        datePickerModal.style.zIndex = "9999";
        datePickerModal.style.display = "flex";
        datePickerModal.style.alignItems = "center";
        datePickerModal.style.justifyContent = "center";

        // Find the modal content and ensure it's properly styled
        const modalContent = datePickerModal.querySelector("div");
        if (modalContent) {
          modalContent.style.position = "relative";
          modalContent.style.maxHeight = "90vh";
          modalContent.style.overflowY = "auto";
          modalContent.style.margin = "20px";
          modalContent.style.backgroundColor = "white";
          modalContent.style.borderRadius = "0.5rem";
          modalContent.style.boxShadow =
            "0 25px 50px -12px rgba(0, 0, 0, 0.25)";
        }
      }
    };

    // Fix date picker modal when it's opened
    document.addEventListener("click", (e) => {
      const target = e.target as HTMLElement;
      if (
        target.closest("button") &&
        target.textContent?.includes("Modify Dates")
      ) {
        setTimeout(fixDatePickerModal, 10);
      }
    });

    // Also fix the modal when the page loads
    setTimeout(fixDatePickerModal, 500);
  });
</script>
