---
interface CancellationPolicy {
  id: string;
  name: string;
  description: string;
  days_before_checkin: number;
  refund_type: string;
  refund_amount: number;
}

interface Props {
  policies: CancellationPolicy[];
}

const { policies = [] } = Astro.props;

// Sort policies by days_before_checkin (descending)
const sortedPolicies = [...policies].sort((a, b) => b.days_before_checkin - a.days_before_checkin);
---

{policies.length > 0 && (
  <div class="mt-10">
    <h2 class="text-2xl font-baskervville mb-6">
      Cancellation Policies
    </h2>
    <div class="space-y-4">
      {sortedPolicies.map((policy) => (
        <div class="border border-border rounded-lg overflow-hidden">
          <div class="collapsible">
            <div class="p-4">
              <div class="grid grid-cols-4 items-center">
                <h3 class="text-lg font-medium col-span-2">{policy.name}</h3>
                <div class="text-sm px-2 py-1 bg-primary/10 text-primary rounded-md justify-self-center">
                  {policy.days_before_checkin} {policy.days_before_checkin === 1 ? 'day' : 'days'} before check-in
                </div>
                <button class="collapsible-trigger flex items-center text-sm text-primary hover:text-primary/80 transition-colors justify-self-end" data-policy-id={policy.id}>
                  <span class="show-details">Show details</span>
                  <span class="hide-details hidden">Hide details</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="ml-1 chevron-down"
                  >
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="ml-1 chevron-up hidden"
                  >
                    <polyline points="18 15 12 9 6 15"></polyline>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Collapsible Content -->
            <div class="collapsible-content hidden px-4 pb-4">
              <p class="text-foreground/70 mb-3">{policy.description}</p>
              <div class="flex items-center text-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-primary mr-2"
                >
                  {policy.refund_type === "no_refund" ? (
                    <><circle cx="12" cy="12" r="10" /><line x1="15" y1="9" x2="9" y2="15" /><line x1="9" y1="9" x2="15" y2="15" /></>
                  ) : (
                    <><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" /><polyline points="22 4 12 14.01 9 11.01" /></>
                  )}
                </svg>
                <span>
                  {policy.refund_type === "percentage"
                    ? `${policy.refund_amount}% refund`
                    : policy.refund_type === "no_refund"
                      ? "No refund"
                      : "Full refund"}
                </span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
)}
