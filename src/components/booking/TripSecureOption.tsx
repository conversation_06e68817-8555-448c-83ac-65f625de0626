import React from "react";

interface TripSecureOptionProps {
  tripInsurance: boolean;
  setTripInsurance: (value: boolean) => void;
  insuranceAmount: number;
  currencyCode: string;
}

const TripSecureOption: React.FC<TripSecureOptionProps> = ({
  tripInsurance,
  setTripInsurance,
  insuranceAmount,
  currencyCode,
}) => {
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm overflow-hidden">
      {/* Banner */}
      <div className="bg-green-50 p-4 flex items-center">
        <div className="text-blue-500 mr-3">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
            <line x1="9" y1="9" x2="9.01" y2="9"></line>
            <line x1="15" y1="9" x2="15.01" y2="9"></line>
          </svg>
        </div>
        <p className="text-green-800">
          Chase the adrenaline rush worry-free! Secure your trip against sudden events
        </p>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-bold text-green-800">Trip Secure</h3>
            <p className="text-sm text-gray-600">Enjoy a Worry-Free Stay</p>
          </div>
          <div className="flex space-x-2">
            <img
              src="/images/allianz-logo.png"
              alt="Allianz"
              className="h-8 w-auto object-contain"
              onError={(e) => {
                e.currentTarget.src = "https://via.placeholder.com/80x30?text=Allianz";
              }}
            />
            <img
              src="/images/icici-logo.png"
              alt="ICICI"
              className="h-8 w-auto object-contain"
              onError={(e) => {
                e.currentTarget.src = "https://via.placeholder.com/80x30?text=ICICI";
              }}
            />
          </div>
        </div>

        {/* Benefits */}
        <div className="bg-blue-50 p-4 rounded-lg mb-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="flex items-center">
              <div className="text-blue-500 mr-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </div>
              <div>
                <p className="text-sm">Medical Assistance</p>
                <p className="text-xs text-gray-500">24*7 SUPPORT</p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="text-blue-500 mr-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </div>
              <div>
                <p className="text-sm">Personal Accident</p>
                <p className="text-xs text-gray-500">Rs 10,00,000</p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="text-blue-500 mr-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </div>
              <div>
                <p className="text-sm">OPD Expenses</p>
                <p className="text-xs text-gray-500">Rs 25,000</p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="text-blue-500 mr-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </div>
              <div>
                <p className="text-sm">Refund on Hotel Cancellation</p>
                <p className="text-xs text-gray-500">Rs 15,000</p>
              </div>
            </div>
          </div>
          <button className="text-[#3566ab] text-sm hover:underline mt-3">
            9 more benefits
          </button>
        </div>

        {/* Price */}
        <div className="mb-4">
          <p className="text-lg font-bold">
            {currencyCode} {formatCurrency(insuranceAmount)}
            <span className="text-sm font-normal text-gray-500 ml-2">
              per person per night
            </span>
          </p>
          <p className="text-xs text-gray-500">18% GST Included</p>
        </div>

        {/* Options */}
        <div className="space-y-2">
          <div className="border border-gray-200 rounded-md p-3">
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                checked={tripInsurance}
                onChange={() => setTripInsurance(true)}
                className="w-4 h-4 text-[#3566ab] border-gray-300 focus:ring-[#3566ab]"
              />
              <span className="ml-2 text-sm font-medium">Yes, secure my trip.</span>
            </label>
          </div>
          <div className="border border-gray-200 rounded-md p-3">
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                checked={!tripInsurance}
                onChange={() => setTripInsurance(false)}
                className="w-4 h-4 text-[#3566ab] border-gray-300 focus:ring-[#3566ab]"
              />
              <span className="ml-2 text-sm font-medium">
                No, I will book without trip secure.
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TripSecureOption;
