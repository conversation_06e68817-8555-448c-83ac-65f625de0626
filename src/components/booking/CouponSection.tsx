import React, { useState } from "react";

interface CouponSectionProps {
  selectedCoupon: string;
  setSelectedCoupon: (coupon: string) => void;
  currencyCode: string;
}

// Available coupons data
const availableCoupons = [
  {
    id: "MMTDE<PERSON>",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    description: "Congratulations! Discount of INR 372 Applied",
    discount: 372,
  },
  {
    id: "MMTHSBC",
    name: "MMTHSBC",
    description: "HSBC Bank Credit Card Offer - Get INR 539 Off!",
    discount: 539,
  },
  {
    id: "HSBCEMI",
    name: "HSBCEMI",
    description: "HSBC Bank Credit Card NoCosteEMI Offer - Get INR 539 Off",
    discount: 539,
  },
  {
    id: "MMT<PERSON>IE<PERSON>",
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    description: "SBI Credit Card EMI Paymode Offer - Get INR 431 Off!",
    discount: 431,
  },
  {
    id: "KOTAKEMI",
    name: "KOTAKEMI",
    description: "KOTAK Credit Card EMI Paymode Offer - Get INR 431 Off!",
    discount: 431,
  },
  {
    id: "MMTPNB",
    name: "MMTPNB",
    description: "PNB Credit Card Users Exclusive Offer - Get INR 539 Off",
    discount: 539,
  },
];

const CouponSection: React.FC<CouponSectionProps> = ({
  selectedCoupon,
  setSelectedCoupon,
  currencyCode,
}) => {
  const [showCouponInput, setShowCouponInput] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [error, setError] = useState("");

  // Handle coupon selection
  const handleCouponSelect = (couponId: string) => {
    setSelectedCoupon(couponId);
  };

  // Handle coupon code submission
  const handleCouponSubmit = () => {
    if (!couponCode.trim()) {
      setError("Please enter a coupon code");
      return;
    }

    // Check if coupon exists
    const coupon = availableCoupons.find(
      (c) => c.id.toLowerCase() === couponCode.toLowerCase()
    );

    if (coupon) {
      setSelectedCoupon(coupon.id);
      setError("");
      setShowCouponInput(false);
    } else {
      setError("Invalid coupon code");
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Coupon Codes</h3>
        <button
          className="text-[#3566ab] text-sm hover:underline"
          onClick={() => setShowCouponInput(!showCouponInput)}
        >
          View All
        </button>
      </div>

      {/* Available Coupons */}
      <div className="space-y-4">
        {availableCoupons.slice(0, 3).map((coupon) => (
          <div
            key={coupon.id}
            className={`border rounded-md p-4 ${
              selectedCoupon === coupon.id
                ? "border-[#3566ab]"
                : "border-gray-200"
            }`}
          >
            <div className="flex items-start">
              <input
                type="radio"
                id={`coupon-${coupon.id}`}
                name="coupon"
                checked={selectedCoupon === coupon.id}
                onChange={() => handleCouponSelect(coupon.id)}
                className="mt-1 w-4 h-4 text-[#3566ab] border-gray-300 focus:ring-[#3566ab]"
              />
              <div className="ml-3 flex-1">
                <div className="flex justify-between">
                  <label
                    htmlFor={`coupon-${coupon.id}`}
                    className="font-medium cursor-pointer"
                  >
                    {coupon.name}
                  </label>
                  <span className="font-medium text-[#3566ab]">
                    {currencyCode} {formatCurrency(coupon.discount)}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">{coupon.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Coupon Input */}
      {showCouponInput ? (
        <div className="mt-4">
          <div className="flex">
            <input
              type="text"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value)}
              placeholder="Enter Coupon Code"
              className="flex-1 border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-[#3566ab] focus:border-[#3566ab]"
            />
            <button
              onClick={handleCouponSubmit}
              className="bg-[#3566ab] text-white px-4 rounded-r-md hover:bg-[#3566ab]/90"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="5" y1="12" x2="19" y2="12" />
                <polyline points="12 5 19 12 12 19" />
              </svg>
            </button>
          </div>
          {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
        </div>
      ) : (
        <div className="mt-4">
          <button
            onClick={() => setShowCouponInput(true)}
            className="text-sm text-gray-600 hover:text-[#3566ab] flex items-center"
          >
            <span className="mr-1">Have a Coupon Code</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="5" y1="12" x2="19" y2="12" />
              <polyline points="12 5 19 12 12 19" />
            </svg>
          </button>
        </div>
      )}

      {/* Info Banner */}
      <div className="mt-4 bg-yellow-50 border border-yellow-100 rounded-md p-3 text-sm text-yellow-800">
        Perfect Piste Gift Cards can be applied at payment step
      </div>
    </div>
  );
};

export default CouponSection;
