import React, { useState, useEffect, useRef } from "react";
import { dialCodes } from "../../constants";
import type { Traveler } from "../../utils/store/cart";

interface GuestDetailsFormProps {
  guestDetails: {
    title: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    specialRequests: string;
  };
  setGuestDetails: React.Dispatch<
    React.SetStateAction<{
      title: string;
      firstName: string;
      lastName: string;
      email: string;
      phone: string;
      specialRequests: string;
    }>
  >;
  travelers: {
    adults: Traveler[];
    children: Traveler[];
    infants: Traveler[];
  };
  setTravelers: React.Dispatch<
    React.SetStateAction<{
      adults: Traveler[];
      children: Traveler[];
      infants: Traveler[];
    }>
  >;
  maxAdults: number;
  maxChildren: number;
  maxInfants: number;
  formErrors: Record<string, string>;
  userData?: {
    first_name?: string;
    last_name?: string;
    email?: string;
    phone?: string;
  } | null;
  isAuthenticated?: boolean;
  clearFieldError?: (fieldName: string) => void;
  termsAccepted: boolean;
  setTermsAccepted: React.Dispatch<React.SetStateAction<boolean>>;
  setShowPoliciesModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const GuestDetailsForm: React.FC<GuestDetailsFormProps> = ({
  guestDetails,
  setGuestDetails,
  travelers,
  setTravelers,
  maxAdults,
  maxChildren,
  maxInfants,
  formErrors,
  userData,
  isAuthenticated = false,
  clearFieldError,
  termsAccepted,
  setTermsAccepted,
  setShowPoliciesModal,
}) => {
  const [selectedDialCode, setSelectedDialCode] = useState("+41");
  const [isDialCodeDropdownOpen, setIsDialCodeDropdownOpen] = useState(false);
  const [dialCodeSearch, setDialCodeSearch] = useState("");
  const dialCodeDropdownRef = useRef<HTMLDivElement>(null);

  // Guest management state
  const [showAddForm, setShowAddForm] = useState(false);
  const [newGuest, setNewGuest] = useState({
    name: "",
    age: "",
    type: "adults" as "adults" | "children" | "infants",
  });
  const [isGuestTypeDropdownOpen, setIsGuestTypeDropdownOpen] = useState(false);
  const guestTypeDropdownRef = useRef<HTMLDivElement>(null);
  const [ageError, setAgeError] = useState("");

  // Age validation function
  const validateAge = (age: string, guestType: "adults" | "children" | "infants"): string => {
    if (!age.trim()) {
      if (guestType === "children" || guestType === "infants") {
        return "Age is required for children and infants";
      }
      return "";
    }

    const ageNum = parseInt(age);

    if (isNaN(ageNum) || ageNum < 0) {
      return "Please enter a valid age";
    }

    switch (guestType) {
      case "children":
        if (ageNum < 2 || ageNum > 11) {
          return "Children must be between 2-11 years old";
        }
        break;
      case "infants":
        if (ageNum < 0 || ageNum > 2) {
          return "Infants must be between 0-2 years old";
        }
        break;
      case "adults":
        if (ageNum < 18) {
          return "Adults must be 18 years or older";
        }
        if (ageNum > 120) {
          return "Please enter a valid age";
        }
        break;
    }

    return "";
  };

  // Handle guest type selection
  const handleGuestTypeSelect = (type: "adults" | "children" | "infants") => {
    setNewGuest((prev) => ({ ...prev, type, age: "" }));
    setIsGuestTypeDropdownOpen(false);
    setAgeError("");
  };

  // Handle age change with validation
  const handleAgeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const age = e.target.value;
    setNewGuest((prev) => ({ ...prev, age }));

    // Clear error when user starts typing
    if (ageError) {
      setAgeError("");
    }

    // Validate age if not empty
    if (age.trim()) {
      const validationError = validateAge(age, newGuest.type);
      if (validationError) {
        setAgeError(validationError);
      }
    }
  };

  // Filter dial codes based on search
  const filteredDialCodes = dialCodes.filter(
    (country) =>
      country.country.toLowerCase().includes(dialCodeSearch.toLowerCase()) ||
      country.code.includes(dialCodeSearch)
  );

  // Handle dial code selection
  const handleDialCodeSelect = (code: string) => {
    setSelectedDialCode(code);
    setIsDialCodeDropdownOpen(false);
    setDialCodeSearch("");
  };

  // Handle click outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dialCodeDropdownRef.current &&
        !dialCodeDropdownRef.current.contains(event.target as Node)
      ) {
        setIsDialCodeDropdownOpen(false);
        setDialCodeSearch("");
      }

      if (
        guestTypeDropdownRef.current &&
        !guestTypeDropdownRef.current.contains(event.target as Node)
      ) {
        setIsGuestTypeDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Prefill form when user is authenticated
  useEffect(() => {
    if (isAuthenticated && userData) {
      setGuestDetails((prev) => ({
        ...prev,
        firstName: userData.first_name || "",
        lastName: userData.last_name || "",
        email: userData.email || "",
        phone: userData.phone || "",
      }));
    }
  }, [isAuthenticated, userData, setGuestDetails]);

  // Handle input change
  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setGuestDetails((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear field error when user starts typing
    if (clearFieldError) {
      clearFieldError(name);
    }
  };

  // Guest management functions
  const handleAddGuest = () => {
    if (!newGuest.name.trim()) return;

    // Validate age
    const ageValidationError = validateAge(newGuest.age, newGuest.type);
    if (ageValidationError) {
      setAgeError(ageValidationError);
      return;
    }

    const guestData: Traveler = {
      name: newGuest.name.trim(),
      ...(newGuest.age && { age: parseInt(newGuest.age) }),
    };

    setTravelers((prev) => ({
      ...prev,
      [newGuest.type]: [...prev[newGuest.type], guestData],
    }));

    setNewGuest({
      name: "",
      age: "",
      type: "adults",
    });
    setAgeError("");
    setShowAddForm(false);
  };

  const handleRemoveGuest = (
    type: "adults" | "children" | "infants",
    index: number
  ) => {
    setTravelers((prev) => ({
      ...prev,
      [type]: prev[type].filter((_, i) => i !== index),
    }));
  };

  const canAddGuest = (type: "adults" | "children" | "infants") => {
    switch (type) {
      case "adults":
        // Account for the lead guest (primary contact) who is always an adult
        // maxAdults represents total adults selected, so additional adults = maxAdults - 1
        return travelers.adults.length < maxAdults - 1;
      case "children":
        // maxChildren represents total children selected
        return travelers.children.length < maxChildren;
      case "infants":
        // maxInfants represents total infants selected
        return travelers.infants.length < maxInfants;
      default:
        return false;
    }
  };

  const getTotalGuests = () => {
    return (
      travelers.adults.length +
      travelers.children.length +
      travelers.infants.length
    );
  };

  const getTotalGuestsIncludingLead = () => {
    // Include the lead guest (primary contact) in the total count
    return (
      1 + // Lead guest
      travelers.adults.length +
      travelers.children.length +
      travelers.infants.length
    );
  };

  const hasCapacityForMoreGuests = () => {
    const totalSelectedGuests = maxAdults + maxChildren + maxInfants;
    return getTotalGuestsIncludingLead() < totalSelectedGuests;
  };

  const canAddAnyGuest = () => {
    return (
      canAddGuest("adults") || canAddGuest("children") || canAddGuest("infants")
    );
  };

  const shouldShowOtherGuestsSection = () => {
    const totalSelectedGuests = maxAdults + maxChildren + maxInfants;
    // Only show if user selected more than 1 guest total (since lead guest is always 1 adult)
    // OR if there are already additional guests added
    return totalSelectedGuests > 1 || getTotalGuests() > 0;
  };

  // Guest type options (defined after canAddGuest function)
  const getGuestTypeOptions = () => [
    {
      value: "adults" as const,
      label: "Adult",
      icon: "👤",
      description: "18+ years",
      available: canAddGuest("adults"),
    },
    {
      value: "children" as const,
      label: "Child",
      icon: "🧒",
      description: "2-11 years",
      available: canAddGuest("children"),
    },
    {
      value: "infants" as const,
      label: "Infant",
      icon: "👶",
      description: "0-2 years",
      available: canAddGuest("infants"),
    },
  ];

  return (
    <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-bold mb-4 text-[#3566ab]">
        Lead Guest Details
      </h3>

      {/* Guest Name */}
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
          <div className="md:col-span-6">
            <label className="block text-xs text-gray-500 uppercase mb-1">
              First Name
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={guestDetails.firstName}
              onChange={handleInputChange}
              placeholder="First Name"
              className={`w-full border ${
                formErrors.firstName ? "border-red-500" : "border-gray-300"
              } rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-[#3566ab] focus:border-[#3566ab]`}
            />
            {formErrors.firstName && (
              <p className="text-red-500 text-xs mt-1">
                {formErrors.firstName}
              </p>
            )}
          </div>
          <div className="md:col-span-6">
            <label className="block text-xs text-gray-500 uppercase mb-1">
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={guestDetails.lastName}
              onChange={handleInputChange}
              placeholder="Last Name"
              className={`w-full border ${
                formErrors.lastName ? "border-red-500" : "border-gray-300"
              } rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-[#3566ab] focus:border-[#3566ab]`}
            />
            {formErrors.lastName && (
              <p className="text-red-500 text-xs mt-1">{formErrors.lastName}</p>
            )}
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-xs text-gray-500 uppercase mb-1">
              Email Address
              <span className="text-xs font-normal normal-case text-gray-400 ml-1">
                (Booking voucher will be sent to this email ID)
              </span>
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={guestDetails.email}
              onChange={handleInputChange}
              placeholder="Email Address"
              className={`w-full border ${
                formErrors.email ? "border-red-500" : "border-gray-300"
              } rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-[#3566ab] focus:border-[#3566ab]`}
            />
            {formErrors.email && (
              <p className="text-red-500 text-xs mt-1">{formErrors.email}</p>
            )}
          </div>
          <div>
            <label className="block text-xs text-gray-500 uppercase mb-1">
              Mobile Number
            </label>
            <div className="flex">
              <div className="relative w-20" ref={dialCodeDropdownRef}>
                <button
                  type="button"
                  onClick={() =>
                    setIsDialCodeDropdownOpen(!isDialCodeDropdownOpen)
                  }
                  className="w-full h-full border border-gray-300 rounded-l-md px-2 py-2 focus:outline-none focus:ring-1 focus:ring-[#3566ab] focus:border-[#3566ab] bg-gray-50 text-sm flex items-center justify-between"
                >
                  <span className="truncate">{selectedDialCode}</span>
                  <svg
                    className={`w-4 h-4 transition-transform ${
                      isDialCodeDropdownOpen ? "rotate-180" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>

                {isDialCodeDropdownOpen && (
                  <div className="absolute top-full left-0 w-80 z-50 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
                    <div className="p-2 border-b border-gray-200">
                      <input
                        type="text"
                        placeholder="Search country or code..."
                        value={dialCodeSearch}
                        onChange={(e) => setDialCodeSearch(e.target.value)}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-[#3566ab] focus:border-[#3566ab]"
                        autoFocus
                      />
                    </div>
                    <div className="max-h-48 overflow-y-auto">
                      {filteredDialCodes.length > 0 ? (
                        filteredDialCodes.map((country, index) => (
                          <button
                            key={`${country.code}-${index}`}
                            type="button"
                            onClick={() => handleDialCodeSelect(country.code)}
                            className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center justify-between ${
                              selectedDialCode === country.code
                                ? "bg-blue-50 text-[#3566ab]"
                                : ""
                            }`}
                          >
                            <span className="truncate">{country.country}</span>
                            <span className="text-gray-500 ml-2 flex-shrink-0">
                              {country.code}
                            </span>
                          </button>
                        ))
                      ) : (
                        <div className="px-3 py-2 text-sm text-gray-500">
                          No countries found
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={guestDetails.phone}
                onChange={handleInputChange}
                placeholder="Phone Number"
                className={`flex-1 border ${
                  formErrors.phone ? "border-red-500" : "border-gray-300"
                } border-l-0 rounded-r-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-[#3566ab] focus:border-[#3566ab]`}
              />
            </div>
            {formErrors.phone && (
              <p className="text-red-500 text-xs mt-1">{formErrors.phone}</p>
            )}
          </div>
        </div>
      </div>

      {/* Special Requests */}
      <div className="mb-6">
        <label className="block text-xs text-gray-500 uppercase mb-1">
          Special Requests (Optional)
        </label>
        <textarea
          name="specialRequests"
          value={guestDetails.specialRequests}
          onChange={handleInputChange}
          placeholder="Any special requests for your stay?"
          rows={3}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-[#3566ab] focus:border-[#3566ab]"
        ></textarea>
      </div>

      {/* Additional Travelers Section - Only show if booking allows more than 1 guest total */}
      {shouldShowOtherGuestsSection() && (
        <div className="mb-6 border-t border-gray-200 pt-6">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h4 className="text-lg font-semibold text-[#3566ab] mb-1">
                Other Guest Details
              </h4>
              <p className="text-sm text-gray-500">
                {hasCapacityForMoreGuests()
                  ? "Add other guests traveling with you"
                  : "Other guests traveling with you"}
              </p>
              {!hasCapacityForMoreGuests() && getTotalGuests() === 0 && (
                <p className="text-xs text-amber-600 mt-1">
                  Maximum capacity reached for this booking
                </p>
              )}
            </div>
            {!showAddForm && hasCapacityForMoreGuests() && canAddAnyGuest() && (
              <button
                type="button"
                onClick={() => setShowAddForm(true)}
                className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-[#3566ab] to-[#2a5490] text-white text-sm font-medium rounded-lg hover:from-[#2a5490] hover:to-[#1e3f73] transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
              >
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Add Guest
              </button>
            )}
          </div>

          {/* Add Guest Form */}
          {showAddForm && (
            <div className="mb-6 p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
              <div className="flex justify-between items-center mb-4">
                <h5 className="text-md font-semibold text-[#3566ab]">
                  Add New Guest
                </h5>
                <button
                  type="button"
                  onClick={() => {
                    setShowAddForm(false);
                    setNewGuest({ name: "", age: "", type: "adults" });
                  }}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Guest Type
                  </label>
                  <div className="relative" ref={guestTypeDropdownRef}>
                    <button
                      type="button"
                      onClick={() =>
                        setIsGuestTypeDropdownOpen(!isGuestTypeDropdownOpen)
                      }
                      className="w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left focus:outline-none focus:ring-2 focus:ring-[#3566ab]/20 focus:border-[#3566ab] transition-all shadow-sm hover:border-gray-400 flex items-center justify-between"
                    >
                      <div className="flex items-center">
                        <span className="text-lg mr-3">
                          {
                            getGuestTypeOptions().find(
                              (option) => option.value === newGuest.type
                            )?.icon
                          }
                        </span>
                        <div>
                          <span className="font-medium text-gray-900">
                            {
                              getGuestTypeOptions().find(
                                (option) => option.value === newGuest.type
                              )?.label
                            }
                          </span>
                          <span className="text-sm text-gray-500 ml-2">
                            {
                              getGuestTypeOptions().find(
                                (option) => option.value === newGuest.type
                              )?.description
                            }
                          </span>
                        </div>
                      </div>
                      <svg
                        className={`w-5 h-5 text-gray-400 transition-transform ${
                          isGuestTypeDropdownOpen ? "rotate-180" : ""
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </button>

                    {isGuestTypeDropdownOpen && (
                      <div className="absolute top-full left-0 w-full z-50 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 overflow-hidden">
                        {getGuestTypeOptions().map((option) => (
                          <button
                            key={option.value}
                            type="button"
                            onClick={() => handleGuestTypeSelect(option.value)}
                            disabled={!option.available}
                            className={`w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors flex items-center ${
                              !option.available
                                ? "opacity-50 cursor-not-allowed bg-gray-50"
                                : "hover:bg-blue-50"
                            } ${
                              newGuest.type === option.value
                                ? "bg-blue-50 border-l-4 border-[#3566ab]"
                                : ""
                            }`}
                          >
                            <span className="text-lg mr-3">{option.icon}</span>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <span className="font-medium text-gray-900">
                                  {option.label}
                                </span>
                                {!option.available && (
                                  <span className="text-xs text-red-500 font-medium">
                                    Max reached
                                  </span>
                                )}
                              </div>
                              <span className="text-sm text-gray-500">
                                {option.description}
                              </span>
                            </div>
                            {newGuest.type === option.value && (
                              <svg
                                className="w-5 h-5 text-[#3566ab] ml-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M5 13l4 4L19 7"
                                />
                              </svg>
                            )}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={newGuest.name}
                      onChange={(e) =>
                        setNewGuest((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                      placeholder="Enter full name"
                      className="w-full border border-gray-300 rounded-lg px-4 py-3 pl-10 focus:outline-none focus:ring-2 focus:ring-[#3566ab]/20 focus:border-[#3566ab] transition-all shadow-sm hover:border-gray-400"
                    />
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <svg
                        className="w-5 h-5 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Age{" "}
                    <span className="text-xs text-gray-500 font-normal">
                      {newGuest.type === "children"
                        ? "(2-11 years)"
                        : newGuest.type === "infants"
                        ? "(0-2 years)"
                        : "(18+ years, optional)"}
                    </span>
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      value={newGuest.age}
                      onChange={handleAgeChange}
                      placeholder="Age"
                      min={
                        newGuest.type === "infants"
                          ? 0
                          : newGuest.type === "children"
                          ? 2
                          : 18
                      }
                      max={
                        newGuest.type === "infants"
                          ? 2
                          : newGuest.type === "children"
                          ? 11
                          : 120
                      }
                      className={`w-full border rounded-lg px-4 py-3 pl-10 focus:outline-none focus:ring-2 focus:ring-[#3566ab]/20 focus:border-[#3566ab] transition-all shadow-sm hover:border-gray-400 ${
                        ageError ? "border-red-500" : "border-gray-300"
                      }`}
                    />
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <svg
                        className="w-5 h-5 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                  </div>
                  {ageError && (
                    <p className="text-red-500 text-xs mt-1">{ageError}</p>
                  )}
                </div>
              </div>

              <div className="flex gap-3 pt-2">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddForm(false);
                    setNewGuest({ name: "", age: "", type: "adults" });
                    setAgeError("");
                  }}
                  className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all font-medium shadow-sm"
                >
                  <span className="flex items-center justify-center">
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                    Cancel
                  </span>
                </button>
                <button
                  type="button"
                  onClick={handleAddGuest}
                  disabled={
                    !newGuest.name.trim() ||
                    !canAddGuest(newGuest.type) ||
                    ageError !== "" ||
                    ((newGuest.type === "children" || newGuest.type === "infants") && !newGuest.age.trim())
                  }
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-[#3566ab] to-[#2a5490] text-white rounded-lg hover:from-[#2a5490] hover:to-[#1e3f73] disabled:bg-gray-300 disabled:cursor-not-allowed transition-all font-medium shadow-md hover:shadow-lg transform hover:-translate-y-0.5 disabled:transform-none"
                >
                  <span className="flex items-center justify-center">
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    Add Guest
                  </span>
                </button>
              </div>
            </div>
          )}

          {/* Display current travelers */}
          <div className="space-y-4">
            {/* Adults */}
            {travelers.adults.length > 0 && (
              <div>
                <h5 className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                  Adults ({travelers.adults.length})
                </h5>
                <div className="grid gap-2">
                  {travelers.adults.map((adult, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center bg-gradient-to-r from-blue-50 to-blue-100 p-3 rounded-lg border border-blue-200"
                    >
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-xs mr-3">
                          {adult.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <span className="font-medium text-gray-900 text-sm">
                            {adult.name}
                          </span>
                          <p className="text-xs text-gray-500">Adult</p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveGuest("adults", index)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 rounded transition-all"
                        title="Remove guest"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Children */}
            {travelers.children.length > 0 && (
              <div>
                <h5 className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  Children ({travelers.children.length})
                </h5>
                <div className="grid gap-2">
                  {travelers.children.map((child, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center bg-gradient-to-r from-green-50 to-green-100 p-3 rounded-lg border border-green-200"
                    >
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold text-xs mr-3">
                          {child.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <span className="font-medium text-gray-900 text-sm">
                            {child.name}
                          </span>
                          <p className="text-xs text-gray-500">
                            Child {child.age && `(Age: ${child.age})`}
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveGuest("children", index)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 rounded transition-all"
                        title="Remove guest"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Infants */}
            {travelers.infants.length > 0 && (
              <div>
                <h5 className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                  Infants ({travelers.infants.length})
                </h5>
                <div className="grid gap-2">
                  {travelers.infants.map((infant, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center bg-gradient-to-r from-purple-50 to-purple-100 p-3 rounded-lg border border-purple-200"
                    >
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-xs mr-3">
                          {infant.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <span className="font-medium text-gray-900 text-sm">
                            {infant.name}
                          </span>
                          <p className="text-xs text-gray-500">
                            Infant {infant.age && `(Age: ${infant.age})`}
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveGuest("infants", index)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 rounded transition-all"
                        title="Remove guest"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {getTotalGuests() === 0 && (
              <div className="text-center py-6">
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg
                    className="w-6 h-6 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                {hasCapacityForMoreGuests() ? (
                  <>
                    <p className="text-gray-500 text-sm">
                      No additional guests added yet
                    </p>
                    <p className="text-gray-400 text-xs mt-1">
                      Click "Add Guest" to include other travelers
                    </p>
                    <p className="text-gray-400 text-xs mt-1">
                      Current: {getTotalGuestsIncludingLead()}/
                      {maxAdults + maxChildren + maxInfants} guests selected
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-gray-500 text-sm">All guests added</p>
                    <p className="text-gray-400 text-xs mt-1">
                      This booking is for {maxAdults + maxChildren + maxInfants}{" "}
                      guest
                      {maxAdults + maxChildren + maxInfants > 1 ? "s" : ""}{" "}
                      total
                    </p>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Terms and Conditions */}
      <div className="mb-4">
        <div className="flex items-start">
          <input
            type="checkbox"
            id="terms"
            checked={termsAccepted}
            onChange={(e) => {
              setTermsAccepted(e.target.checked);
              // Clear terms error when checkbox is checked
              if (e.target.checked && clearFieldError) {
                clearFieldError("terms");
              }
            }}
            className={`w-4 h-4 text-[#3566ab] rounded focus:ring-[#3566ab] mt-0.5 ${
              formErrors.terms ? "border-red-500" : "border-gray-300"
            }`}
          />
          <label htmlFor="terms" className="ml-2 text-sm text-gray-600">
            By proceeding, I agree to Perfect Piste's{" "}
            <a
              href="/privacy-policy"
              target="_blank"
              className="text-[#3566ab] hover:underline"
            >
              Privacy policy
            </a>{" "}
            and{" "}
            <button onClick={() => setShowPoliciesModal(true)}>
              <a className="text-[#3566ab] hover:underline">
                Cancellation Policies
              </a>
            </button>
            .
          </label>
        </div>
        {formErrors.terms && (
          <p className="text-red-500 text-xs mt-1 ml-6">{formErrors.terms}</p>
        )}
      </div>

      {/* GST Details */}
      {/* <div className="mt-6">
        <label className="flex items-center">
          <input
            type="checkbox"
            className="w-4 h-4 text-[#3566ab] border-gray-300 rounded focus:ring-[#3566ab]"
          />
          <span className="ml-2 text-sm">
            Enter GST Details <span className="text-gray-500">(Optional)</span>
          </span>
        </label>
      </div> */}
    </div>
  );
};

export default GuestDetailsForm;
