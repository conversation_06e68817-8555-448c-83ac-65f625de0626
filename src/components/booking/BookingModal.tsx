import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "../ui/dialog";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { formatDate } from "../../utils/dateUtils";
import StripePaymentForm from "../payment/StripePaymentForm";
import PaymentConfirmation from "../payment/PaymentConfirmation";
import { updateBookingStatus } from "../../utils/payment/stripe";
import StripeProvider from "../payment/StripeProvider";

interface BookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  hotelId: string;
  roomConfigId: string;
  checkInDate: Date | null;
  checkOutDate: Date | null;
  checkInTime: string;
  checkOutTime: string;
  totalAmount: number;
  currencyCode: string;
  guestCount: number;
  infantCount?: number;
  mealPlan?: string;
  mealPlanLabel?: string;
  regionId?: string;
  // Additional room details
  roomName?: string;
  roomImage?: string;
  roomFeatures?: string[];
  hotelName?: string;
  hotelLocation?: string;
  adultCount?: number;
  childrenCount?: number;
  nightCount?: number;
}

const BookingModal: React.FC<BookingModalProps> = ({
  isOpen,
  onClose,
  hotelId,
  roomConfigId,
  checkInDate,
  checkOutDate,
  checkInTime,
  checkOutTime,
  totalAmount,
  currencyCode,
  guestCount,
  infantCount = 0,
  mealPlan = "fb",
  mealPlanLabel = "Full Board",
  regionId = "reg_01",
  // Additional room details with defaults
  roomName = "Superior Room",
  roomImage = "",
  roomFeatures = ["Mountain View", "Free WiFi", "Breakfast Included"],
  hotelName = "",
  hotelLocation = "",
  adultCount = 1,
  childrenCount = 0,
  nightCount = 8,
}) => {
  // Form state
  const [guestName, setGuestName] = useState("");
  const [guestEmail, setGuestEmail] = useState("");
  const [guestPhone, setGuestPhone] = useState("");
  const [specialRequests, setSpecialRequests] = useState("");
  const [notes, setNotes] = useState("");
  // const [promoCode, setPromoCode] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Payment state
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [bookingId, setBookingId] = useState<string>("");

  // Validation
  const [errors, setErrors] = useState<{
    guestName?: string;
    guestEmail?: string;
    guestPhone?: string;
  }>({});

  const validateForm = () => {
    const newErrors: {
      guestName?: string;
      guestEmail?: string;
      guestPhone?: string;
    } = {};

    if (!guestName.trim()) {
      newErrors.guestName = "Name is required";
    }

    if (!guestEmail.trim()) {
      newErrors.guestEmail = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(guestEmail)) {
      newErrors.guestEmail = "Please enter a valid email address";
    }

    if (!guestPhone.trim()) {
      newErrors.guestPhone = "Phone number is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch(
        `${
          import.meta.env.PUBLIC_BACKEND_URL
        }/store/hotel-management/bookings/create`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-publishable-api-key":
              import.meta.env.PUBLIC_BACKEND_API_KEY || "",
          },
          body: JSON.stringify({
            hotel_id: "01JQV5KJZYJBK75VVZYYJDAVFW",
            room_config_id: "prod_01JS8ACDRTNCCZ7R0F86RX1VPR",
            check_in_date: "2023-12-11",
            check_out_date: "2023-12-11",
            check_in_time: "14:00",
            check_out_time: "12:00",
            guest_name: "John Doe",
            guest_email: "<EMAIL>",
            guest_phone: "+1234567890",
            number_of_guests: 2,
            total_amount: 1000,
            currency_code: "USD",
            region_id: "reg_01JP9R0NP6B5DXGDYHFSSW0FK1",
            special_requests: "specialRequests",
            notes: "notes",
            metadata: {
              source: "website",
              // promo_code: promoCode || undefined,
            },
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create booking");
      }

      const data = await response.json();
      setSuccess(true);

      // Store the booking ID for payment processing
      // Extract booking ID and client secret from the response
      let extractedBookingId = "";
      let clientSecret = "";
      let paymentSessionId = "";

      if (data.booking && data.booking.id) {
        extractedBookingId = data.booking.id;

        // Try to extract client secret and payment session ID
        if (data.payment_session && data.payment_session.client_secret) {
          clientSecret = data.payment_session.client_secret;
          paymentSessionId = data.payment_session.id;
        } else if (
          data.booking.metadata &&
          data.booking.metadata.client_secret
        ) {
          clientSecret = data.booking.metadata.client_secret;
          paymentSessionId = data.booking.metadata.payment_session_id || "";
        }
      } else if (data.id) {
        extractedBookingId = data.id;
      } else if (typeof data === "string") {
        // Some APIs might just return the ID as a string
        extractedBookingId = data;
      }

      // Check if we have a client secret in the response directly
      if (!clientSecret && data.client_secret) {
        clientSecret = data.client_secret;
      }

      // Check if we have a client secret in the payment_session directly
      if (
        !clientSecret &&
        data.payment_session &&
        data.payment_session.client_secret
      ) {
        clientSecret = data.payment_session.client_secret;
        paymentSessionId = data.payment_session.id || "";
      }

      if (extractedBookingId) {
        setBookingId(extractedBookingId);

        // Store client secret in session storage for payment processing
        if (clientSecret) {
          // Log the client secret before storing it

          // Check if the client secret is in the expected format (pi_XXX_secret_YYY)
          if (!clientSecret.includes("_secret_")) {
            console.warn(
              `[BOOKING] Client secret format is unexpected: ${clientSecret.substring(
                0,
                20
              )}...`
            );
          }

          sessionStorage.setItem("stripe_client_secret", clientSecret);
          sessionStorage.setItem("stripe_payment_session_id", paymentSessionId);

          // Session storage items have been set
        } else {
          // No client secret found to store in session storage
        }

        // Show payment form after booking is created
        setShowPaymentForm(true);
      } else {
        // Booking ID not found in response, use a fake one for demo
        const fakeBookingId = `booking_${Date.now()}`;
        setBookingId(fakeBookingId);
        setShowPaymentForm(true);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-2xl font-baskervville">
            Complete Your Booking
          </DialogTitle>
          <DialogDescription>
            Please provide your details to confirm your reservation.
          </DialogDescription>
        </DialogHeader>

        {paymentSuccess ? (
          <PaymentConfirmation
            bookingId={bookingId}
            hotelName={hotelName}
            hotelLocation={hotelLocation}
            checkInDate={formatDate(checkInDate)}
            checkOutDate={formatDate(checkOutDate)}
            amount={totalAmount}
            currency={currencyCode}
            onClose={onClose}
            roomName={roomName}
            roomImage={roomImage}
            roomFeatures={roomFeatures}
            adultCount={adultCount}
            childrenCount={childrenCount}
            infantCount={infantCount}
            nightCount={nightCount}
            guestCount={guestCount}
            mealPlan={mealPlan}
            mealPlanLabel={mealPlanLabel}
          />
        ) : showPaymentForm ? (
          <div className="py-4">
            <h3 className="text-xl font-medium mb-4">Payment Details</h3>
            <p className="text-muted-foreground mb-6">
              Your booking has been created. Please complete the payment to
              confirm your reservation.
            </p>

            <StripeProvider>
              <StripePaymentForm
                bookingId={bookingId}
                amount={totalAmount}
                currency={currencyCode}
                customerEmail={guestEmail}
                onPaymentSuccess={() => {
                  setPaymentSuccess(true);
                  // Update booking status to paid
                  updateBookingStatus(bookingId, "paid").catch((_: Error) => {
                    setError(
                      "Error updating booking status. Please contact support."
                    );
                  });
                }}
                onPaymentError={(errorMsg) => setError(errorMsg)}
              />
            </StripeProvider>
          </div>
        ) : success ? (
          <div className="py-6 text-center">
            <div className="mb-4 mx-auto w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </div>
            <h3 className="text-xl font-medium mb-2">Booking Created!</h3>
            <p className="text-muted-foreground">
              Please proceed to payment to confirm your reservation.
            </p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Order Summary - Moved to the top */}
            <div className="mb-6">
              <h4 className="font-baskervville text-lg mb-4">Order Summary</h4>

              {/* Selected Room Section */}
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <h5 className="text-[#285DA6] font-baskervville mb-2">
                  Selected Room
                </h5>
                <div className="flex items-start gap-3">
                  {roomImage ? (
                    <img
                      src={roomImage}
                      alt={roomName}
                      className="w-20 h-20 object-cover rounded-md"
                    />
                  ) : (
                    <div className="w-20 h-20 bg-gray-200 rounded-md flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-gray-400"
                      >
                        <rect
                          x="3"
                          y="3"
                          width="18"
                          height="18"
                          rx="2"
                          ry="2"
                        ></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21 15 16 10 5 21"></polyline>
                      </svg>
                    </div>
                  )}
                  <div className="flex-1">
                    <h6 className="font-medium">{roomName}</h6>
                    <p className="text-sm text-gray-600">
                      Up to {guestCount} guests
                    </p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {roomFeatures.map((feature, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-karla bg-gray-100 text-gray-800"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Trip Details */}
              <h5 className="text-[#285DA6] font-baskervville mb-2">
                Your Trip Dates
              </h5>
              <div className="mb-4">
                <div className="flex justify-between mb-2 text-sm">
                  <span>Check-in:</span>
                  <span>
                    {formatDate(checkInDate)} at {checkInTime}
                  </span>
                </div>
                <div className="flex justify-between mb-2 text-sm">
                  <span>Check-out:</span>
                  <span>
                    {formatDate(checkOutDate)} at {checkOutTime}
                  </span>
                </div>
                <div className="flex justify-between mb-2 text-sm">
                  <span>Duration:</span>
                  <span>{nightCount} nights</span>
                </div>
              </div>

              {/* Guest Details */}
              <h5 className="text-[#285DA6] font-baskervville mb-2">Guests</h5>
              <div className="mb-4">
                <div className="flex justify-between mb-2 text-sm">
                  <span>Adults:</span>
                  <span>{adultCount}</span>
                </div>
                {childrenCount > 0 && (
                  <div className="flex justify-between mb-2 text-sm">
                    <span>Children:</span>
                    <span>{childrenCount}</span>
                  </div>
                )}
                {infantCount > 0 && (
                  <div className="flex justify-between mb-2 text-sm">
                    <span>Infants:</span>
                    <span>{infantCount}</span>
                  </div>
                )}
                <div className="flex justify-between mb-2 text-sm">
                  <span>Total Guests:</span>
                  <span>{guestCount}</span>
                </div>
              </div>

              {/* Meal Plan */}
              <h5 className="text-[#285DA6] font-baskervville mb-2">
                Room Board
              </h5>
              <div className="mb-4">
                <div className="flex justify-between mb-2 text-sm">
                  <span>Meal Plan:</span>
                  <span>{mealPlanLabel}</span>
                </div>
              </div>

              {/* Price Breakdown */}
              <h5 className="text-[#285DA6] font-baskervville mb-2">
                Price Details
              </h5>
              <div className="border-t border-gray-200 pt-2">
                <div className="flex justify-between mb-2 text-sm">
                  <span>Room Rate (per night)</span>
                  <span>
                    {currencyCode}{" "}
                    {new Intl.NumberFormat("en-US", {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    }).format(totalAmount / nightCount)}
                  </span>
                </div>
                <div className="flex justify-between mb-2 text-sm">
                  <span>Nights</span>
                  <span>{nightCount}</span>
                </div>
                <div className="flex justify-between mb-2 text-sm">
                  <span>Subtotal</span>
                  <span>
                    {currencyCode}{" "}
                    {new Intl.NumberFormat("en-US", {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    }).format(totalAmount * 0.9)}
                  </span>
                </div>
                {/* Only show taxes if they exist */}
                {totalAmount * 0.1 > 0 && (
                  <div className="flex justify-between mb-2 text-sm">
                    <span>Taxes & Fees</span>
                    <span>
                      {currencyCode}{" "}
                      {new Intl.NumberFormat("en-US", {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0,
                      }).format(totalAmount * 0.1)}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex justify-between font-bold mt-2 pt-2 border-t border-gray-200">
                <div>
                  <span>Total:</span>
                  <p className="text-xs font-normal text-gray-500 mt-1">Inclusive of tax</p>
                </div>
                <span>
                  {currencyCode}{" "}
                  {new Intl.NumberFormat("en-US", {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(totalAmount)}
                </span>
              </div>
              <div className="text-xs text-right mt-1 text-muted-foreground">
                All prices shown in {currencyCode}
              </div>
            </div>

            {/* Personal Details Section - Moved below Order Summary */}
            <div className="pt-4 border-t border-border">
              <h4 className="font-baskervville text-lg mb-4">Your Details</h4>
              <div className="space-y-3">
                <div>
                  <Label htmlFor="guestName">Full Name</Label>
                  <Input
                    id="guestName"
                    value={guestName}
                    onChange={(e) => setGuestName(e.target.value)}
                    placeholder="John Doe"
                    className={errors.guestName ? "border-red-500" : ""}
                  />
                  {errors.guestName && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.guestName}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="guestEmail">Email Address</Label>
                  <Input
                    id="guestEmail"
                    type="email"
                    value={guestEmail}
                    onChange={(e) => setGuestEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className={errors.guestEmail ? "border-red-500" : ""}
                  />
                  {errors.guestEmail && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.guestEmail}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="guestPhone">Phone Number</Label>
                  <Input
                    id="guestPhone"
                    type="tel"
                    value={guestPhone}
                    onChange={(e) => setGuestPhone(e.target.value)}
                    placeholder="1234567890"
                    className={errors.guestPhone ? "border-red-500" : ""}
                  />
                  {errors.guestPhone && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.guestPhone}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="specialRequests">Special Requests</Label>
                  <Textarea
                    id="specialRequests"
                    value={specialRequests}
                    onChange={(e) => setSpecialRequests(e.target.value)}
                    placeholder="Late check-in, dietary requirements, etc."
                  />
                </div>

                <div>
                  <Label htmlFor="notes">Additional Notes</Label>
                  <Textarea
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Any other information you'd like us to know"
                  />
                </div>

                {/* <div>
                  <Label htmlFor="promoCode">Promo Code (Optional)</Label>
                  <Input
                    id="promoCode"
                    value={promoCode}
                    onChange={(e) => setPromoCode(e.target.value)}
                    placeholder="Enter promo code if you have one"
                  />
                </div> */}
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            <DialogFooter className="mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Processing...
                  </span>
                ) : (
                  "Confirm Booking"
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default BookingModal;
