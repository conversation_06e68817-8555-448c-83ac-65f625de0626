import React, { useState } from "react";
import { formatDate } from "../../utils/dateUtils";

interface BookingFormProps {
  hotelId: string;
  roomConfigId: string;
  checkInDate: string;
  checkOutDate: string;
  checkInTime: string;
  checkOutTime: string;
  totalAmount: number;
  currencyCode: string;
  guestCount: number;
  hotelName: string;
  hotelLocation: string;
  roomName: string;
  roomImage: string;
  mealPlan?: string;
}

const BookingForm: React.FC<BookingFormProps> = ({
  hotelId,
  roomConfigId,
  checkInDate,
  checkOutDate,
  checkInTime,
  checkOutTime,
  totalAmount,
  currencyCode,
  guestCount,
  hotelName,
  hotelLocation,
  roomName,
  roomImage,
  mealPlan = "none",
}) => {
  // Form state
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    specialRequests: "",
    paymentMethod: "credit_card",
    cardNumber: "",
    cardExpiry: "",
    cardCVC: "",
    cardName: "",
    agreeToTerms: false,
  });

  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState("");

  // Calculate nights - handle empty or invalid dates
  const isValidDate = (dateStr: string) => {
    if (!dateStr) return false;
    const date = new Date(dateStr);
    return !isNaN(date.getTime());
  };

  // Only create Date objects if the strings are valid
  const startDate = isValidDate(checkInDate) ? new Date(checkInDate) : null;
  const endDate = isValidDate(checkOutDate) ? new Date(checkOutDate) : null;

  // Calculate nights only if both dates are valid
  let nights = 0;
  if (startDate && endDate) {
    const diffTime = endDate.getTime() - startDate.getTime();
    nights = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    // Ensure nights is at least 1 for valid date ranges
    nights = Math.max(1, nights);
  }

  // Format currency
  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;
    const checked =
      type === "checkbox" ? (e.target as HTMLInputElement).checked : undefined;

    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.agreeToTerms) {
      setSubmitError("You must agree to the terms and conditions to proceed.");
      return;
    }

    setIsSubmitting(true);
    setSubmitError("");

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Success
      setSubmitSuccess(true);
      window.scrollTo(0, 0);
    } catch (error) {
      setSubmitError(
        "There was an error processing your booking. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitSuccess) {
    return (
      <div className="bg-white border border-[#285DA6]/10 rounded-lg shadow-md p-8 text-center">
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 bg-[#285DA6]/10 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#285DA6]"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
        </div>
        <h2 className="text-xl font-karla uppercase tracking-wider mb-4 text-[#285DA6]">
          Booking Confirmed!
        </h2>
        <p className="mb-6">
          Thank you for your booking. We've sent a confirmation email to{" "}
          {formData.email}.
        </p>
        <div className="bg-[#285DA6]/5 p-4 rounded-lg mb-6">
          <div className="font-medium mb-2">
            Booking Reference:{" "}
            <span className="text-[#285DA6]">
              PP-
              {Math.floor(Math.random() * 10000)
                .toString()
                .padStart(4, "0")}
            </span>
          </div>
          <div className="text-sm mb-1">
            {hotelName}, {hotelLocation}
          </div>
          <div className="text-sm mb-1">
            {formatDate(new Date(checkInDate))} -{" "}
            {formatDate(new Date(checkOutDate))}
          </div>
          <div className="text-sm mb-1">
            {guestCount} {guestCount === 1 ? "guest" : "guests"}, {roomName}
          </div>
          <div className="text-sm">
            Meal Plan: {mealPlan === "none" && "Room Only"}
            {mealPlan === "bb" && "Bed & Breakfast"}
            {mealPlan === "hb" && "Half Board"}
            {mealPlan === "fb" && "Full Board"}
          </div>
        </div>
        <a
          href={`/stays/${hotelId}`}
          className="inline-block px-6 py-3 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 transition-colors font-karla uppercase tracking-wider"
        >
          Return to Hotel
        </a>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Order Summary - Displayed at the top as per user preference */}
      <div className="lg:col-span-3 bg-white border border-[#285DA6]/10 rounded-lg shadow-md p-6 mb-4">
        <h2 className="text-base font-karla uppercase tracking-wider mb-4 text-[#285DA6]">
          Order Summary
        </h2>
        <div className="flex flex-col md:flex-row">
          <div className="w-full md:w-1/4 mb-4 md:mb-0">
            <div className="aspect-video rounded-lg overflow-hidden">
              <img
                src={roomImage || "/images/room-placeholder.jpg"}
                alt={roomName}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
          <div className="md:pl-6 flex-1">
            <h3 className="font-baskervville text-lg mb-1">{hotelName}</h3>
            <p className="text-sm text-foreground/70 mb-3">{hotelLocation}</p>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <div className="text-xs font-karla uppercase tracking-wider text-foreground/60 mb-1">
                  Check In
                </div>
                <div className="text-sm font-medium">
                  {startDate
                    ? `${formatDate(startDate)} (${checkInTime})`
                    : "Select date"}
                </div>
              </div>
              <div>
                <div className="text-xs font-karla uppercase tracking-wider text-foreground/60 mb-1">
                  Check Out
                </div>
                <div className="text-sm font-medium">
                  {endDate
                    ? `${formatDate(endDate)} (${checkOutTime})`
                    : "Select date"}
                </div>
              </div>
            </div>

            <div className="mb-4">
              <div className="text-xs font-karla uppercase tracking-wider text-foreground/60 mb-1">
                Room Type
              </div>
              <div className="text-sm font-medium">
                {roomName} ({guestCount} {guestCount === 1 ? "guest" : "guests"}
                )
              </div>
            </div>

            <div className="mb-4">
              <div className="text-xs font-karla uppercase tracking-wider text-foreground/60 mb-1">
                Meal Plan
              </div>
              <div className="text-sm font-medium">
                {mealPlan === "none" && "Room Only"}
                {mealPlan === "bb" && "Bed & Breakfast"}
                {mealPlan === "hb" && "Half Board"}
                {mealPlan === "fb" && "Full Board"}
              </div>
            </div>

            <div className="bg-[#285DA6]/5 p-3 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="font-medium">Total</span>
                <span className="text-lg font-baskervville text-[#285DA6]">
                  {formatCurrency(totalAmount, currencyCode)}
                </span>
              </div>
              <div className="text-xs text-right text-foreground/60">
                {startDate && endDate
                  ? `${nights} ${
                      nights === 1 ? "night" : "nights"
                    }, includes taxes and fees`
                  : "Select dates to see total"}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Guest Information Form */}
      <div className="lg:col-span-2">
        <div className="bg-white border border-[#285DA6]/10 rounded-lg shadow-md p-6">
          <h2 className="text-base font-karla uppercase tracking-wider mb-4 text-[#285DA6]">
            Guest Information
          </h2>

          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label
                  htmlFor="firstName"
                  className="block text-sm font-medium mb-1"
                >
                  First Name*
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                />
              </div>
              <div>
                <label
                  htmlFor="lastName"
                  className="block text-sm font-medium mb-1"
                >
                  Last Name*
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium mb-1"
                >
                  Email Address*
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                />
              </div>
              <div>
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium mb-1"
                >
                  Phone Number*
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                />
              </div>
            </div>

            <div className="mb-6">
              <label
                htmlFor="specialRequests"
                className="block text-sm font-medium mb-1"
              >
                Special Requests
              </label>
              <textarea
                id="specialRequests"
                name="specialRequests"
                value={formData.specialRequests}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
              ></textarea>
            </div>

            <h2 className="text-base font-karla uppercase tracking-wider mb-4 text-[#285DA6]">
              Payment Details
            </h2>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                Payment Method
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="credit_card"
                    checked={formData.paymentMethod === "credit_card"}
                    onChange={handleInputChange}
                    className="mr-2 text-[#285DA6]"
                  />
                  Credit Card
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="paypal"
                    checked={formData.paymentMethod === "paypal"}
                    onChange={handleInputChange}
                    className="mr-2 text-[#285DA6]"
                  />
                  PayPal
                </label>
              </div>
            </div>

            {formData.paymentMethod === "credit_card" && (
              <div className="space-y-4 mb-6">
                <div>
                  <label
                    htmlFor="cardName"
                    className="block text-sm font-medium mb-1"
                  >
                    Name on Card*
                  </label>
                  <input
                    type="text"
                    id="cardName"
                    name="cardName"
                    value={formData.cardName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                  />
                </div>
                <div>
                  <label
                    htmlFor="cardNumber"
                    className="block text-sm font-medium mb-1"
                  >
                    Card Number*
                  </label>
                  <input
                    type="text"
                    id="cardNumber"
                    name="cardNumber"
                    value={formData.cardNumber}
                    onChange={handleInputChange}
                    required
                    placeholder="XXXX XXXX XXXX XXXX"
                    className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="cardExpiry"
                      className="block text-sm font-medium mb-1"
                    >
                      Expiry Date*
                    </label>
                    <input
                      type="text"
                      id="cardExpiry"
                      name="cardExpiry"
                      value={formData.cardExpiry}
                      onChange={handleInputChange}
                      required
                      placeholder="MM/YY"
                      className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="cardCVC"
                      className="block text-sm font-medium mb-1"
                    >
                      CVC*
                    </label>
                    <input
                      type="text"
                      id="cardCVC"
                      name="cardCVC"
                      value={formData.cardCVC}
                      onChange={handleInputChange}
                      required
                      placeholder="123"
                      className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                    />
                  </div>
                </div>
              </div>
            )}

            <div className="mb-6">
              <label className="flex items-start">
                <input
                  type="checkbox"
                  name="agreeToTerms"
                  checked={formData.agreeToTerms}
                  onChange={handleInputChange}
                  className="mt-1 mr-2 text-[#285DA6]"
                  required
                />
                <span className="text-sm">
                  I agree to the{" "}
                  <a href="/terms" className="text-[#285DA6] hover:underline">
                    Terms and Conditions
                  </a>{" "}
                  and{" "}
                  <a href="/privacy" className="text-[#285DA6] hover:underline">
                    Privacy Policy
                  </a>
                </span>
              </label>
            </div>

            {submitError && (
              <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
                {submitError}
              </div>
            )}

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full py-3 px-4 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 transition-colors font-karla uppercase tracking-wider disabled:opacity-70"
            >
              {isSubmitting ? "Processing..." : "Complete Booking"}
            </button>
          </form>
        </div>
      </div>

      {/* Booking Policy */}
      <div className="lg:col-span-1">
        <div className="bg-white border border-[#285DA6]/10 rounded-lg shadow-md p-6">
          <h2 className="text-base font-karla uppercase tracking-wider mb-4 text-[#285DA6]">
            Booking Policy
          </h2>

          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-karla uppercase tracking-wider mb-2 text-[#285DA6]">
                Free Cancellation
              </h3>
              <p className="text-sm text-foreground/70">
                Cancel up to 48 hours before check-in for a full refund.
              </p>
            </div>

            <div>
              <h3 className="text-sm font-karla uppercase tracking-wider mb-2 text-[#285DA6]">
                Payment
              </h3>
              <p className="text-sm text-foreground/70">
                Your card will not be charged until 48 hours before check-in.
              </p>
            </div>

            <div>
              <h3 className="text-sm font-karla uppercase tracking-wider mb-2 text-[#285DA6]">
                Check-in/Check-out
              </h3>
              <p className="text-sm text-foreground/70">
                Check-in: {checkInTime}
                <br />
                Check-out: {checkOutTime}
              </p>
            </div>

            <div className="pt-4 border-t border-[#285DA6]/10">
              <div className="flex items-center mb-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#285DA6] mr-2"
                >
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                <span className="text-sm font-medium">
                  24/7 Customer Support
                </span>
              </div>
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#285DA6] mr-2"
                >
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                <span className="text-sm font-medium">Secure Payment</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingForm;
