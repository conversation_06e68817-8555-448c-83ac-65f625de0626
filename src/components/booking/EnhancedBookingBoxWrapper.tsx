import React from "react";
import { CartProvider } from "../cart/CartContext";
import EnhancedBookingBox from "./EnhancedBookingBox";
import { type EnhancedBookingBoxProps } from "../../utils/types";

// Use the same props as EnhancedBookingBox
type EnhancedBookingBoxWrapperProps = EnhancedBookingBoxProps;

const EnhancedBookingBoxWrapper: React.FC<EnhancedBookingBoxWrapperProps> = (
  props
) => {
  return (
    <CartProvider>
      <EnhancedBookingBox {...props} />
    </CartProvider>
  );
};

export default EnhancedBookingBoxWrapper;
