import React from "react";

interface PriceBreakdownProps {
  basePrice: number;
  discount: number;
  taxesAndFees: number;
  totalPayable: number;
  currencyCode: string;
  nights: number;
}

const PriceBreakdown: React.FC<PriceBreakdownProps> = ({
  basePrice,
  discount,
  taxesAndFees,
  totalPayable,
  currencyCode,
  nights,
}) => {
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Calculate per night rate from total base price
  const perNightRate = basePrice / nights;

  return (
    <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-bold mb-4">Price Breakup</h3>

      {/* Room Rate Breakdown */}
      <div className="mb-4 pb-4 border-b border-gray-200 space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm">Room price</span>
          <span className="font-medium">
            {currencyCode} {formatCurrency(perNightRate)}
          </span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm">Nights</span>
          <span className="font-medium">x {nights}</span>
        </div>
      </div>

      {/* Discount (if applicable) */}
      {discount > 0 && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-1 text-green-600"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 8v4"></path>
                <path d="M12 16h.01"></path>
              </svg>
              Total Discount
            </span>
            <span className="font-medium text-green-600">
              {currencyCode} {formatCurrency(discount)}
            </span>
          </div>
        </div>
      )}

      {/* Price after Discount */}
      {discount > 0 && (
        <div className="mb-4 pb-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <span className="text-sm">Price after Discount</span>
            <span className="font-medium">
              {currencyCode} {formatCurrency(basePrice - discount)}
            </span>
          </div>
        </div>
      )}

      {/* Taxes & Fees - Only show if taxes exist */}
      {taxesAndFees > 0 && (
        <div className="mb-4 pb-4 border-b border-gray-200">
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-1 text-gray-400"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 8v4"></path>
                <path d="M12 16h.01"></path>
              </svg>
              Taxes & Service Fees
            </span>
            <span className="font-medium">
              {currencyCode} {formatCurrency(taxesAndFees)}
            </span>
          </div>
          <p className="text-xs text-gray-500 mt-1">Inclusive of taxes</p>
        </div>
      )}

      {/* Total Amount */}
      <div>
        <div className="flex justify-between items-center">
          <div>
            <span className="font-bold">Total Amount to be paid</span>
            <p className="text-xs font-normal text-gray-500 mt-1">Inclusive of tax</p>
          </div>
          <span className="font-bold text-lg">
            {currencyCode} {formatCurrency(totalPayable)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default PriceBreakdown;
