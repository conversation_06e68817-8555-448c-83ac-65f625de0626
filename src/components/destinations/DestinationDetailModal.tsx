import React, { useEffect, useRef, useState } from "react";
import DestinationHero from "./DestinationHero";
import DestinationStickyHeader from "./DestinationStickyHeader";
import DestinationMainContent from "./DestinationMainContent";
import ReactPhotoModal from "../photos/ReactPhotoModal";

interface DestinationDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  destinationDetails: any;
  loading: boolean;
}

const DestinationDetailModal: React.FC<DestinationDetailModalProps> = ({
  isOpen,
  onClose,
  destinationDetails,
  loading,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [isClosing, setIsClosing] = useState(false);

  // Handle smooth closing animation
  const handleClose = () => {
    setIsClosing(true);
    // Wait for animation to complete before actually closing
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 300); // Match this with the CSS animation duration
  };

  // <PERSON>le click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        handleClose();
      }
    };

    // Handle escape key to close
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscKey);
      document.body.style.overflow = "hidden"; // Prevent scrolling of background

      // Hide the header when modal is open - use a more direct approach
      const headerElements = document.querySelectorAll(".header-wrapper");
      headerElements.forEach((header) => {
        (header as HTMLElement).style.display = "none";
      });
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscKey);
      document.body.style.overflow = ""; // Restore scrolling

      // Show the header when modal is closed
      const headerElements = document.querySelectorAll(".header-wrapper");
      headerElements.forEach((header) => {
        (header as HTMLElement).style.display = "";
      });
    };
  }, [isOpen, onClose]);

  // Direct scroll function that can be called from anywhere
  const scrollToSection = (sectionId: string) => {
    if (!modalRef.current) return;

    // Remove the # if it exists
    const id = sectionId.startsWith("#") ? sectionId.substring(1) : sectionId;

    // Find the target element by ID
    const targetElement = document.getElementById(id);
    if (!targetElement) {
      console.error(`Section with id ${id} not found`);
      return;
    }

    // Calculate offset based on target
    let offset = 80; // Default offset

    // Special handling for different sections
    if (id === "activities") {
      offset = 100;
    } else if (id === "experience") {
      offset = 70;
    }

    // Get the position of the element relative to the modal
    const modalRect = modalRef.current.getBoundingClientRect();
    const elementRect = targetElement.getBoundingClientRect();
    const relativeTop =
      elementRect.top - modalRect.top + modalRef.current.scrollTop;

    // Scroll the modal content
    modalRef.current.scrollTo({
      top: relativeTop - offset,
      behavior: "smooth",
    });
  };

  // Handle anchor links within the modal
  useEffect(() => {
    if (!isOpen || !modalRef.current) return;

    const handleAnchorClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (
        target.tagName === "A" &&
        target.getAttribute("href")?.startsWith("#")
      ) {
        e.preventDefault();
        const targetId = target.getAttribute("href");
        if (!targetId) return;

        // Use the direct scroll function
        scrollToSection(targetId);
      }
    };

    // Add event listeners to all anchor links in the modal
    const anchorLinks = modalRef.current.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach((link) => {
      link.addEventListener("click", handleAnchorClick as EventListener);
    });

    return () => {
      if (modalRef.current) {
        const anchorLinks = modalRef.current.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach((link) => {
          link.removeEventListener("click", handleAnchorClick as EventListener);
        });
      }
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="destination-detail-modal-overlay font-baskervville">
      <div
        className={`destination-detail-modal-container px-8 py-4 rounded-r-lg ${
          isClosing ? "closing" : ""
        }`}
      >
        <button
          className="modal-close-button"
          onClick={handleClose}
          aria-label="Close modal"
        >
          ×
        </button>

        <button
          className="mobile-close-button"
          onClick={handleClose}
          aria-label="Close modal"
        >
          ×
        </button>

        <div className="destination-detail-modal-content" ref={modalRef}>
          {loading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#285DA6]"></div>
            </div>
          ) : destinationDetails ? (
            <>
              {/* Sticky Header */}
              <DestinationStickyHeader
                name={destinationDetails.name}
                location={destinationDetails.location || ""}
                country={destinationDetails.country || ""}
                onSectionClick={scrollToSection}
              />

              {/* Hero Section with Gallery */}
              <DestinationHero
                name={destinationDetails.name}
                location={destinationDetails.location || ""}
                country={destinationDetails.country || ""}
                mainImage={
                  destinationDetails.images && destinationDetails.images.length > 0
                    ? destinationDetails.images[0].url
                    : destinationDetails.imageUrl
                }
                images={
                  destinationDetails.images && destinationDetails.images.length > 0
                    ? destinationDetails.images.map((img: any) => img.url)
                    : [destinationDetails.imageUrl]
                }
              />

              {/* Main Content */}
              <DestinationMainContent
                destinationData={destinationDetails}
              />

              {/* Photo Modal */}
              <ReactPhotoModal
                images={
                  destinationDetails.images && destinationDetails.images.length > 0
                    ? destinationDetails.images.map((img: any) => img.url)
                    : [destinationDetails.imageUrl]
                }
                hotelName={destinationDetails.name}
              />
            </>
          ) : (
            <div className="flex justify-center items-center h-full">
              <p>Failed to load destination details. Please try again.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DestinationDetailModal;
