---
interface Props {
  destinationName: string;
  description?: string;
  ctaText1?: string;
  ctaLink1?: string;
  ctaText2?: string;
  ctaLink2?: string;
  backgroundImage?: string;
}

const {
  destinationName,
  description = "Let our team of experts create a bespoke travel experience tailored to your desires.",
  ctaText1 = "BROWSE HOTELS",
  ctaLink1 = "/stays",
  ctaText2 = "CONTACT CONCIERGE",
  ctaLink2 = "/contact",
  backgroundImage = "https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2576&q=80",
} = Astro.props;
---

<div class="container-custom pt-4 pb-12 sm:pt-6 sm:pb-16">
  <section class="relative h-[50vh] sm:h-[60vh] overflow-hidden rounded-md">
    <img
      src={backgroundImage}
      alt="Snow covered mountain range during daytime"
      class="w-full h-full object-cover"
    />
    <div class="absolute inset-0 bg-black/40 flex items-center">
      <div class="container-custom">
        <div class="max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-3xl">
          <h2
            class="text-2xl sm:text-3xl md:text-4xl font-baskervville mb-2 sm:mb-4 text-white"
          >
            Ready to Experience {destinationName}?
          </h2>
          <p class="text-base sm:text-lg md:text-xl text-white/90 mb-4 sm:mb-6">
            {description}
          </p>
          <div class="mt-4 sm:mt-6 md:mt-8 flex flex-wrap gap-6">
            <a
              href={ctaLink1}
              class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
            >
              {ctaText1}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-2"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            </a>
            <a
              href={ctaLink2}
              class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
            >
              {ctaText2}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-2"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
