---
const planningSteps = [
  {
    number: "01",
    title: "Consultation",
    description: "Our luxury travel experts will understand your preferences, desires, and expectations to create your perfect mountain escape."
  },
  {
    number: "02",
    title: "Customization",
    description: "We craft a bespoke itinerary tailored to your specific needs, including accommodations, activities, and exclusive experiences."
  },
  {
    number: "03",
    title: "Confirmation",
    description: "Once you approve your personalized itinerary, we handle all bookings and arrangements with our premium partners."
  },
  {
    number: "04",
    title: "Concierge Support",
    description: "From the moment you book until after your return, our dedicated concierge team is available 24/7 to assist with any request."
  }
];
---

<section class="py-16 bg-primary/5">
  <div class="container-custom">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div>
        <p class="section-micro-headline">Effortless Luxury</p>
        <h2 class="section-title mb-6">Your Journey Begins Here</h2>
        <p class="text-foreground/80 mb-8">
          At Perfect Piste, we believe that planning your luxury mountain getaway should be as enjoyable as the journey itself. Our expert travel consultants handle every detail, ensuring a seamless experience from initial consultation to your return home.
        </p>
        
        <div class="space-y-8">
          {planningSteps.map(step => (
            <div class="flex">
              <div class="mr-6">
                <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary font-baskervville text-xl">
                  {step.number}
                </div>
              </div>
              <div>
                <h3 class="text-lg font-baskervville mb-2">{step.title}</h3>
                <p class="text-foreground/70 text-sm">{step.description}</p>
              </div>
            </div>
          ))}
        </div>
        
        <div class="mt-10">
          <a href="/contact" class="btn-primary">Start Planning Your Journey</a>
        </div>
      </div>
      
      <div class="relative">
        <div class="aspect-[4/5] rounded-lg overflow-hidden shadow-glow">
          <img 
            src="https://images.unsplash.com/photo-1527631746610-bca00a040d60?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
            alt="Luxury travel planning" 
            class="w-full h-full object-cover"
          />
        </div>
        <div class="absolute -bottom-6 -left-6 w-64 bg-background rounded-lg p-4 shadow-glow border border-border/30">
          <div class="flex items-center mb-3">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="20" 
              height="20" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              stroke-width="2" 
              stroke-linecap="round" 
              stroke-linejoin="round"
              class="text-primary mr-2"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <span class="text-sm font-medium">Personalized Service</span>
          </div>
          <p class="text-xs text-foreground/70">
            "Our travel consultant created the perfect itinerary for our family. Every detail was thoughtfully arranged."
          </p>
          <div class="mt-3 text-xs text-foreground/60">
            — James & Sarah Thompson
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
