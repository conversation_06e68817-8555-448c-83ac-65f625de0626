---
const seasons = [
  {
    name: "Winter",
    title: "Winter Wonderlands",
    description:
      "Experience the magic of pristine snow-covered slopes and cozy alpine villages.",
    image:
      "https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    months: "December - March",
  },
  {
    name: "Spring",
    title: "Spring Awakening",
    description:
      "Enjoy longer days, milder temperatures, and the beautiful contrast of snow and blooming alpine flowers.",
    image:
      "https://images.unsplash.com/photo-1486870591958-9b9d0d1dda99?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    months: "April - May",
  },
  {
    name: "Summer",
    title: "Alpine Summers",
    description:
      "Discover breathtaking hiking trails, crystal-clear lakes, and vibrant mountain festivals.",
    image:
      "https://images.unsplash.com/photo-1464822759023-fed622ff2c3b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    months: "June - August",
  },
  {
    name: "Autumn",
    title: "Golden Autumn",
    description:
      "Witness the spectacular transformation of alpine landscapes with vibrant fall colors.",
    image:
      "https://images.unsplash.com/photo-1477322524744-0eece9e79640?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    months: "September - November",
  },
];
---

<section class="py-16 bg-background">
  <div class="container-custom">
    <div class="text-center mb-12">
      <p class="section-micro-headline">Year-Round Luxury</p>
      <h2 class="section-title">Seasonal Highlights</h2>
      <p class="section-subtitle max-w-3xl mx-auto">
        Each season offers unique experiences in our carefully selected
        destinations. Discover the perfect time for your luxury mountain escape.
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {
        seasons.map((season) => (
          <div class="group relative overflow-hidden rounded-lg shadow-sm border border-border/30">
            <div class="aspect-[3/4] overflow-hidden">
              <img
                src={season.image}
                alt={season.title}
                class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105 rounded-lg"
              />
            </div>
            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent flex flex-col justify-end p-6 text-white">
              <div class="bg-primary/80 text-white text-xs uppercase tracking-wider py-1 px-3 rounded-full inline-block mb-2 backdrop-blur-sm">
                {season.months}
              </div>
              <h3 class="text-xl font-baskervville mb-2">{season.title}</h3>
              <p class="text-white/80 text-sm mb-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {season.description}
              </p>
              <a
                href={`/destinations?season=${season.name}`}
                class="inline-block text-sm uppercase tracking-wider font-karla border-b border-white/50 pb-1 hover:border-white transition-colors"
              >
                Explore {season.name} Destinations
              </a>
            </div>
          </div>
        ))
      }
    </div>
  </div>
</section>
