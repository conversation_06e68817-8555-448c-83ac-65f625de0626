---
interface Props {
  name: string;
  propertyCount: number;
  imageUrl: string;
}

const { name, propertyCount, imageUrl } = Astro.props;
---

<div class="container-custom">
  <section class="relative h-[60vh] overflow-hidden rounded-lg">
    <img
      src={imageUrl}
      alt={name}
      class="w-full h-full object-cover rounded-lg"
    />
    <div class="absolute inset-0 bg-black/40 flex items-center">
      <div class="container-custom">
        <div class="max-w-3xl">
          <h1
            class="text-4xl md:text-5xl lg:text-6xl text-white font-baskervville mb-6"
          >
            {name}
          </h1>
          <div class="flex flex-wrap gap-6">
            <a
              href="#hotels"
              class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
            >
              View Properties
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-2"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            </a>
            <a
              href="/contact"
              class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
            >
              Plan Your Stay
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-2"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>


