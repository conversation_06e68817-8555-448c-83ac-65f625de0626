---
// Props for the component
interface Props {
  id: string;
  name: string;
  propertyCount: number;
  imageUrl: string;
  category?: string;
  description?: string;
  searchPrompt?: string;
}

const {
  id,
  name,
  propertyCount,
  imageUrl,
  category,
  description,
  searchPrompt,
} = Astro.props;

// Default descriptions based on category
const categoryDescriptions: Record<string, string> = {
  "Perfect for Beginners":
    "Ideal for first-timers and early learners, these destinations offer wide, gentle slopes, excellent ski schools, and a high number of blue runs. Well-groomed pistes and patient instructors make your first ski experience smooth and confidence-building.",
  "Perfect for Apres Ski":
    "When the skiing day ends, the fun continues with vibrant nightlife, cozy mountain bars, live music, and exceptional dining experiences that create the perfect social atmosphere.",
  "Perfect for Off Piste":
    "For the adventurous skier seeking untouched powder and challenging terrain. These destinations offer thrilling backcountry experiences with expert guides to help you discover hidden gems.",
};

// Use provided description or default based on category
const displayDescription =
  description ||
  (category &&
    categoryDescriptions[category as keyof typeof categoryDescriptions]) ||
  "Discover this extraordinary destination with Perfect Piste, offering the ideal blend of luxury accommodations and world-class skiing experiences.";

// Use category or default to destination name
const displayCategory = category || `${name}, SWITZERLAND`;
---

<a
  href={searchPrompt
    ? `/ai-search?query=${encodeURIComponent(searchPrompt)}&user_message=${encodeURIComponent(searchPrompt)}&ai_search=true`
    : `/destinations/${id}`}
  class="block group"
>
  <div
    class="h-[450px] overflow-hidden relative rounded-md shadow-sm border border-border/10 transition-all duration-300 hover:shadow-md hover:border-foreground/20 group-hover:-translate-y-1"
  >
    <img
      src={imageUrl}
      alt={name}
      class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
    />
    <div class="absolute inset-0 bg-black/40 p-8 flex flex-col justify-start">
      <div class="mb-2">
        {
          displayCategory.startsWith("Perfect for") ? (
            <>
              <h3 class="text-white text-lg font-karla uppercase tracking-wider leading-tight">
                Perfect for
              </h3>
              <h3 class="text-white text-xl font-baskervville uppercase tracking-wider leading-tight">
                {displayCategory.replace("Perfect for ", "")}
              </h3>
            </>
          ) : (
            <h3 class="text-white text-xl font-baskervville uppercase tracking-wider leading-tight">
              {displayCategory}
            </h3>
          )
        }
      </div>
      <p
        class="text-white text-sm font-baskervville font-light leading-relaxed max-w-md"
      >
        {displayDescription}
      </p>
    </div>
  </div>
</a>
