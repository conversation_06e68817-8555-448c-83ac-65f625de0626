import React from "react";

interface DestinationHeroProps {
  name: string;
  location: string;
  country: string;
  mainImage: string;
  images: string[];
}

const DestinationHero: React.FC<DestinationHeroProps> = ({
  name,
  location,
  country,
  mainImage,
  images,
}) => {
  // Function to open the photo modal
  const openPhotoModal = (index: number) => {
    // Check if the openPhotoModal function exists in the window object
    if (typeof (window as any).openPhotoModal === "function") {
      (window as any).openPhotoModal(index);
    } else {
      console.warn("Photo modal function not available");
    }
  };

  return (
    <div className="container-custom">
      {/* Title Section */}
      <div className="pt-8 pb-6">
        <h1 className="text-3xl font-baskervville mb-2">{name}</h1>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary mr-1"
              >
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              <span>
                {location}
                {country ? `, ${country}` : ""}
              </span>
            </div>
          </div>
          <div className="flex gap-4">
            <button className="flex items-center hover:underline">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-1"
              >
                <path d="M7 11v13l7-7 7 7V11"></path>
                <rect x="3" y="3" width="18" height="8" rx="1" ry="1"></rect>
              </svg>
              Share
            </button>
            <button
              id="add-to-wishlist"
              className="flex items-center hover:underline"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-1 wishlist-icon"
              >
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
              </svg>
              <span className="wishlist-text">Save</span>
            </button>
          </div>
        </div>
      </div>

      {/* Image Gallery */}
      <div className="relative">
        {/* View All Photos Button */}
        <button
          onClick={() => openPhotoModal(0)}
          className="absolute top-4 right-4 z-10 bg-white rounded-lg px-4 py-2 shadow-md flex items-center font-medium text-sm hover:bg-gray-50 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2"
          >
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <circle cx="8.5" cy="8.5" r="1.5"></circle>
            <polyline points="21 15 16 10 5 21"></polyline>
          </svg>
          View all photos
        </button>

        <div
          id="gallery"
          className="grid grid-cols-2 gap-2 rounded-lg overflow-hidden mb-12 h-[450px]"
        >
          <div className="h-full">
            <img
              src={mainImage}
              alt={name}
              className="w-full h-full object-cover cursor-pointer hover:opacity-95 transition-opacity"
              onClick={() => openPhotoModal(0)}
            />
          </div>
          <div className="grid grid-cols-2 gap-2 h-full">
            {images.slice(1, 5).map((image, index) => (
              <div key={index} className="overflow-hidden">
                <img
                  src={image}
                  alt={`${name} - Image ${index + 2}`}
                  className="w-full h-full object-cover cursor-pointer hover:opacity-95 transition-opacity"
                  onClick={() => openPhotoModal(index + 1)}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DestinationHero;
