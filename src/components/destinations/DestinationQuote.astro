---
interface Props {
  quote?: string;
  author?: string;
  quoteIndex?: number;
}

// Collection of famous travel quotes
const travelQuotes = [
  {
    quote: "The mountains are calling and I must go.",
    author: "<PERSON>",
  },
  {
    quote:
      "The world is a book, and those who do not travel read only one page.",
    author: "<PERSON>",
  },
  {
    quote:
      "Travel makes one modest. You see what a tiny place you occupy in the world.",
    author: "<PERSON><PERSON>",
  },
  {
    quote: "We travel not to escape life, but for life not to escape us.",
    author: "Anonymous",
  },
  {
    quote:
      "The real voyage of discovery consists not in seeking new landscapes, but in having new eyes.",
    author: "<PERSON>",
  },
  {
    quote: "Travel far enough, you meet yourself.",
    author: "<PERSON>",
  },
  {
    quote: "The journey not the arrival matters.",
    author: "<PERSON>.S<PERSON>",
  },
  {
    quote: "Adventure is worthwhile in itself.",
    author: "<PERSON>",
  },
  {
    quote: "To travel is to live.",
    author: "<PERSON>",
  },
  {
    quote: "Not all those who wander are lost.",
    author: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  },
];

// Get props or use random quote if none provided
const { quoteIndex } = Astro.props;

// If a specific quote index is provided, use it; otherwise, select a random quote
const randomIndex =
  typeof quoteIndex === "number"
    ? quoteIndex
    : Math.floor(Math.random() * travelQuotes.length);
const { quote, author } =
  Astro.props.quote && Astro.props.author
    ? { quote: Astro.props.quote, author: Astro.props.author }
    : travelQuotes[randomIndex];
---

<section
  class="py-20 bg-primary text-primary-foreground relative overflow-hidden"
>
  <!-- Background pattern -->
  <div class="absolute inset-0 opacity-5 pattern-dots"></div>
  <div class="container-custom relative z-10">
    <div class="max-w-3xl mx-auto text-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="48"
        height="48"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="1"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="mx-auto mb-6 opacity-50"
      >
        <path
          d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"
        ></path>
        <path
          d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"
        ></path>
      </svg>
      <div
        id="quote-container"
        class="min-h-[220px] flex flex-col items-center justify-center"
      >
        <div
          class="quote-text-container min-h-[120px] flex items-center justify-center mb-6"
        >
          <p
            class="text-2xl md:text-3xl lg:text-4xl font-baskervville italic leading-relaxed animate-fade-in"
          >
            "{quote}"
          </p>
        </div>
        <div class="w-16 h-0.5 bg-primary-foreground/30 mx-auto mb-4"></div>
        <div
          class="author-container min-h-[40px] flex items-center justify-center"
        >
          <p
            class="text-lg md:text-xl font-karla tracking-wider uppercase animate-fade-in animate-delay-200"
          >
            {author}
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  // Client-side JavaScript to rotate quotes
  const quotes = [
    {
      quote: "The mountains are calling and I must go.",
      author: "John Muir",
    },
    {
      quote:
        "The world is a book, and those who do not travel read only one page.",
      author: "Saint Augustine",
    },
    {
      quote:
        "Travel makes one modest. You see what a tiny place you occupy in the world.",
      author: "Gustave Flaubert",
    },
    {
      quote: "We travel not to escape life, but for life not to escape us.",
      author: "Anonymous",
    },
    {
      quote:
        "The real voyage of discovery consists not in seeking new landscapes, but in having new eyes.",
      author: "Marcel Proust",
    },
    {
      quote: "Travel far enough, you meet yourself.",
      author: "David Mitchell",
    },
    {
      quote: "The journey not the arrival matters.",
      author: "T.S. Eliot",
    },
    {
      quote: "Adventure is worthwhile in itself.",
      author: "Amelia Earhart",
    },
    {
      quote: "To travel is to live.",
      author: "Hans Christian Andersen",
    },
    {
      quote: "Not all those who wander are lost.",
      author: "J.R.R. Tolkien",
    },
  ];

  // Function to update the quote
  function updateQuote() {
    const container = document.getElementById("quote-container");
    if (!container) return;

    // Fade out
    container.style.opacity = "0";
    container.style.transform = "translateY(10px)";

    setTimeout(() => {
      // Get random quote
      const randomIndex = Math.floor(Math.random() * quotes.length);
      const { quote, author } = quotes[randomIndex];

      // Update DOM
      const quoteElement = container.querySelector(".quote-text-container p");
      const authorElement = container.querySelector(".author-container p");

      if (quoteElement && authorElement) {
        quoteElement.textContent = `"${quote}"`;
        authorElement.textContent = author;
      }

      // Fade in
      container.style.opacity = "1";
      container.style.transform = "translateY(0)";
    }, 500);
  }

  // Set up rotation interval (every 8 seconds)
  let quoteInterval: number | undefined;

  document.addEventListener("DOMContentLoaded", () => {
    const container = document.getElementById("quote-container");
    if (container) {
      // Add transition styles
      container.style.transition = "opacity 0.5s ease, transform 0.5s ease";

      // Start rotation after a delay
      setTimeout(() => {
        updateQuote();
        quoteInterval = setInterval(updateQuote, 8000);
      }, 3000); // Initial delay before first change
    }
  });

  // Clean up interval when page is left
  document.addEventListener("visibilitychange", () => {
    if (document.visibilityState === "hidden") {
      clearInterval(quoteInterval);
    } else if (document.visibilityState === "visible") {
      updateQuote();
      quoteInterval = setInterval(updateQuote, 8000);
    }
  });
</script>
