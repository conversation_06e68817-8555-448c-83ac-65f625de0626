---
interface Props {
  title: string;
  description: string;
  backgroundImage?: string;
  ctaText?: string;
  ctaLink?: string;
}

const {
  title,
  description,
  backgroundImage = "https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
  ctaText = "Explore Destinations",
  ctaLink = "#featured-destinations",
} = Astro.props;
---

<div class="container-custom">
  <section class="relative h-[60vh] overflow-hidden rounded-lg">
    <img
      src={backgroundImage}
      alt={title}
      class="w-full h-full object-cover rounded-lg"
    />
    <div class="absolute inset-0 bg-black/40 flex items-center">
      <div class="container-custom">
        <div class="max-w-3xl">
          <h1
            class="text-4xl md:text-5xl lg:text-6xl text-white font-baskervville mb-6"
          >
            {title}
          </h1>
          <p class="text-xl text-white/90 mb-8">
            {description}
          </p>
          <div class="flex flex-wrap gap-6">
            <a
              href={ctaLink}
              class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
            >
              {ctaText}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-2"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            </a>
            <a
              href="/contact"
              class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
            >
              Contact Us
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-2"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>


