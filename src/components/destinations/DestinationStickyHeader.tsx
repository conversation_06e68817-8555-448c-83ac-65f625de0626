import React, { useEffect } from "react";

interface DestinationStickyHeaderProps {
  name: string;
  location: string;
  country: string;
  onSectionClick?: (sectionId: string) => void;
}

const DestinationStickyHeader: React.FC<DestinationStickyHeaderProps> = ({
  name,
  location,
  country,
  onSectionClick,
}) => {
  // Add smooth scrolling behavior for anchor links
  useEffect(() => {
    const handleAnchorClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (
        target.tagName === "A" &&
        target.getAttribute("href")?.startsWith("#")
      ) {
        e.preventDefault();
        const targetId = target.getAttribute("href");
        if (!targetId) return;

        const targetElement = document.querySelector(targetId);
        if (!targetElement) return;

        // Calculate offset based on target
        let offset = 80; // Default offset

        // Special handling for different sections
        if (targetId === "#activities") {
          offset = 100; // Extra offset for activities section
        } else if (targetId === "#experience") {
          offset = 70; // Less offset for experience section
        }

        window.scrollTo({
          top:
            targetElement.getBoundingClientRect().top + window.scrollY - offset,
          behavior: "smooth",
        });
      }
    };

    // Add event listeners to all anchor links in the sticky header
    const anchorLinks = document.querySelectorAll(
      '.sticky-header-container a[href^="#"]'
    );
    anchorLinks.forEach((link) => {
      link.addEventListener("click", handleAnchorClick as EventListener);
    });

    // Cleanup
    return () => {
      anchorLinks.forEach((link) => {
        link.removeEventListener("click", handleAnchorClick as EventListener);
      });
    };
  }, []);

  return (
    <div className="sticky-header-container bg-white shadow-md py-3 border-b">
      <div className="container-custom">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => {
                if (onSectionClick) {
                  onSectionClick("#experience");
                } else {
                  const experienceSection = document.getElementById("experience");
                  if (experienceSection) {
                    experienceSection.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                    // Add offset
                    setTimeout(() => {
                      window.scrollBy(0, -70);
                    }, 10);
                  }
                }
              }}
              className="text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors hover:border-b-2 hover:border-[#285DA6] pb-1 cursor-pointer bg-transparent border-0"
            >
              Experience
            </button>
            <button
              onClick={() => {
                if (onSectionClick) {
                  onSectionClick("#activities");
                } else {
                  const activitiesSection = document.getElementById("activities");
                  if (activitiesSection) {
                    activitiesSection.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                    // Add offset
                    setTimeout(() => {
                      window.scrollBy(0, -80);
                    }, 10);
                  }
                }
              }}
              className="text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors hover:border-b-2 hover:border-[#285DA6] pb-1 cursor-pointer bg-transparent border-0"
            >
              Activities
            </button>
            <button
              onClick={() => {
                if (onSectionClick) {
                  onSectionClick("#tips");
                } else {
                  const tipsSection = document.getElementById("tips");
                  if (tipsSection) {
                    tipsSection.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                    // Add offset
                    setTimeout(() => {
                      window.scrollBy(0, -80);
                    }, 10);
                  }
                }
              }}
              className="text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors hover:border-b-2 hover:border-[#285DA6] pb-1 cursor-pointer bg-transparent border-0"
            >
              Travel Tips
            </button>
          </div>
          <div>
            <h1 className="text-lg font-baskervville">{name}</h1>
            <div className="flex items-center text-sm text-muted-foreground">
              <span>
                {location}
                {country ? `, ${country}` : ""}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DestinationStickyHeader;
