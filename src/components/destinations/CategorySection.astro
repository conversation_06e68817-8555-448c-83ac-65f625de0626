---
import DestinationCard from "./DestinationCard.astro";

// Define the ski categories
const categories = [
  {
    id: "beginners",
    name: "Beginners",
    category: "Perfect for Beginners",
    propertyCount: 12,
    imageUrl:
      "https://images.unsplash.com/photo-1520364311437-283acc84fe43?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    description:
      "Gentle slopes, excellent ski schools, and supportive infrastructure make these destinations ideal for those just starting their skiing journey. Enjoy wide, well-groomed runs and patient instructors.",
  },
  {
    id: "apres-ski",
    name: "Apres Ski",
    category: "Perfect for Apres Ski",
    propertyCount: 15,
    imageUrl:
      "https://images.unsplash.com/photo-1594873562950-86b8b8fa1399?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    description:
      "When the skiing day ends, the fun continues with vibrant nightlife, cozy mountain bars, live music, and exceptional dining experiences that create the perfect social atmosphere.",
  },
  {
    id: "off-piste",
    name: "Off Piste",
    category: "Perfect for Off Piste",
    propertyCount: 10,
    imageUrl:
      "https://images.unsplash.com/photo-1535640368727-187c8a674b5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    description:
      "For the adventurous skier seeking untouched powder and challenging terrain. These destinations offer thrilling backcountry experiences with expert guides to help you discover hidden gems.",
  },
];
---

<section class="pt-12 sm:pt-16 pb-8">
  <div class="container-custom">
    <div
      class="flex flex-col sm:flex-row justify-between items-start mb-6 sm:mb-10"
    >
      <div>
        <span class="text-sm uppercase tracking-wider text-[#285DA6] font-karla"
          >Ski Categories</span
        >
        <h2
          class="text-2xl sm:text-[28px] md:text-[32px] font-baskervville uppercase tracking-[0.1em] mt-2 sm:whitespace-nowrap"
        >
          Find Your Perfect Experience
        </h2>
        <p class="font-baskervville text-sm sm:text-base mt-2 max-w-2xl">
          Explore our tailored skiing experiences designed for every skill level
          and preference
        </p>
      </div>
      <a
        href="#all-destinations"
        class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-[#285DA6] border-b border-[#285DA6] pb-1 transition-all hover:border-transparent mt-4 sm:mt-1"
      >
        View All Destinations
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2"
        >
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </a>
    </div>

    <style>
      h2 {
        font-size: clamp(1.5rem, 1.8vw, 1.8rem);
        overflow: visible;
        max-width: 100%;
      }

      @media (max-width: 1200px) {
        h2 {
          font-size: clamp(1.2rem, 1.4vw, 1.4rem);
          letter-spacing: 0.08em;
        }
      }
    </style>

    <div
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-6 sm:gap-y-8 mt-6 sm:mt-8 mx-auto"
    >
      {
        categories.map((category) => (
          <DestinationCard
            id={category.id}
            name={category.name}
            propertyCount={category.propertyCount}
            imageUrl={category.imageUrl}
            category={category.category}
            description={category.description}
          />
        ))
      }
    </div>
  </div>
</section>
