---
interface Feature {
  icon: string;
  title: string;
  description: string;
}

interface Props {
  features?: Feature[];
}

const defaultFeatures = [
  {
    icon: `<path d="m8 3 4 8 5-5 5 15H2L8 3z"></path>`,
    title: "Mountain Escapes",
    description: "Pristine slopes & scenic views",
  },
  {
    icon: `<polygon points="3 6 9 3 15 6 21 3 21 18 15 21 9 18 3 21"></polygon>
          <line x1="9" x2="9" y1="3" y2="18"></line>
          <line x1="15" x2="15" y1="6" y2="21"></line>`,
    title: "Curated Locations",
    description: "Hand-selected destinations",
  },
  {
    icon: `<circle cx="12" cy="12" r="10"></circle>
          <polygon
            points="16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76"
          ></polygon>`,
    title: "Expert Guidance",
    description: "Local knowledge & insights",
  },
  {
    icon: `<rect width="18" height="18" x="3" y="3" rx="2"></rect>
          <circle cx="9" cy="9" r="2"></circle>
          <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>`,
    title: "Breathtaking Views",
    description: "Unforgettable landscapes",
  },
];

const { features = defaultFeatures } = Astro.props;
---

<section class="pt-20 pb-4 bg-white relative overflow-hidden">
  <div class="container-custom relative z-10">
    <div class="text-center mb-16">
      <p
        class="section-micro-headline font-karla tracking-wider uppercase text-[#285DA6]"
      >
        The Perfect Piste Experience
      </p>
      <h2 class="section-title mb-4">What Sets Us Apart</h2>
      <div class="w-20 h-1 bg-[#285DA6]/30 mx-auto rounded-full"></div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
      {
        features.map((feature: Feature) => (
          <div class="flex flex-col items-center text-center group p-6 rounded-lg transition-all duration-300 transform hover:-translate-y-1 hover:bg-[#285DA6]/5">
            <div class="w-16 h-16 rounded-full bg-[#285DA6]/10 flex items-center justify-center mb-6 transition-colors duration-300 shadow-sm group-hover:shadow-md group-hover:bg-[#285DA6]/20">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="text-[#285DA6]"
                set:html={feature.icon}
              />
            </div>
            <h3 class="text-xl font-baskervville mb-3 uppercase tracking-wider">
              {feature.title}
            </h3>
            <p class="text-center mx-auto max-w-[200px] text-foreground/70">
              {feature.description}
            </p>
          </div>
        ))
      }
    </div>
  </div>
</section>
