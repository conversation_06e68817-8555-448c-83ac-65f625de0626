---
import HotelCard from '../cards/HotelCard';


interface Hotel {
  id: number;
  name: string;
  location: string;
  rating: number;
  price: number;
  currency: string;
  imageUrl: string;
  destination: string;
  description: string;
  tags: [string];
  amenities?: string[];
}

interface Props {
  destinationName: string;
  hotels: Hotel[];
}

const { destinationName, hotels } = Astro.props;
---

<section id="hotels" class="py-16 bg-accent/30">
  <div class="container-custom">
    <div class="mb-12">
      <h2 class="text-3xl md:text-4xl font-baskervville">
        Top Hotels in {destinationName}
      </h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {
        hotels.map((hotel) => (
          <HotelCard
            id={hotel.id}
            name={hotel.name}
            location={hotel.location}
            rating={hotel.rating}
            imageUrl={hotel.imageUrl}
            amenities={hotel.amenities}
            description={hotel.description}
            client:load
          />
        ))
      }
    </div>
  </div>
</section>
