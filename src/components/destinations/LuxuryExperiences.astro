---
const experiences = [
  {
    title: "Heli-Skiing Adventures",
    description: "Access untouched powder and remote slopes with our exclusive helicopter skiing packages.",
    image: "https://images.unsplash.com/photo-1565992441121-4367c2967103?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    icon: `<path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
           <path d="m7 9 5 3-5 3z"></path>`
  },
  {
    title: "Private Mountain Guides",
    description: "Explore hidden gems and secret spots with our expert local guides who customize each experience.",
    image: "https://images.unsplash.com/photo-1605540436563-5bca919ae766?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    icon: `<path d="M20 12V8H6a2 2 0 0 1-2-2c0-1.1.9-2 2-2h12v4"></path>
           <path d="M4 6v12c0 1.1.9 2 2 2h14v-4"></path>
           <path d="M12 12v4h4"></path>
           <path d="M12 12h4"></path>`
  },
  {
    title: "Alpine Gastronomy",
    description: "Indulge in exceptional mountain cuisine with Michelin-starred restaurants and private chefs.",
    image: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    icon: `<path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
           <path d="M7 2v20"></path>
           <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>`
  }
];
---

<section class="py-16 relative overflow-hidden">
  <div class="absolute inset-0 z-0">
    <img 
      src="https://images.unsplash.com/photo-1519681393784-d120267933ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" 
      alt="Mountain background" 
      class="w-full h-full object-cover opacity-10"
    />
    <div class="absolute inset-0 bg-background/80 backdrop-blur-sm"></div>
  </div>

  <div class="container-custom relative z-10">
    <div class="text-center mb-12">
      <p class="section-micro-headline">Beyond Ordinary</p>
      <h2 class="section-title">Extraordinary Experiences</h2>
      <p class="section-subtitle max-w-3xl mx-auto">
        Elevate your mountain getaway with our curated collection of exclusive experiences that create unforgettable memories.
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      {experiences.map(experience => (
        <div class="bg-background/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-glow border border-border/30 transition-all duration-300 hover:shadow-glow-lg">
          <div class="h-48 overflow-hidden relative">
            <img 
              src={experience.image} 
              alt={experience.title} 
              class="w-full h-full object-cover transition-transform duration-700 hover:scale-105"
            />
            <div class="absolute top-4 right-4 w-10 h-10 bg-primary/90 backdrop-blur-sm rounded-full flex items-center justify-center text-white">
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="20" 
                height="20" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                stroke-width="1.5" 
                stroke-linecap="round" 
                stroke-linejoin="round"
                set:html={experience.icon}
              >
              </svg>
            </div>
          </div>
          
          <div class="p-6">
            <h3 class="text-xl font-baskervville mb-3">{experience.title}</h3>
            <p class="text-foreground/70 text-sm mb-4">{experience.description}</p>
            
            <a href="/experiences" class="inline-flex items-center text-primary text-sm font-medium">
              Discover More
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                stroke-width="2" 
                stroke-linecap="round" 
                stroke-linejoin="round"
                class="ml-1"
              >
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </a>
          </div>
        </div>
      ))}
    </div>
  </div>
</section>
