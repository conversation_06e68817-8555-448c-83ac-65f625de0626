---
const insights = [
  {
    title: "Best Time to Visit",
    description: "While winter offers world-class skiing, summer brings hiking and mountain biking opportunities. Spring and autumn offer unique experiences with fewer crowds.",
    icon: `<circle cx="12" cy="12" r="10"></circle>
           <polyline points="12 6 12 12 16 14"></polyline>`
  },
  {
    title: "Local Customs",
    description: "Each alpine region has its own unique traditions and etiquette. Our destination guides provide insights to help you connect with local culture.",
    icon: `<path d="M3 21h18"></path>
           <path d="M3 7V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v2"></path>
           <path d="M3 16v-5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v5"></path>
           <path d="M10 21V7"></path>
           <path d="M14 21V7"></path>`
  },
  {
    title: "Packing Essentials",
    description: "Mountain weather can be unpredictable. Our seasonal packing guides ensure you're prepared for changing conditions and special events.",
    icon: `<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
           <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
           <line x1="12" y1="22.08" x2="12" y2="12"></line>`
  },
  {
    title: "Exclusive Access",
    description: "Perfect Piste guests enjoy priority reservations at renowned restaurants, private access to slopes before they open to the public, and VIP treatment.",
    icon: `<rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
           <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>`
  },
  {
    title: "Sustainable Travel",
    description: "We partner with eco-conscious properties and providers committed to preserving the natural beauty of mountain environments for future generations.",
    icon: `<path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
           <circle cx="12" cy="12" r="3"></circle>`
  },
  {
    title: "Health & Wellness",
    description: "From altitude adjustment tips to spa recommendations, we provide guidance to ensure your physical and mental wellbeing throughout your journey.",
    icon: `<path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>`
  }
];
---

<section class="py-16">
  <div class="container-custom">
    <div class="text-center mb-12">
      <p class="section-micro-headline">Expert Knowledge</p>
      <h2 class="section-title">Destination Insights</h2>
      <p class="section-subtitle max-w-3xl mx-auto">
        Benefit from our extensive local knowledge and insider tips to enhance your mountain experience.
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {insights.map(insight => (
        <div class="bg-background p-6 rounded-lg border border-border/30 shadow-sm hover:shadow-glow transition-all duration-300">
          <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="24" 
              height="24" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              stroke-width="1.5" 
              stroke-linecap="round" 
              stroke-linejoin="round"
              class="text-primary"
              set:html={insight.icon}
            >
            </svg>
          </div>
          <h3 class="text-lg font-baskervville mb-3">{insight.title}</h3>
          <p class="text-foreground/70 text-sm">{insight.description}</p>
        </div>
      ))}
    </div>

    <div class="mt-12 text-center">
      <a href="/travel-guide" class="btn-outline">
        Explore Our Travel Guides
      </a>
    </div>
  </div>
</section>
