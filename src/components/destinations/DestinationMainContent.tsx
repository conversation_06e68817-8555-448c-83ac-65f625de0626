import React from "react";

// Define our own interface to avoid import issues
interface DestinationData {
  id: string;
  name: string;
  location: string;
  country: string;
  description: string;
  difficulty: string;
  price: string;
  priceRange: string;
  imageUrl?: string;
  images?: Array<{ url: string; alt?: string }>;
  amenities?: string[];
  elevation?: string;
  trails?: number;
  snowDepth?: string;
  weather?: string;
  temperature?: string;
  newSnow?: string;
}

interface DestinationMainContentProps {
  destinationData: DestinationData;
}

const DestinationMainContent: React.FC<DestinationMainContentProps> = ({
  destinationData,
}) => {
  // Helper function to render difficulty level
  const renderDifficultyLevel = (difficulty: string) => {
    const levels = {
      beginner: { label: "Beginner", color: "bg-green-500" },
      intermediate: { label: "Intermediate", color: "bg-blue-500" },
      advanced: { label: "Advanced", color: "bg-red-500" },
      expert: { label: "Expert", color: "bg-black" },
      "all-levels": { label: "All Levels", color: "bg-purple-500" },
    };

    const difficultyInfo =
      levels[difficulty as keyof typeof levels] || levels["all-levels"];

    return (
      <div className="flex items-center gap-2">
        <div className={`w-3 h-3 rounded-full ${difficultyInfo.color}`}></div>
        <span>{difficultyInfo.label}</span>
      </div>
    );
  };

  // Helper function to render price range
  const renderPriceRange = (priceRange: string) => {
    const ranges = {
      budget: { label: "Budget", symbols: "€" },
      "mid-range": { label: "Mid-Range", symbols: "€€" },
      luxury: { label: "Luxury", symbols: "€€€" },
      "ultra-luxury": { label: "Ultra Luxury", symbols: "€€€€" },
    };

    const rangeInfo =
      ranges[priceRange as keyof typeof ranges] || ranges["mid-range"];

    return (
      <div className="flex items-center gap-2">
        <span className="font-medium">{rangeInfo.label}</span>
        <span className="text-primary">{rangeInfo.symbols}</span>
      </div>
    );
  };

  // Default travel tips if none provided
  const defaultTips = [
    {
      title: "Best Time to Visit",
      description:
        "The ideal time to visit is during the winter months (December to March) for skiing, or summer (June to August) for hiking and outdoor activities.",
    },
    {
      title: "Local Cuisine",
      description:
        "Don't miss the opportunity to sample authentic local dishes. Our concierge can arrange private dining experiences with renowned chefs.",
    },
    {
      title: "Getting Around",
      description:
        "We offer private transportation services to ensure comfortable and convenient travel throughout your stay.",
    },
  ];

  return (
    <div className="container-custom">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-12">
        {/* Main Content - Left Column */}
        <div className="lg:col-span-2">
          {/* Experience Section */}
          <section id="experience" className="mb-12">
            <h2 className="text-2xl font-baskervville mb-4">
              Experience {destinationData.name}
            </h2>
            <p className="text-lg mb-6 leading-relaxed">
              {destinationData.description}
            </p>
            <div className="grid grid-cols-2 gap-6 mb-6">
              <div className="bg-accent/20 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Difficulty Level</h3>
                {renderDifficultyLevel(destinationData.difficulty)}
              </div>
              <div className="bg-accent/20 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Price Range</h3>
                {renderPriceRange(destinationData.priceRange)}
              </div>
            </div>
            <p className="text-foreground/70">
              Immerse yourself in the unique culture and breathtaking landscapes
              of {destinationData.name}. Our curated collection of luxury
              properties offers the perfect base to explore this magnificent
              destination, combining authentic local experiences with
              unparalleled comfort and service.
            </p>
          </section>

          {/* Activities Section */}
          <section id="activities" className="mb-12">
            <h2 className="text-2xl font-baskervville mb-4">
              Activities & Attractions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white border border-border/30 rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-medium mb-3">Winter Activities</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary mr-2"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span>World-class skiing and snowboarding</span>
                  </li>
                  <li className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary mr-2"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span>Snowshoeing and cross-country skiing</span>
                  </li>
                  <li className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary mr-2"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span>Ice skating and sledding</span>
                  </li>
                  <li className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary mr-2"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span>Après-ski entertainment</span>
                  </li>
                </ul>
              </div>
              <div className="bg-white border border-border/30 rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-medium mb-3">Summer Activities</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary mr-2"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span>Hiking and mountain biking</span>
                  </li>
                  <li className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary mr-2"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span>Alpine lakes and swimming</span>
                  </li>
                  <li className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary mr-2"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span>Paragliding and rock climbing</span>
                  </li>
                  <li className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary mr-2"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span>Local festivals and events</span>
                  </li>
                </ul>
              </div>
            </div>
          </section>

          {/* Travel Tips Section */}
          <section id="tips" className="mb-12">
            <h2 className="text-2xl font-baskervville mb-4">Travel Tips</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {defaultTips.map((tip, index) => (
                <div
                  key={index}
                  className="bg-white border border-border/30 rounded-lg p-6 shadow-sm"
                >
                  <h3 className="text-lg font-medium mb-3">{tip.title}</h3>
                  <p className="text-foreground/70 text-sm">
                    {tip.description}
                  </p>
                </div>
              ))}
            </div>
          </section>
        </div>

        {/* Sidebar - Right Column */}
        <div className="lg:col-span-1">
          <div className="sticky top-20">
            <div className="bg-white border border-border/30 rounded-lg p-6 shadow-sm mb-6">
              <h3 className="text-lg font-medium mb-4">Destination Details</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-foreground/70 mb-1">
                    Location
                  </h4>
                  <p>
                    {destinationData.location}
                    {destinationData.country
                      ? `, ${destinationData.country}`
                      : ""}
                  </p>
                </div>
                {destinationData.elevation && (
                  <div>
                    <h4 className="text-sm font-medium text-foreground/70 mb-1">
                      Elevation
                    </h4>
                    <p>{destinationData.elevation}</p>
                  </div>
                )}
                {destinationData.trails && (
                  <div>
                    <h4 className="text-sm font-medium text-foreground/70 mb-1">
                      Number of Trails
                    </h4>
                    <p>{destinationData.trails}</p>
                  </div>
                )}
                {destinationData.snowDepth && (
                  <div>
                    <h4 className="text-sm font-medium text-foreground/70 mb-1">
                      Snow Depth
                    </h4>
                    <p>{destinationData.snowDepth}</p>
                  </div>
                )}
                {destinationData.weather && (
                  <div>
                    <h4 className="text-sm font-medium text-foreground/70 mb-1">
                      Weather
                    </h4>
                    <p>{destinationData.weather}</p>
                  </div>
                )}
                {destinationData.temperature && (
                  <div>
                    <h4 className="text-sm font-medium text-foreground/70 mb-1">
                      Temperature
                    </h4>
                    <p>{destinationData.temperature}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DestinationMainContent;
