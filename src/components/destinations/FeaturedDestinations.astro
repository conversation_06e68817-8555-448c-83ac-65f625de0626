---
import DestinationCard from "./DestinationCard.astro";

interface Destination {
  id: string;
  name: string;
  propertyCount: number;
  imageUrl: string;
}

interface Props {
  destinations: Destination[];
  iconUrl?: string;
  title?: string;
}

const { 
  destinations,
  iconUrl = "https://images.unsplash.com/photo-1482938289607-e9573fc25ebb?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100&q=80",
  title = "Featured Destinations"
} = Astro.props;

const featuredDestinations = destinations.slice(0, 3);
---

<section class="py-16">
  <div class="container-custom">
    <div class="flex items-center mb-12">
      <img
        src={iconUrl}
        alt="Mountain Icon"
        class="w-12 h-12 object-cover rounded-full mr-4"
      />
      <h2 class="text-3xl md:text-4xl font-baskervville">
        {title}
      </h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      {
        featuredDestinations.map((destination) => (
          <DestinationCard
            id={destination.id}
            name={destination.name}
            propertyCount={destination.propertyCount}
            imageUrl={destination.imageUrl}
          />
        ))
      }
    </div>
  </div>
</section>
