---
interface Tip {
  title: string;
  description: string;
  imageKeyword: string;
  link: string;
}

interface Props {
  destinationName: string;
  tips?: Tip[];
}

const defaultTips = [
  {
    title: "Best Time to Visit",
    description:
      "The ideal time to visit is during the shoulder seasons (spring and fall) when the weather is pleasant and crowds are smaller.",
    imageKeyword: "season",
    link: "https://images.pexels.com/photos/2016572/pexels-photo-2016572.jpeg?auto=compress&cs=tinysrgb&w=1200",
  },
  {
    title: "Local Cuisine",
    description:
      "Don't miss the opportunity to sample authentic local dishes. Our concierge can arrange private dining experiences with renowned chefs.",
    imageKeyword: "food",
    link: "https://images.pexels.com/photos/14715961/pexels-photo-14715961.jpeg?auto=compress&cs=tinysrgb&w=1200",
  },
  {
    title: "Getting Around",
    description:
      "We offer private transportation services to ensure comfortable and convenient travel throughout your stay.",
    imageKeyword: "travel",
    link: "https://images.pexels.com/photos/12070983/pexels-photo-12070983.jpeg?auto=compress&cs=tinysrgb&w=1200",
  },
];

const { destinationName, tips = defaultTips } = Astro.props;
console.log("destinationName", tips);
---

<section id="tips" class="py-16 bg-background border-y border-border/20">
  <div class="container-custom">
    <div class="text-center mb-12">
      <p class="section-micro-headline" style="color: #285DA6;">
        Insider Knowledge
      </p>
      <h2 class="section-title">Travel Tips for {destinationName}</h2>
      <p class="section-subtitle max-w-3xl mx-auto">
        Make the most of your luxury experience with our expert recommendations
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      {
        tips.map((tip) => (
          <div class="bg-card border border-border/30 rounded-lg shadow-sm overflow-hidden hover:shadow-glow transition-all duration-300">
            <div class="h-48 overflow-hidden">
              <img
                src={tip.link}
                alt={tip.title}
                class="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
              />
            </div>
            <div class="p-6">
              <h3 class="text-xl font-baskervville mb-3">{tip.title}</h3>
              <p class="text-foreground/70">
                {tip.description.includes("{destinationName}")
                  ? tip.description.replace(
                      "{destinationName}",
                      destinationName
                    )
                  : tip.description}
              </p>
            </div>
          </div>
        ))
      }
    </div>
  </div>
</section>
