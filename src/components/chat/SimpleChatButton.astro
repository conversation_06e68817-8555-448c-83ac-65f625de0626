---
// A simple chat button component that doesn't rely on React hydration
---

<button
  id="chat-button"
  class="flex items-center justify-center w-14 h-14 rounded-full bg-primary text-primary-foreground shadow-glow transition-all duration-300 hover:shadow-glow-lg hover:scale-105"
  aria-label="Open ski assistant chat"
>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
  >
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
    ></path>
  </svg>
</button>

<!-- Removed custom styles -->

<div
  id="chat-window"
  class="absolute bottom-16 right-0 w-72 md:w-96 bg-card border border-primary/30 rounded-lg shadow-glow overflow-hidden transition-all duration-500 transform translate-y-12 opacity-0 pointer-events-none z-50"
>
  <div
    class="flex items-center justify-between p-4 border-b border-border/60 bg-primary/5"
  >
    <div class="flex items-center">
      <div
        class="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center mr-3"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="text-primary"
        >
          <path
            d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
          ></path>
        </svg>
      </div>
      <div>
        <h3 class="font-baskervville text-base">Ski Assistant</h3>
        <p class="text-xs text-foreground/60">Find Your Perfect Piste</p>
      </div>
    </div>
    <button
      id="close-chat"
      class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-accent/50 transition-colors text-foreground/60 hover:text-foreground"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    </button>
  </div>

  <div class="h-80 overflow-y-auto p-4 flex flex-col gap-3">
    <div class="flex justify-start">
      <div
        class="max-w-[80%] rounded-lg p-3 text-foreground"
        style="background-color: color-mix(in srgb, var(--accent) 70%, transparent);"
      >
        <p class="text-sm">
          Welcome to our Ski Assistant. How may I help you find your perfect ski
          destination?
        </p>
      </div>
    </div>
  </div>

  <form id="chat-form" class="p-3 border-t border-border/60 flex gap-2">
    <input
      type="text"
      id="chat-input"
      class="flex-1 bg-background/50 border border-border/60 rounded-md px-3 py-2 text-sm focus:outline-none focus:border-primary/50 focus:ring-1 focus:ring-primary/30 placeholder:text-muted-foreground"
      placeholder="Ask about ski destinations..."
    />
    <button
      type="submit"
      class="bg-primary text-primary-foreground rounded-md px-3 flex items-center justify-center hover:bg-primary/90 transition-colors"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <line x1="22" y1="2" x2="11" y2="13"></line>
        <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
      </svg>
    </button>
  </form>
</div>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const chatButton = document.getElementById("chat-button");
    const chatWindow = document.getElementById("chat-window");
    const closeChat = document.getElementById("close-chat");
    const chatForm = document.getElementById("chat-form") as HTMLFormElement;
    const chatInput = document.getElementById("chat-input") as HTMLInputElement;

    if (chatButton && chatWindow && closeChat && chatForm && chatInput) {
      // Toggle chat window
      chatButton.addEventListener("click", () => {
        chatWindow.classList.toggle("translate-y-12");
        chatWindow.classList.toggle("opacity-0");
        chatWindow.classList.toggle("pointer-events-none");
      });

      // Close chat window
      closeChat.addEventListener("click", () => {
        chatWindow.classList.add("translate-y-12");
        chatWindow.classList.add("opacity-0");
        chatWindow.classList.add("pointer-events-none");
      });

      // Handle form submission
      chatForm.addEventListener("submit", (e) => {
        e.preventDefault();

        const message = chatInput.value.trim();
        if (!message) return;

        // Add user message
        const chatContainer = chatWindow.querySelector(".flex.flex-col");
        if (!chatContainer) return;

        const userMessageDiv = document.createElement("div");
        userMessageDiv.className = "flex justify-end";
        userMessageDiv.innerHTML = `
        <div class="max-w-[80%] rounded-lg p-3 text-foreground bg-accent/20" style="background-color: color-mix(in srgb, var(--accent) 20%, transparent);" >
            <div class="flex items-start gap-2">
              <p class="text-sm">${message}</p>
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mt-1 text-foreground/60">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
            </div>
          </div>
        `;

        chatContainer.appendChild(userMessageDiv);

        // Clear input
        chatInput.value = "";

        // Simulate response after a brief delay
        setTimeout(() => {
          const botMessageDiv = document.createElement("div");
          botMessageDiv.className = "flex justify-start";
          botMessageDiv.innerHTML = `
            <div class="max-w-[80%] rounded-lg p-3 text-foreground" style="background-color: color-mix(in srgb, var(--accent) 70%, transparent);">
              <p class="text-sm">Thank you for your inquiry. Our luxury travel advisor will curate personalized recommendations based on your preferences. Is there anything specific about this destination that interests you most?</p>
            </div>
          `;

          chatContainer.appendChild(botMessageDiv);

          // Scroll to bottom
          chatContainer.scrollTop = chatContainer.scrollHeight;
        }, 1000);
      });
    }
  });
</script>
