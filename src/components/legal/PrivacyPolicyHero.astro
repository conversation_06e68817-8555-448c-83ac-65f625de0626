---
// Props for the component
interface Props {
  title?: string;
  subtitle?: string;
  imageUrl?: string;
}

const {
  title = "Privacy Policy",
  subtitle = "Learn about how <PERSON> Piste collects, uses, and protects your personal information in accordance with data protection regulations",
  imageUrl = "https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
} = Astro.props;
---

<!-- Hero Section -->
<div class="container-custom">
  <section class="relative h-[50vh] sm:h-[60vh] overflow-hidden rounded-lg">
    <img
      src={imageUrl}
      alt={title}
      class="w-full h-full object-cover rounded-lg"
    />
    <div
      class="absolute inset-0 bg-gradient-to-r from-black/70 to-black/50 flex items-center"
    >
      <div class="px-8 sm:px-12 lg:px-16">
        <div>
          <div class="max-w-4xl">
            <h1
              class="text-2xl sm:text-4xl md:text-5xl lg:text-6xl text-white font-baskervville mb-4 sm:mb-6 animate-fade-in leading-tight"
            >
              {title}
            </h1>
            <p
              class="text-base sm:text-xl text-white/90 mb-6 sm:mb-8 animate-fade-in animate-delay-200 max-w-2xl"
            >
              {subtitle}
            </p>
            <div class="flex flex-col sm:flex-row gap-4 sm:gap-6 animate-fade-in animate-delay-400">
              <a
                href="#privacy-policy-content"
                class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
              >
                Read Policy
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="ml-2"
                >
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                  <polyline points="12 5 19 12 12 19"></polyline>
                </svg>
              </a>
              <a
                href="/contact"
                class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
              >
                Contact Us
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="ml-2"
                >
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                  <polyline points="22,6 12,13 2,6"/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

<style>
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
    opacity: 0;
  }

  .animate-delay-200 {
    animation-delay: 0.2s;
  }

  .animate-delay-400 {
    animation-delay: 0.4s;
  }
</style>
