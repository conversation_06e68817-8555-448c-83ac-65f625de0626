import React from "react";

interface RegistrationSuccessProps {
  firstName: string;
  email: string;
}

const RegistrationSuccess: React.FC<RegistrationSuccessProps> = ({
  firstName,
  email,
}) => {
  return (
    <div className="bg-white p-6 md:p-8 rounded-lg shadow-md border border-gray-100">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-green-100 mb-6 animate-pulse">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-10 w-10 text-green-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h2 className="text-2xl font-baskervville mb-3">
          Registration Successful!
        </h2>
        <p className="text-gray-600 text-lg">
          Welcome to Perfect Piste,{" "}
          <span className="font-medium text-gray-800">{firstName}</span>!
        </p>
      </div>

      {/* <div className="bg-blue-50 p-5 rounded-lg mb-8 border-l-4 border-blue-500">
        <div className="flex items-start">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 text-blue-500 mr-3 mt-0.5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <div>
            <p className="text-sm text-blue-800 font-medium mb-1">
              Verification Required
            </p>
            <p className="text-sm text-blue-700">
              We've sent a confirmation email to{" "}
              <strong className="font-medium">{email}</strong>. Please check
              your inbox to verify your account.
            </p>
          </div>
        </div>
      </div> */}

      {/* <div className="bg-green-50 p-5 rounded-lg mb-8 border-l-4 border-green-500">
        <div className="flex items-start">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 text-green-500 mr-3 mt-0.5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
          <div>
            <p className="text-sm text-green-800 font-medium mb-1">
              Redirecting to Login
            </p>
            <p className="text-sm text-green-700">
              You will be redirected to the login page in a moment. Please sign
              in with your new credentials.
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-5 mb-8">
        <h3 className="font-karla font-medium text-lg border-b border-gray-100 pb-2">
          What's next?
        </h3>
        <ul className="space-y-4">
          <li className="flex items-start">
            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-[#285DA6] mr-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-800">
                Complete your profile
              </p>
              <p className="text-xs text-gray-500">
                Get personalized recommendations based on your preferences
              </p>
            </div>
          </li>
          <li className="flex items-start">
            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-[#285DA6] mr-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1v-5a1 1 0 00-.293-.707l-2-2A1 1 0 0015 7h-1z" />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-800">
                Browse luxury accommodations
              </p>
              <p className="text-xs text-gray-500">
                Explore our exclusive collection of premium ski destinations
              </p>
            </div>
          </li>
          <li className="flex items-start">
            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-[#285DA6] mr-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-800">
                Create your wishlist
              </p>
              <p className="text-xs text-gray-500">
                Save your favorite destinations for future trips
              </p>
            </div>
          </li>
        </ul>
      </div> */}

      {/* <div className="space-y-3">
        <a
          href="/"
          className="block w-full bg-[#285DA6] text-white py-3 text-center rounded-lg hover:bg-[#285DA6]/90 transition-all transform hover:translate-y-[-2px] font-karla font-medium text-sm uppercase tracking-wider shadow-sm hover:shadow"
        >
          Go to My Account
        </a>
        <a
          href="/"
          className="block w-full text-center py-3 text-[#285DA6] hover:text-[#285DA6]/80 transition-colors text-sm font-medium"
        >
          Return to Home
        </a>
      </div> */}
    </div>
  );
};

export default RegistrationSuccess;
