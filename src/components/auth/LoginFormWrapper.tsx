import React, { useState } from "react";
import { UserProvider } from "../../contexts/UserContext";
import LoginForm from "./LoginForm";
import OtpLoginForm from "./OtpLoginForm";

const LoginFormWrapper: React.FC = () => {
  // Default to OTP login and comment out password option for now
  const [authMethod, setAuthMethod] = useState<"password" | "otp">("otp");

  return (
    <UserProvider>
      <div className="mb-6">
        {/* Commenting out auth method toggle for now
        <div className="flex justify-center space-x-4 mb-6">
          <button
            onClick={() => setAuthMethod("password")}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              authMethod === "password"
                ? "bg-[#285DA6] text-white"
                : "bg-gray-100 text-gray-600 hover:bg-gray-200"
            }`}
          >
            Password Login
          </button>
          <button
            onClick={() => setAuthMethod("otp")}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              authMethod === "otp"
                ? "bg-[#285DA6] text-white"
                : "bg-gray-100 text-gray-600 hover:bg-gray-200"
            }`}
          >
            OTP Login
          </button>
        </div>
        */}

        {/* Always show OTP login form */}
        <OtpLoginForm />
      </div>
    </UserProvider>
  );
};

export default LoginFormWrapper;
