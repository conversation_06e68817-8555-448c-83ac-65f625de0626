import React from "react";
import LoginForm from "./LoginForm";
import { useUser } from "../../contexts/UserContext";
import { useEffect } from "react";

const LoginPage: React.FC = () => {
  const { isAuthenticated, loading } = useUser();

  // Redirect if already logged in
  useEffect(() => {
    if (!loading && isAuthenticated) {
      window.location.href = "/";
    }
  }, [isAuthenticated, loading]);

  if (loading) {
    return (
      <div className="container-custom py-16">
        <div className="flex justify-center items-center min-h-[50vh]">
          <div className="text-center">
            <div className="inline-block w-12 h-12 border-4 border-[#285DA6]/30 border-t-[#285DA6] rounded-full animate-spin mb-4"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container-custom py-16">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl md:text-4xl font-baskervville mb-8 text-center">
          Sign In
        </h1>
        <p className="text-center text-foreground/70 mb-10 max-w-xl mx-auto">
          Sign in to your Perfect Piste account to access your bookings,
          wishlist, and exclusive member benefits.
        </p>

        <LoginForm />
      </div>
    </div>
  );
};

export default LoginPage;
