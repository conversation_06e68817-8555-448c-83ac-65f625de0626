import React from "react";
import RegisterForm from "./RegisterForm";
import { useUser } from "../../contexts/UserContext";
import { useEffect } from "react";

const RegisterPage: React.FC = () => {
  const { isAuthenticated, loading } = useUser();

  // Redirect if already logged in
  useEffect(() => {
    if (!loading && isAuthenticated) {
      window.location.href = "/";
    }
  }, [isAuthenticated, loading]);

  if (loading) {
    return (
      <div className="container-custom py-16">
        <div className="flex justify-center items-center min-h-[50vh]">
          <div className="text-center">
            <div className="inline-block w-12 h-12 border-4 border-[#285DA6]/30 border-t-[#285DA6] rounded-full animate-spin mb-4"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container-custom py-16">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl md:text-4xl font-baskervville mb-8 text-center">
          Create Account
        </h1>
        <p className="text-center text-foreground/70 mb-10 max-w-xl mx-auto">
          Join Perfect Piste to access exclusive luxury ski experiences,
          personalized recommendations, and member-only benefits.
        </p>

        <RegisterForm />
      </div>
    </div>
  );
};

export default RegisterPage;
