import React, { useState, useMemo } from "react";
import { Search, X } from "lucide-react";
import { Accordion } from "../ui/accordion";

// FAQ data organized by categories
const bookingFAQs = [
  {
    question: "How do I make a booking?",
    answer: "You can make a booking through our website by searching for your desired destination and dates, selecting a property, and following the booking process. Alternatively, you can contact our concierge team who will be happy to assist you with your reservation.",
    category: "Booking & Payments"
  },
  {
    question: "What payment methods do you accept?",
    answer: "We accept all major credit cards including Visa, Mastercard, and American Express. We also accept bank transfers for larger bookings. Payment is processed securely through our encrypted payment system.",
    category: "Booking & Payments"
  },
  {
    question: "Can I modify or cancel my booking?",
    answer: "Yes, you can modify or cancel your booking subject to the property's cancellation policy. Each property has different terms, which are clearly displayed during the booking process. Our concierge team can assist you with any changes to your reservation.",
    category: "Booking & Payments"
  },
  {
    question: "When will I be charged for my booking?",
    answer: "Payment timing depends on the property and booking type. Some properties require full payment at booking, while others may only require a deposit. The payment schedule will be clearly shown before you confirm your booking.",
    category: "Booking & Payments"
  }
];

const propertyFAQs = [
  {
    question: "Are all properties ski-in/ski-out?",
    answer: "Not all properties are ski-in/ski-out, but many are. We clearly indicate the proximity to slopes for each property. Our collection includes ski-in/ski-out chalets, properties within walking distance of lifts, and some that require a short shuttle ride to the slopes.",
    category: "Properties & Amenities"
  },
  {
    question: "What amenities are typically included?",
    answer: "Amenities vary by property but commonly include fully equipped kitchens, Wi-Fi, heating, linens, and towels. Many properties also feature hot tubs, saunas, fireplaces, and concierge services. Specific amenities are listed on each property page.",
    category: "Properties & Amenities"
  },
  {
    question: "Do properties include ski equipment?",
    answer: "Most properties do not include ski equipment, but we can arrange equipment rental through our preferred partners. Some luxury chalets may include basic equipment or have partnerships with local rental shops for discounted rates.",
    category: "Properties & Amenities"
  },
  {
    question: "Are pets allowed?",
    answer: "Pet policies vary by property. Some properties welcome pets while others do not allow them. You can filter your search to show only pet-friendly properties, and specific pet policies are detailed on each property page.",
    category: "Properties & Amenities"
  }
];

const serviceFAQs = [
  {
    question: "What concierge services do you offer?",
    answer: "Our concierge team can assist with restaurant reservations, activity bookings, transportation arrangements, grocery shopping, ski lessons, equipment rental, and any special requests to enhance your stay. We're available 24/7 during your stay.",
    category: "Services & Support"
  },
  {
    question: "Do you provide airport transfers?",
    answer: "Yes, we can arrange airport transfers including private cars, shared shuttles, or helicopter transfers depending on your destination and preferences. Transfer options and pricing are available during the booking process.",
    category: "Services & Support"
  },
  {
    question: "Can you arrange ski lessons and lift passes?",
    answer: "Absolutely! Our concierge team can arrange ski lessons with certified instructors, purchase lift passes, and book other mountain activities. We work with the best local providers to ensure you have an exceptional experience on the slopes.",
    category: "Services & Support"
  },
  {
    question: "What if I need assistance during my stay?",
    answer: "Our concierge team is available 24/7 during your stay to assist with any needs or emergencies. You'll receive contact information before your arrival, and many properties also have on-site staff or local representatives.",
    category: "Services & Support"
  }
];

const travelFAQs = [
  {
    question: "What's the best time to visit ski destinations?",
    answer: "The ski season typically runs from December to April, with peak season being Christmas/New Year and February/March school holidays. For the best snow conditions and fewer crowds, consider visiting in January or early March.",
    category: "Travel & Destinations"
  },
  {
    question: "Do I need travel insurance?",
    answer: "We highly recommend comprehensive travel insurance that covers winter sports activities. This should include coverage for medical expenses, trip cancellation, and ski equipment. We can recommend trusted insurance providers.",
    category: "Travel & Destinations"
  },
  {
    question: "What should I pack for a ski holiday?",
    answer: "Essential items include warm, waterproof clothing, thermal layers, gloves, goggles, sunscreen, and après-ski attire. Many items can be rented locally if you prefer not to travel with bulky equipment. We provide a detailed packing list after booking.",
    category: "Travel & Destinations"
  },
  {
    question: "Are there activities for non-skiers?",
    answer: "Yes! Most ski destinations offer activities like snowshoeing, sledding, spa treatments, shopping, scenic cable car rides, and excellent dining. Many of our properties are located in charming alpine villages with plenty to explore.",
    category: "Travel & Destinations"
  }
];

// Combine all FAQs
const allFAQs = [...bookingFAQs, ...propertyFAQs, ...serviceFAQs, ...travelFAQs];

const FAQSearchWithFilter: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");

  // Filter FAQs based on search query
  const filteredFAQs = useMemo(() => {
    if (!searchQuery.trim()) {
      return {
        "Booking & Payments": bookingFAQs,
        "Properties & Amenities": propertyFAQs,
        "Services & Support": serviceFAQs,
        "Travel & Destinations": travelFAQs,
      };
    }

    const query = searchQuery.toLowerCase();
    const filtered = allFAQs.filter(
      (faq) =>
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query)
    );

    // Group filtered results by category
    const groupedResults: Record<string, typeof allFAQs> = {};
    filtered.forEach((faq) => {
      if (!groupedResults[faq.category]) {
        groupedResults[faq.category] = [];
      }
      groupedResults[faq.category].push(faq);
    });

    return groupedResults;
  }, [searchQuery]);

  const clearSearch = () => {
    setSearchQuery("");
  };

  const totalResults = Object.values(filteredFAQs).reduce(
    (total, faqs) => total + faqs.length,
    0
  );

  return (
    <div className="space-y-12 sm:space-y-16">
      {/* Search Component */}
      <div className="relative max-w-2xl mx-auto px-4 sm:px-0">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
            <Search className="h-4 w-4 sm:h-5 sm:w-5 text-[#285DA6]/60" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search frequently asked questions..."
            className="w-full pl-10 sm:pl-12 pr-10 sm:pr-12 py-3 sm:py-4 bg-white/80 backdrop-blur-sm border border-border/30 rounded-lg sm:rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/40 transition-all duration-200 font-karla text-sm sm:text-base text-foreground placeholder:text-foreground/50"
          />
          {searchQuery && (
            <button
              onClick={clearSearch}
              className="absolute inset-y-0 right-0 pr-3 sm:pr-4 flex items-center text-[#285DA6]/60 hover:text-[#285DA6] transition-colors duration-200"
              aria-label="Clear search"
            >
              <X className="h-4 w-4 sm:h-5 sm:w-5" />
            </button>
          )}
        </div>

        {searchQuery && (
          <div className="mt-3 text-xs sm:text-sm text-foreground/60 font-karla text-center px-2">
            {totalResults > 0 ? (
              <>
                Found <span className="font-semibold text-[#285DA6]">{totalResults}</span> result{totalResults !== 1 ? 's' : ''} for: <span className="font-semibold text-[#285DA6]">"{searchQuery}"</span>
              </>
            ) : (
              <>
                No results found for: <span className="font-semibold text-[#285DA6]">"{searchQuery}"</span>
              </>
            )}
          </div>
        )}
      </div>

      {/* FAQ Results */}
      {totalResults > 0 ? (
        <div className="space-y-8 sm:space-y-12 lg:space-y-16">
          {Object.entries(filteredFAQs).map(([category, faqs]) => {
            if (faqs.length === 0) return null;

            const getCategoryIcon = (category: string) => {
              switch (category) {
                case "Booking & Payments":
                  return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect width="20" height="14" x="2" y="5" rx="2"/>
                      <line x1="2" y1="10" x2="22" y2="10"/>
                    </svg>
                  );
                case "Properties & Amenities":
                  return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M3 21h18"/>
                      <path d="M5 21V7l8-4v18"/>
                      <path d="M19 21V11l-6-4"/>
                    </svg>
                  );
                case "Services & Support":
                  return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                      <circle cx="9" cy="7" r="4"/>
                      <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                      <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                    </svg>
                  );
                case "Travel & Destinations":
                  return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                      <polyline points="14,2 14,8 20,8"/>
                      <line x1="16" y1="13" x2="8" y2="13"/>
                      <line x1="16" y1="17" x2="8" y2="17"/>
                      <polyline points="10,9 9,9 8,9"/>
                    </svg>
                  );
                default:
                  return null;
              }
            };

            return (
              <div key={category} className="bg-white/80 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 lg:p-8 shadow-lg border border-border/20 hover:shadow-xl transition-all duration-300">
                <div className="flex items-center mb-6 sm:mb-8">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#285DA6] to-[#1e4a8c] rounded-lg sm:rounded-xl flex items-center justify-center mr-3 sm:mr-4 shadow-lg flex-shrink-0">
                    {getCategoryIcon(category)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h2 className="text-lg sm:text-xl lg:text-2xl font-baskervville text-foreground truncate">
                      {category}
                    </h2>
                    {searchQuery && (
                      <span className="text-xs sm:text-sm text-[#285DA6] font-karla font-semibold block sm:hidden">
                        {faqs.length} result{faqs.length !== 1 ? 's' : ''}
                      </span>
                    )}
                  </div>
                  {searchQuery && (
                    <span className="hidden sm:block text-sm text-[#285DA6] font-karla font-semibold ml-4">
                      {faqs.length} result{faqs.length !== 1 ? 's' : ''}
                    </span>
                  )}
                </div>
                <Accordion items={faqs.map(faq => ({ question: faq.question, answer: faq.answer }))} />
              </div>
            );
          })}
        </div>
      ) : searchQuery ? (
        <div className="text-center py-8 sm:py-12 px-4">
          <div className="w-12 h-12 sm:w-16 sm:h-16 bg-[#285DA6]/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search className="h-6 w-6 sm:h-8 sm:w-8 text-[#285DA6]/60" />
          </div>
          <h3 className="text-lg sm:text-xl font-baskervville text-foreground mb-2">
            No results found
          </h3>
          <p className="text-sm sm:text-base text-foreground/60 font-karla mb-4 max-w-md mx-auto">
            Try searching with different keywords or browse all categories below.
          </p>
          <button
            onClick={clearSearch}
            className="inline-flex items-center justify-center px-4 sm:px-6 py-2 sm:py-3 bg-[#285DA6] text-white rounded-lg sm:rounded-xl font-karla font-bold text-xs sm:text-sm uppercase tracking-[0.05em] hover:bg-[#285DA6]/90 transition-colors"
          >
            Show All FAQs
          </button>
        </div>
      ) : null}
    </div>
  );
};

export default FAQSearchWithFilter;
