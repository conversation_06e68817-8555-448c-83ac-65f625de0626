import React, { useState } from "react";
import { Search, X } from "lucide-react";

interface FAQSearchProps {
  onSearch: (query: string) => void;
  placeholder?: string;
}

const FAQSearch: React.FC<FAQSearchProps> = ({
  onSearch,
  placeholder = "Search frequently asked questions...",
}) => {
  const [query, setQuery] = useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    onSearch(value);
  };

  const clearSearch = () => {
    setQuery("");
    onSearch("");
  };

  return (
    <div className="relative max-w-2xl mx-auto mb-12">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-[#285DA6]/60" />
        </div>
        <input
          type="text"
          value={query}
          onChange={handleInputChange}
          placeholder={placeholder}
          className="w-full pl-12 pr-12 py-4 bg-white/80 backdrop-blur-sm border border-border/30 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/40 transition-all duration-200 font-karla text-foreground placeholder:text-foreground/50"
        />
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-4 flex items-center text-[#285DA6]/60 hover:text-[#285DA6] transition-colors duration-200"
            aria-label="Clear search"
          >
            <X className="h-5 w-5" />
          </button>
        )}
      </div>
      
      {query && (
        <div className="mt-3 text-sm text-foreground/60 font-karla text-center">
          Searching for: <span className="font-semibold text-[#285DA6]">"{query}"</span>
        </div>
      )}
    </div>
  );
};

export default FAQSearch;
