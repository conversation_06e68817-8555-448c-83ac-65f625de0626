---
interface Props {
  images: string[];
  hotelName: string;
}

const { images, hotelName } = Astro.props;
---

<div
  id="photo-modal"
  class="fixed inset-0 z-[200] bg-black/90 flex items-center justify-center"
>
  <div class="absolute top-4 left-4 z-10">
    <button
      id="close-modal"
      class="text-white hover:text-gray-300 transition-colors p-2"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    </button>
  </div>

  <div class="absolute top-4 right-4 z-10 flex items-center gap-4">
    <button
      class="text-white hover:text-gray-300 transition-colors flex items-center gap-2 bg-black/50 backdrop-blur-sm px-3 py-1.5 rounded-full"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path d="M7 11v13l7-7 7 7V11"></path>
        <rect x="3" y="3" width="18" height="8" rx="1" ry="1"></rect>
      </svg>
      <span class="text-sm">Share</span>
    </button>

    <button
      class="text-white hover:text-gray-300 transition-colors flex items-center gap-2 bg-black/50 backdrop-blur-sm px-3 py-1.5 rounded-full group"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="group-hover:fill-red-500 group-hover:text-red-500 transition-colors"
      >
        <path
          d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
        ></path>
      </svg>
      <span class="text-sm">Save</span>
    </button>
  </div>

  <div class="w-full h-full flex items-center justify-center">
    <div
      class="relative w-full max-w-5xl h-[80vh] flex items-center justify-center"
    >
      <!-- Main Image -->
      <div
        id="modal-image-container"
        class="w-full h-full flex items-center justify-center"
      >
        <img
          id="modal-image"
          src=""
          alt={`${hotelName} photo`}
          class="max-h-full max-w-full object-contain"
        />
      </div>

      <!-- Navigation Buttons -->
      <button
        id="prev-image"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm rounded-full p-3 hover:bg-white transition-colors"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M15 18l-6-6 6-6"></path>
        </svg>
      </button>

      <button
        id="next-image"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm rounded-full p-3 hover:bg-white transition-colors"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M9 18l6-6-6-6"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Image Counter -->
  <div
    class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 backdrop-blur-sm px-3 py-1.5 rounded-full text-white"
  >
    <span id="current-image-index">1</span> / <span id="total-images"
      >{images.length}</span
    >
  </div>

  <!-- Thumbnail Strip -->
  <div class="absolute bottom-16 left-0 right-0 overflow-x-auto py-4">
    <div
      class="flex items-center gap-2 px-4 max-w-5xl mx-auto"
      id="thumbnail-container"
    >
      {
        images.map((image, index) => (
          <div
            class="thumbnail-item flex-shrink-0 w-20 h-20 rounded-md overflow-hidden cursor-pointer border-2 border-transparent hover:border-white transition-colors"
            data-index={index}
          >
            <img
              src={image}
              alt={`${hotelName} thumbnail ${index + 1}`}
              class="w-full h-full object-cover"
            />
          </div>
        ))
      }
    </div>
  </div>
</div>

<script is:inline define:vars={{ images }}>
  // Client-side functionality for photo modal
  document.addEventListener("DOMContentLoaded", () => {
    const modal = document.getElementById("photo-modal");
    const closeBtn = document.getElementById("close-modal");
    const modalImage = document.getElementById("modal-image");
    const prevBtn = document.getElementById("prev-image");
    const nextBtn = document.getElementById("next-image");
    const currentIndexEl = document.getElementById("current-image-index");
    const thumbnails = document.querySelectorAll(".thumbnail-item");
    const thumbnailContainer = document.getElementById("thumbnail-container");

    let currentIndex = 0;
    let isNavigating = false;
    const preloadedImages = {};

    // Preload images for faster navigation
    const preloadImage = (index) => {
      if (preloadedImages[index] || index < 0 || index >= images.length) return;

      const img = new Image();
      img.src = images[index];
      preloadedImages[index] = img;
    };

    // Preload adjacent images
    const preloadAdjacentImages = (index) => {
      // Preload current image if not already loaded
      preloadImage(index);

      // Preload next and previous images
      preloadImage(index + 1);
      preloadImage(index - 1);

      // Preload a few more in each direction for faster browsing
      preloadImage(index + 2);
      preloadImage(index - 2);
    };

    // Function to update the modal image with optimizations
    const updateModalImage = (index) => {
      if (isNavigating || index < 0 || index >= images.length) return;

      isNavigating = true;
      currentIndex = index;

      // Use preloaded image if available, otherwise set src directly
      if (preloadedImages[index]) {
        modalImage.src = preloadedImages[index].src;
      } else {
        modalImage.src = images[index];
      }

      currentIndexEl.textContent = index + 1;

      // Update active thumbnail with optimized DOM operations
      thumbnails.forEach((thumb, i) => {
        if (i === index) {
          if (!thumb.classList.contains("border-white")) {
            thumb.classList.add("border-white");
          }
        } else if (thumb.classList.contains("border-white")) {
          thumb.classList.remove("border-white");
        }
      });

      // Scroll the active thumbnail into view
      const activeThumb = thumbnails[index];
      if (activeThumb) {
        const containerRect = thumbnailContainer.getBoundingClientRect();
        const thumbRect = activeThumb.getBoundingClientRect();

        // Only scroll if the thumbnail is not fully visible
        if (
          thumbRect.left < containerRect.left ||
          thumbRect.right > containerRect.right
        ) {
          thumbnailContainer.scrollLeft =
            activeThumb.offsetLeft -
            containerRect.width / 2 +
            thumbRect.width / 2;
        }
      }

      // Preload adjacent images for faster navigation
      preloadAdjacentImages(index);

      // Reset navigation lock after a short delay
      setTimeout(() => {
        isNavigating = false;
      }, 100);
    };

    // Open modal when a photo is clicked
    window.openPhotoModal = (index) => {
      // Preload the clicked image and adjacent images
      preloadAdjacentImages(index);

      // Show modal with a slight delay to allow for preloading
      setTimeout(() => {
        modal.classList.remove("hidden");
        document.body.style.overflow = "hidden"; // Prevent scrolling
        updateModalImage(index);
      }, 50);
    };

    // Close modal
    closeBtn.addEventListener("click", () => {
      modal.classList.add("hidden");
      document.body.style.overflow = ""; // Restore scrolling
    });

    // Navigate to previous image with debounce
    prevBtn.addEventListener("click", () => {
      if (isNavigating) return;
      const newIndex = (currentIndex - 1 + images.length) % images.length;
      updateModalImage(newIndex);
    });

    // Navigate to next image with debounce
    nextBtn.addEventListener("click", () => {
      if (isNavigating) return;
      const newIndex = (currentIndex + 1) % images.length;
      updateModalImage(newIndex);
    });

    // Thumbnail click with debounce
    thumbnails.forEach((thumb) => {
      thumb.addEventListener("click", () => {
        if (isNavigating) return;
        const index = parseInt(thumb.getAttribute("data-index"));
        updateModalImage(index);
      });
    });

    // Keyboard navigation with debounce
    document.addEventListener("keydown", (e) => {
      if (modal.classList.contains("hidden") || isNavigating) return;

      if (e.key === "Escape") {
        modal.classList.add("hidden");
        document.body.style.overflow = "";
      } else if (e.key === "ArrowLeft") {
        const newIndex = (currentIndex - 1 + images.length) % images.length;
        updateModalImage(newIndex);
      } else if (e.key === "ArrowRight") {
        const newIndex = (currentIndex + 1) % images.length;
        updateModalImage(newIndex);
      }
    });

    // Start preloading the first few images when the page loads
    for (let i = 0; i < Math.min(5, images.length); i++) {
      preloadImage(i);
    }
  });
</script>
