---
interface Props {
  images: string[];
  hotelName: string;
}

const { images, hotelName } = Astro.props;
---

<div
  id="photo-modal"
  class="fixed inset-0 z-[9999] bg-black/90 hidden flex items-center justify-center"
>
  <div class="absolute top-4 left-4 z-10">
    <button
      id="close-modal"
      class="text-white hover:text-gray-300 transition-colors p-2"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    </button>
  </div>

  <div class="absolute top-4 right-4 z-10 flex items-center gap-4">
    <button
      class="text-white hover:text-gray-300 transition-colors flex items-center gap-2 bg-black/50 backdrop-blur-sm px-3 py-1.5 rounded-full"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path d="M7 11v13l7-7 7 7V11"></path>
        <rect x="3" y="3" width="18" height="8" rx="1" ry="1"></rect>
      </svg>
      <span class="text-sm">Share</span>
    </button>

    <button
      class="text-white hover:text-gray-300 transition-colors flex items-center gap-2 bg-black/50 backdrop-blur-sm px-3 py-1.5 rounded-full group"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="group-hover:fill-red-500 group-hover:text-red-500 transition-colors"
      >
        <path
          d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
        ></path>
      </svg>
      <span class="text-sm">Save</span>
    </button>
  </div>

  <div class="w-full h-full flex items-center justify-center">
    <div
      class="relative w-full max-w-5xl h-[80vh] flex items-center justify-center"
    >
      <!-- Main Image -->
      <div
        id="modal-image-container"
        class="w-full h-full flex items-center justify-center"
      >
        <img
          id="modal-image"
          src=""
          alt={`${hotelName} photo`}
          class="max-h-full max-w-full object-contain"
        />
      </div>

      <!-- Navigation Buttons -->
      <button
        id="prev-image"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm rounded-full p-3 hover:bg-white transition-colors"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M15 18l-6-6 6-6"></path>
        </svg>
      </button>

      <button
        id="next-image"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm rounded-full p-3 hover:bg-white transition-colors"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M9 18l6-6-6-6"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Image Counter -->
  <div
    class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 backdrop-blur-sm px-3 py-1.5 rounded-full text-white"
  >
    <span id="current-image-index">1</span> / <span id="total-images"
      >{images.length}</span
    >
  </div>
</div>

<script is:inline define:vars={{ images }}>
  // Client-side functionality for photo modal
  document.addEventListener("DOMContentLoaded", () => {
    const modal = document.getElementById("photo-modal");
    const closeBtn = document.getElementById("close-modal");
    const modalImage = document.getElementById("modal-image");
    const prevBtn = document.getElementById("prev-image");
    const nextBtn = document.getElementById("next-image");
    const currentIndexEl = document.getElementById("current-image-index");

    let currentIndex = 0;
    let isNavigating = false;

    // Simple image cache
    const imageCache = {};

    // Function to update the modal image
    const updateModalImage = (index) => {
      if (isNavigating) return;
      isNavigating = true;

      currentIndex = index;

      // Use cached image if available
      if (imageCache[index]) {
        modalImage.src = imageCache[index];
      } else {
        modalImage.src = images[index];
        imageCache[index] = images[index];
      }

      currentIndexEl.textContent = index + 1;

      // Reset navigation lock after a short delay
      setTimeout(() => {
        isNavigating = false;
      }, 50);
    };

    // Open modal when a photo is clicked
    window.openPhotoModal = (index) => {
      modal.classList.remove("hidden");
      document.body.style.overflow = "hidden"; // Prevent scrolling
      updateModalImage(index);

      // Preload next image
      if (index + 1 < images.length) {
        imageCache[index + 1] = images[index + 1];
      }
    };

    // Close modal
    closeBtn.addEventListener("click", () => {
      modal.classList.add("hidden");
      document.body.style.overflow = ""; // Restore scrolling
    });

    // Navigate to previous image
    prevBtn.addEventListener("click", () => {
      if (isNavigating) return;
      const newIndex = (currentIndex - 1 + images.length) % images.length;
      updateModalImage(newIndex);

      // Preload previous image
      const prevIndex = (newIndex - 1 + images.length) % images.length;
      if (!imageCache[prevIndex]) {
        imageCache[prevIndex] = images[prevIndex];
      }
    });

    // Navigate to next image
    nextBtn.addEventListener("click", () => {
      if (isNavigating) return;
      const newIndex = (currentIndex + 1) % images.length;
      updateModalImage(newIndex);

      // Preload next image
      const nextIndex = (newIndex + 1) % images.length;
      if (!imageCache[nextIndex]) {
        imageCache[nextIndex] = images[nextIndex];
      }
    });

    // Keyboard navigation
    document.addEventListener("keydown", (e) => {
      if (modal.classList.contains("hidden") || isNavigating) return;

      if (e.key === "Escape") {
        modal.classList.add("hidden");
        document.body.style.overflow = "";
      } else if (e.key === "ArrowLeft") {
        const newIndex = (currentIndex - 1 + images.length) % images.length;
        updateModalImage(newIndex);
      } else if (e.key === "ArrowRight") {
        const newIndex = (currentIndex + 1) % images.length;
        updateModalImage(newIndex);
      }
    });

    // Close modal when clicking outside the image
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        modal.classList.add("hidden");
        document.body.style.overflow = "";
      }
    });

    // Preload first few images
    for (let i = 0; i < Math.min(3, images.length); i++) {
      imageCache[i] = images[i];
    }
  });
</script>
