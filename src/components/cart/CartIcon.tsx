import React, { useState, useRef, useEffect } from "react";
import { useCart } from "./useCart";

interface CartIconProps {
  className?: string;
}

const CartIcon: React.FC<CartIconProps> = ({ className = "" }) => {
  const { cart, totalPrice } = useCart();
  const [showTooltip, setShowTooltip] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Close tooltip when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        tooltipRef.current &&
        !tooltipRef.current.contains(event.target as Node)
      ) {
        setShowTooltip(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  return (
    <div className={`relative ${className}`} ref={tooltipRef}>
      <button
        type="button"
        className="flex items-center justify-center p-2 text-foreground hover:text-primary transition-colors"
        onClick={() => setShowTooltip(!showTooltip)}
        aria-label="Shopping Cart"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="transition-transform duration-200"
        >
          <circle cx="9" cy="21" r="1"></circle>
          <circle cx="20" cy="21" r="1"></circle>
          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
        </svg>
        {cart.length > 0 && (
          <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full h-4 w-4 flex items-center justify-center">
            {cart.length}
          </span>
        )}
      </button>

      {showTooltip && (
        <div className="absolute right-0 mt-2 w-72 bg-background border border-border rounded-md shadow-lg z-10 animate-slide-down">
          <div className="p-3 border-b border-border">
            <h3 className="font-karla font-medium text-sm">Your Cart</h3>
          </div>
          <div className="max-h-80 overflow-y-auto">
            {cart.length === 0 ? (
              <div className="p-4 text-center text-sm text-foreground/70">
                Your cart is empty
              </div>
            ) : (
              <ul>
                {cart.map((item) => (
                  <li
                    key={item.id}
                    className="p-3 border-b border-border/30 last:border-0"
                  >
                    <div className="flex flex-col gap-1">
                      <h4 className="font-karla text-sm font-medium">
                        {item.hotelName}
                      </h4>
                      <p className="text-xs text-foreground/70">
                        {item.roomType}
                      </p>
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-xs">
                          {new Date(item.checkIn).toLocaleDateString()} -{" "}
                          {new Date(item.checkOut).toLocaleDateString()}
                        </span>
                        <span className="font-medium text-sm">
                          {formatPrice(item.price)}
                        </span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
          {cart.length > 0 && (
            <div className="p-3 border-t border-border">
              <div className="flex justify-between items-center mb-3">
                <span className="font-karla text-sm">Total:</span>
                <span className="font-karla font-medium">
                  {formatPrice(totalPrice)}
                </span>
              </div>
              <a
                href="/checkout"
                className="block w-full py-2 px-4 bg-primary text-primary-foreground text-center text-sm rounded-md hover:bg-primary/90 transition-colors"
              >
                Checkout
              </a>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CartIcon;
