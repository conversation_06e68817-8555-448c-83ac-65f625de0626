import React from "react";

interface SafeCartIconProps {
  className?: string;
}

// This is a simplified version of CartIcon that doesn't use the context
// It's used as a fallback when the CartProvider isn't available
const SafeCartIcon: React.FC<SafeCartIconProps> = ({ className = "" }) => {
  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        className="flex items-center justify-center p-1.5 text-foreground hover:text-primary transition-colors"
        aria-label="Shopping Cart"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="transition-transform duration-200"
        >
          <circle cx="9" cy="21" r="1"></circle>
          <circle cx="20" cy="21" r="1"></circle>
          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
        </svg>
      </button>
    </div>
  );
};

export default SafeCartIcon;
