import React, { type ReactNode } from "react";
import { CartProvider } from "./CartContext";
import StripeProvider from "../payment/StripeProvider";

interface CartAndStripeProviderProps {
  children: ReactNode;
}

const CartAndStripeProvider: React.FC<CartAndStripeProviderProps> = ({ children }) => {
  return (
    <CartProvider>
      <StripeProvider>
        {children}
      </StripeProvider>
    </CartProvider>
  );
};

export default CartAndStripeProvider;
