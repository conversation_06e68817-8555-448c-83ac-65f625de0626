import React, { useState } from "react";
import { useCart } from "./useCart";
import { CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import {
  createHotelCart,
  createCartPaymentSession,
  updateCartPaymentSession,
  completeCart,
  capturePayment,
} from "../../utils/store/cart";

interface CustomerInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  postalCode: string;
  specialRequests: string;
}

const CartCheckout: React.FC = () => {
  const { cart, totalPrice, clearCart } = useCart();
  const stripe = useStripe();
  const elements = useElements();

  // State for customer information
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    country: "US",
    postalCode: "",
    specialRequests: "",
  });

  // State for checkout process
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [cartId, setCartId] = useState<string | null>(null);
  const [paymentSessionId, setPaymentSessionId] = useState<string | null>(null);

  const [currentStep, setCurrentStep] = useState<"info" | "payment">("info");

  // Calculate tax amount (10%)
  const taxAmount = totalPrice * 0.1;
  const totalWithTax = totalPrice + taxAmount;

  // Format currency
  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setCustomerInfo((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission for customer information
  const handleCustomerInfoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!cart.length) {
      setPaymentError("Your cart is empty");
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    try {
      // Use the first item in the cart for the API call
      const firstItem = cart[0];

      // Create a cart in the backend
      const cartResponse = await createHotelCart({
        hotel_id: firstItem.hotelId,
        room_config_id: firstItem.roomId,
        // room_id: firstItem.roomId,
        check_in_date: firstItem.checkIn,
        check_out_date: firstItem.checkOut,
        check_in_time: firstItem.checkInTime || "",
        check_out_time: firstItem.checkOutTime || "",
        guest_name: `${customerInfo.firstName} ${customerInfo.lastName}`,
        guest_email: customerInfo.email,
        guest_phone: customerInfo.phone,
        adults: firstItem.guests,
        children: 0,
        infants: firstItem.infants || 0,
        number_of_rooms: 1,
        total_amount: firstItem.price,
        currency_code: firstItem.currency.toLowerCase(),
        special_requests: customerInfo.specialRequests,
        shipping_address: {
          first_name: customerInfo.firstName,
          last_name: customerInfo.lastName,
          address_1: customerInfo.address,
          city: customerInfo.city,
          country_code: customerInfo.country.toLowerCase(),
          postal_code: customerInfo.postalCode,
          phone: customerInfo.phone,
        },
      });

      // Store the cart ID
      if (cartResponse.cart?.id) {
        setCartId(cartResponse.cart.id);

        // Create a payment session
        const paymentResponse = await createCartPaymentSession(
          cartResponse.cart.id,
          "pp_stripe_stripe"
        );

        if (paymentResponse.payment_session?.id) {
          setPaymentSessionId(paymentResponse.payment_session.id);
          // Move to payment step
          setCurrentStep("payment");
        } else {
          throw new Error("Failed to create payment session");
        }
      } else {
        throw new Error("Failed to create cart");
      }
    } catch (error) {
      console.error("Error creating cart:", error);
      setPaymentError(
        error instanceof Error
          ? error.message
          : "An error occurred during checkout"
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle payment submission
  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements || !cartId || !paymentSessionId) {
      setPaymentError("Payment processing is not available");
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    try {
      const cardElement = elements.getElement(CardElement);

      if (!cardElement) {
        throw new Error("Card element not found");
      }

      // Confirm card payment
      const { error, paymentIntent } = await stripe.confirmCardPayment(
        sessionStorage.getItem("stripe_client_secret") || "",
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: `${customerInfo.firstName} ${customerInfo.lastName}`,
              email: customerInfo.email,
              phone: customerInfo.phone,
              address: {
                line1: customerInfo.address,
                city: customerInfo.city,
                country: customerInfo.country,
                postal_code: customerInfo.postalCode,
              },
            },
          },
        }
      );

      if (error) {
        throw new Error(error.message);
      }

      if (paymentIntent && paymentIntent.status === "succeeded") {
        // Update payment session status
        await updateCartPaymentSession(cartId, paymentSessionId, {
          status: "authorized",
        });

        // Complete the cart
        const firstItem = cart[0];
        const completeResponse = await completeCart(
          cartId,
          firstItem.roomId,
          firstItem.checkIn,
          firstItem.checkOut
        );

        if (completeResponse.booking?.id) {
          // Capture the payment
          await capturePayment(paymentSessionId);

          // Create booking summary data from cart and customer info
          const firstItem = cart[0];
          const summaryData = {
            hotelId: firstItem.hotelId,
            roomId: firstItem.roomId,
            checkInDate: firstItem.checkIn,
            checkOutDate: firstItem.checkOut,
            checkInTime: firstItem.checkInTime || "14:00",
            checkOutTime: firstItem.checkOutTime || "11:00",
            nights: Math.ceil(
              (new Date(firstItem.checkOut).getTime() -
                new Date(firstItem.checkIn).getTime()) /
                (1000 * 60 * 60 * 24)
            ),
            adults: firstItem.guests - (firstItem.infants || 0),
            children: 0, // Not tracked in cart
            infants: firstItem.infants || 0,
            mealPlan: firstItem.mealPlan || "none",
            totalAmount: totalWithTax,
            basePrice: totalPrice,
            taxesAndFees: taxAmount,
            currencyCode: firstItem.currency?.toUpperCase() || "USD",
            hotel: {
              name: firstItem.hotelName,
              location: "", // Not available in cart
              image: firstItem.image || "",
              check_in_time: firstItem.checkInTime || "14:00",
              check_out_time: firstItem.checkOutTime || "11:00",
            },
            room: {
              name: firstItem.roomType,
              images: [firstItem.image || ""],
              thumbnail: firstItem.image || "",
              description: "",
              amenities: [],
              size: "",
              bedType: "",
              maxAdults: 2,
              maxChildren: 0,
              maxInfants: 1,
              mealPlanPrices: {},
            },
            guestDetails: {
              title: "Mr", // Default
              firstName: customerInfo.firstName,
              lastName: customerInfo.lastName,
              email: customerInfo.email,
              phone: customerInfo.phone,
              specialRequests: customerInfo.specialRequests || "",
            },
            travelers: {
              adults: [],
              children: [],
              infants: [],
            },
            orderId: completeResponse.booking.id,
            bookingReference: `PP-${Math.floor(Math.random() * 10000)
              .toString()
              .padStart(4, "0")}`,
            confirmedAt: new Date().toISOString(),
          };

          localStorage.setItem(
            "bookingSummaryData",
            JSON.stringify(summaryData)
          );

          // Clear the cart
          clearCart();

          // Redirect to booking summary page
          window.location.href = `/booking-summary/${completeResponse.booking.id}`;
        } else {
          throw new Error("Failed to complete booking");
        }
      } else {
        throw new Error("Payment failed");
      }
    } catch (error) {
      console.error("Error processing payment:", error);
      setPaymentError(
        error instanceof Error
          ? error.message
          : "An error occurred during payment processing"
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Render customer information form
  const renderCustomerInfoForm = () => (
    <form onSubmit={handleCustomerInfoSubmit} className="space-y-6">
      <h3 className="text-xl font-baskervville mb-4">Contact Information</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="firstName" className="block text-sm font-medium mb-1">
            First Name *
          </label>
          <input
            type="text"
            id="firstName"
            name="firstName"
            value={customerInfo.firstName}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
          />
        </div>
        <div>
          <label htmlFor="lastName" className="block text-sm font-medium mb-1">
            Last Name *
          </label>
          <input
            type="text"
            id="lastName"
            name="lastName"
            value={customerInfo.lastName}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-1">
            Email *
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={customerInfo.email}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
          />
        </div>
        <div>
          <label htmlFor="phone" className="block text-sm font-medium mb-1">
            Phone *
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={customerInfo.phone}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
          />
        </div>
      </div>

      <h3 className="text-xl font-baskervville mb-4 mt-8">Billing Address</h3>

      <div>
        <label htmlFor="address" className="block text-sm font-medium mb-1">
          Address *
        </label>
        <input
          type="text"
          id="address"
          name="address"
          value={customerInfo.address}
          onChange={handleInputChange}
          required
          className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="city" className="block text-sm font-medium mb-1">
            City *
          </label>
          <input
            type="text"
            id="city"
            name="city"
            value={customerInfo.city}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
          />
        </div>
        <div>
          <label htmlFor="country" className="block text-sm font-medium mb-1">
            Country *
          </label>
          <select
            id="country"
            name="country"
            value={customerInfo.country}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
          >
            <option value="US">United States</option>
            <option value="GB">United Kingdom</option>
            <option value="CA">Canada</option>
            <option value="AU">Australia</option>
            <option value="FR">France</option>
            <option value="DE">Germany</option>
            <option value="IT">Italy</option>
            <option value="ES">Spain</option>
            <option value="CH">Switzerland</option>
            <option value="AT">Austria</option>
          </select>
        </div>
      </div>

      <div>
        <label htmlFor="postalCode" className="block text-sm font-medium mb-1">
          Postal Code *
        </label>
        <input
          type="text"
          id="postalCode"
          name="postalCode"
          value={customerInfo.postalCode}
          onChange={handleInputChange}
          required
          className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
        />
      </div>

      <div>
        <label
          htmlFor="specialRequests"
          className="block text-sm font-medium mb-1"
        >
          Special Requests
        </label>
        <textarea
          id="specialRequests"
          name="specialRequests"
          value={customerInfo.specialRequests}
          onChange={handleInputChange}
          rows={3}
          className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
        />
      </div>

      {paymentError && (
        <div className="p-3 bg-red-50 border border-red-200 text-red-700 rounded-md">
          {paymentError}
        </div>
      )}

      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isProcessing}
          className="px-6 py-3 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 font-karla uppercase tracking-wider transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isProcessing ? "Processing..." : "Continue to Payment"}
        </button>
      </div>
    </form>
  );

  // Render payment form
  const renderPaymentForm = () => (
    <form onSubmit={handlePaymentSubmit} className="space-y-6">
      <h3 className="text-xl font-baskervville mb-4">Payment Information</h3>

      <div className="mb-6">
        <label
          htmlFor="card-element"
          className="block text-sm font-medium mb-2"
        >
          Credit or Debit Card
        </label>
        <div className="p-3 border border-[#285DA6]/20 rounded-md">
          <CardElement
            id="card-element"
            options={{
              style: {
                base: {
                  fontSize: "16px",
                  color: "#424770",
                  "::placeholder": {
                    color: "#aab7c4",
                  },
                },
                invalid: {
                  color: "#9e2146",
                },
              },
            }}
          />
        </div>
      </div>

      <div className="bg-[#285DA6]/5 p-4 rounded-lg mb-4">
        <h4 className="font-medium mb-3">Order Summary</h4>
        <div className="flex justify-between mb-2">
          <span>Subtotal</span>
          <span>{formatCurrency(totalPrice)}</span>
        </div>
        {/* Only show tax line if tax amount exists */}
        {taxAmount > 0 && (
          <div className="flex justify-between mb-2">
            <span>Tax (10%)</span>
            <span>{formatCurrency(taxAmount)}</span>
          </div>
        )}
        <div className="flex justify-between font-medium pt-2 border-t border-[#285DA6]/10 mt-2">
          <div>
            <span>Total</span>
            <p className="text-xs font-normal text-gray-500 mt-1">Inclusive of tax</p>
          </div>
          <span>{formatCurrency(taxAmount > 0 ? totalWithTax : totalPrice)}</span>
        </div>
      </div>

      {paymentError && (
        <div className="p-3 bg-red-50 border border-red-200 text-red-700 rounded-md">
          {paymentError}
        </div>
      )}

      <div className="flex justify-between">
        <button
          type="button"
          onClick={() => setCurrentStep("info")}
          disabled={isProcessing}
          className="px-6 py-3 border border-[#285DA6] text-[#285DA6] rounded-lg hover:bg-[#285DA6]/5 font-karla uppercase tracking-wider transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Back
        </button>
        <button
          type="submit"
          disabled={isProcessing || !stripe}
          className="px-6 py-3 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 font-karla uppercase tracking-wider transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isProcessing ? "Processing Payment..." : "Complete Payment"}
        </button>
      </div>
    </form>
  );

  return (
    <div className="bg-white rounded-lg border border-[#285DA6]/10 p-6 shadow-md">
      {currentStep === "info" && renderCustomerInfoForm()}
      {currentStep === "payment" && renderPaymentForm()}
    </div>
  );
};

export default CartCheckout;
