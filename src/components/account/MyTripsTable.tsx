import React, { useState } from "react";

interface Booking {
  id: string;
  hotel_name: string;
  room_type: string;
  room_config_name?: string;
  check_in_date: string;
  check_out_date: string;
  total_amount: number;
  currency_code: string;
  status: string;
  guest_name?: string;
  guest_email?: string;
  guest_phone?: string;
  payment_status?: string;
  check_in_time?: string;
  check_out_time?: string;
  number_of_guests?: number;
  updated_at?: string;
}

interface MyTripsTableProps {
  bookings: Booking[];
  loading: boolean;
  onBookingClick: (booking: Booking) => void;
  pagination: {
    currentPage: number;
    totalPages: number;
    itemsPerPage: number;
  };
  onPageChange: (page: number) => void;
}

const MyTripsTable: React.FC<MyTripsTableProps> = ({
  bookings,
  loading,
  onBookingClick,
  pagination,
  onPageChange,
}) => {
  const [sortBy, setSortBy] = useState<"date" | "amount" | "hotel">("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const formatDisplayDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        weekday: "short",
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    } catch (error) {
      return dateString;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      confirmed: { bg: "bg-green-100", text: "text-green-800", label: "Confirmed" },
      pending: { bg: "bg-yellow-100", text: "text-yellow-800", label: "Pending" },
      cancelled: { bg: "bg-red-100", text: "text-red-800", label: "Cancelled" },
      completed: { bg: "bg-blue-100", text: "text-blue-800", label: "Completed" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}
      >
        {config.label}
      </span>
    );
  };

  const sortedBookings = [...bookings].sort((a, b) => {
    let aValue, bValue;

    switch (sortBy) {
      case "date":
        aValue = new Date(a.check_in_date).getTime();
        bValue = new Date(b.check_in_date).getTime();
        break;
      case "amount":
        aValue = a.total_amount;
        bValue = b.total_amount;
        break;
      case "hotel":
        aValue = a.hotel_name.toLowerCase();
        bValue = b.hotel_name.toLowerCase();
        break;
      default:
        return 0;
    }

    if (sortOrder === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const handleSort = (column: "date" | "amount" | "hotel") => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("desc");
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#285DA6]"></div>
          <span className="ml-3 text-gray-600">Loading your trips...</span>
        </div>
      </div>
    );
  }

  if (bookings.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-gray-400"
          >
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No trips yet</h3>
        <p className="text-gray-500 mb-6">
          Start planning your perfect ski holiday today!
        </p>
        <a
          href="/search"
          className="inline-flex items-center px-6 py-3 bg-[#285DA6] text-white rounded-lg hover:bg-[#1A3A6E] transition-colors font-medium"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2"
          >
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
          Browse Hotels
        </a>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      {/* Header with sorting */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">My Trips</h2>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Sort by:</span>
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [column, order] = e.target.value.split("-");
                setSortBy(column as "date" | "amount" | "hotel");
                setSortOrder(order as "asc" | "desc");
              }}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-[#285DA6] focus:border-[#285DA6]"
            >
              <option value="date-desc">Newest First</option>
              <option value="date-asc">Oldest First</option>
              <option value="amount-desc">Highest Amount</option>
              <option value="amount-asc">Lowest Amount</option>
              <option value="hotel-asc">Hotel A-Z</option>
              <option value="hotel-desc">Hotel Z-A</option>
            </select>
          </div>
        </div>
      </div>

      {/* Desktop Table View */}
      <div className="hidden lg:block">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Hotel & Room
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Check-in
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Check-out
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Amount
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedBookings.map((booking) => (
              <tr
                key={booking.id}
                className="hover:bg-gray-50 transition-colors cursor-pointer"
                onClick={() => onBookingClick(booking)}
              >
                <td className="px-6 py-4">
                  <div>
                    <div className="font-medium text-gray-900">{booking.hotel_name}</div>
                    <div className="text-sm text-gray-500">
                      {booking.room_config_name || booking.room_type}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {formatDisplayDate(booking.check_in_date)}
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {formatDisplayDate(booking.check_out_date)}
                </td>
                <td className="px-6 py-4 text-sm font-medium text-gray-900">
                  {booking.currency_code.toUpperCase()} {booking.total_amount.toFixed(2)}
                </td>
                <td className="px-6 py-4">
                  {getStatusBadge(booking.status)}
                </td>
                <td className="px-6 py-4">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onBookingClick(booking);
                    }}
                    className="text-[#285DA6] hover:text-[#1A3A6E] font-medium text-sm"
                  >
                    View Details
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden divide-y divide-gray-200">
        {sortedBookings.map((booking) => (
          <div
            key={booking.id}
            onClick={() => onBookingClick(booking)}
            className="p-6 hover:bg-gray-50 transition-colors cursor-pointer"
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 mb-1">{booking.hotel_name}</h3>
                <p className="text-sm text-gray-500">{booking.room_config_name || booking.room_type}</p>
              </div>
              {getStatusBadge(booking.status)}
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">Check-in</p>
                <p className="font-medium">{formatDisplayDate(booking.check_in_date)}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">Check-out</p>
                <p className="font-medium">{formatDisplayDate(booking.check_out_date)}</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between pt-3 border-t border-gray-100">
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">Total Amount</p>
                <p className="font-medium text-gray-900">
                  {booking.currency_code.toUpperCase()} {booking.total_amount.toFixed(2)}
                </p>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onBookingClick(booking);
                }}
                className="px-4 py-2 bg-[#285DA6]/10 text-[#285DA6] rounded-lg hover:bg-[#285DA6]/20 transition-colors text-sm font-medium"
              >
                View Details
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Page {pagination.currentPage} of {pagination.totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => onPageChange(pagination.currentPage - 1)}
                disabled={pagination.currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              <button
                onClick={() => onPageChange(pagination.currentPage + 1)}
                disabled={pagination.currentPage === pagination.totalPages}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MyTripsTable;
