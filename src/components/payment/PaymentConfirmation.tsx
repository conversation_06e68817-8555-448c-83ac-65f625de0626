import React from "react";
import { Button } from "../ui/button";

interface PaymentConfirmationProps {
  bookingId: string;
  hotelName?: string;
  checkInDate: string;
  checkOutDate: string;
  amount: number;
  currency: string;
  onClose: () => void;
  // Additional room details
  roomName?: string;
  roomImage?: string;
  roomFeatures?: string[];
  hotelLocation?: string;
  adultCount?: number;
  childrenCount?: number;
  infantCount?: number;
  nightCount?: number;
  guestCount?: number;
  mealPlan?: string;
  mealPlanLabel?: string;
}

const PaymentConfirmation: React.FC<PaymentConfirmationProps> = ({
  bookingId,
  hotelName,
  checkInDate,
  checkOutDate,
  amount,
  currency,
  onClose,
  // Additional room details with defaults
  roomName = "Superior Room",
  roomImage = "",
  roomFeatures = ["Mountain View", "Free WiFi", "Breakfast Included"],
  hotelLocation = "",
  adultCount = 1,
  childrenCount = 0,
  infantCount = 0,
  nightCount = 8,
  guestCount = 1,
  mealPlan = "fb",
  mealPlanLabel = "Full Board",
}) => {
  return (
    <div className="text-center">
      <div className="mb-6">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8 text-green-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h3 className="text-xl font-bold mb-2">Payment Successful!</h3>
        <p className="text-foreground/70">
          Your booking has been confirmed and your payment has been processed.
        </p>
      </div>

      <div className="bg-background/50 p-4 rounded-lg mb-6 text-left">
        <h4 className="font-baskervville text-lg mb-4">Order Summary</h4>

        <div className="mb-4">
          <p className="text-sm text-foreground/70">Booking Reference</p>
          <p className="font-medium">{bookingId}</p>
        </div>

        {/* Selected Room Section */}
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <h5 className="text-[#285DA6] font-baskervville mb-2">Selected Room</h5>
          <div className="flex items-start gap-3">
            {roomImage ? (
              <img
                src={roomImage}
                alt={roomName}
                className="w-20 h-20 object-cover rounded-md"
              />
            ) : (
              <div className="w-20 h-20 bg-gray-200 rounded-md flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <circle cx="8.5" cy="8.5" r="1.5"></circle>
                  <polyline points="21 15 16 10 5 21"></polyline>
                </svg>
              </div>
            )}
            <div className="flex-1">
              <h6 className="font-medium">{roomName}</h6>
              <p className="text-sm text-gray-600">Up to {guestCount} guests</p>
              <div className="flex flex-wrap gap-1 mt-1">
                {roomFeatures.map((feature, index) => (
                  <span key={index} className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-karla bg-gray-100 text-gray-800">
                    {feature}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {hotelName && (
          <div className="mb-4">
            <h5 className="text-[#285DA6] font-baskervville mb-2">Hotel</h5>
            <p className="font-medium">{hotelName}</p>
            {hotelLocation && <p className="text-sm text-gray-600">{hotelLocation}</p>}
          </div>
        )}

        {/* Trip Details */}
        <h5 className="text-[#285DA6] font-baskervville mb-2">Your Trip Dates</h5>
        <div className="mb-4">
          <div className="flex justify-between mb-2 text-sm">
            <span>Check-in:</span>
            <span>{checkInDate}</span>
          </div>
          <div className="flex justify-between mb-2 text-sm">
            <span>Check-out:</span>
            <span>{checkOutDate}</span>
          </div>
          <div className="flex justify-between mb-2 text-sm">
            <span>Duration:</span>
            <span>{nightCount} nights</span>
          </div>
        </div>

        {/* Guest Details */}
        <h5 className="text-[#285DA6] font-baskervville mb-2">Guests</h5>
        <div className="mb-4">
          <div className="flex justify-between mb-2 text-sm">
            <span>Adults:</span>
            <span>{adultCount}</span>
          </div>
          {childrenCount > 0 && (
            <div className="flex justify-between mb-2 text-sm">
              <span>Children:</span>
              <span>{childrenCount}</span>
            </div>
          )}
          {infantCount > 0 && (
            <div className="flex justify-between mb-2 text-sm">
              <span>Infants:</span>
              <span>{infantCount}</span>
            </div>
          )}
          <div className="flex justify-between mb-2 text-sm">
            <span>Total Guests:</span>
            <span>{guestCount}</span>
          </div>
        </div>

        {/* Meal Plan */}
        <h5 className="text-[#285DA6] font-baskervville mb-2">Room Board</h5>
        <div className="mb-4">
          <div className="flex justify-between mb-2 text-sm">
            <span>Meal Plan:</span>
            <span>{mealPlanLabel}</span>
          </div>
        </div>

        {/* Price Breakdown */}
        <h5 className="text-[#285DA6] font-baskervville mb-2">Price Details</h5>
        <div className="border-t border-gray-200 pt-2">
          <div className="flex justify-between mb-2 text-sm">
            <span>Room Rate (per night)</span>
            <span>{currency} {new Intl.NumberFormat('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(amount / nightCount)}</span>
          </div>
          <div className="flex justify-between mb-2 text-sm">
            <span>Nights</span>
            <span>{nightCount}</span>
          </div>
          <div className="flex justify-between mb-2 text-sm">
            <span>Subtotal</span>
            <span>{currency} {new Intl.NumberFormat('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(amount * 0.9)}</span>
          </div>
          {/* Only show taxes if they exist */}
          {amount * 0.1 > 0 && (
            <div className="flex justify-between mb-2 text-sm">
              <span>Taxes & Fees</span>
              <span>{currency} {new Intl.NumberFormat('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(amount * 0.1)}</span>
            </div>
          )}
        </div>

        <div className="flex justify-between font-bold mt-2 pt-2 border-t border-gray-200">
          <div>
            <span>Total:</span>
            <p className="text-xs font-normal text-gray-500 mt-1">Inclusive of tax</p>
          </div>
          <span>
            {currency} {new Intl.NumberFormat('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(amount)}
          </span>
        </div>
        <div className="text-xs text-right mt-1 text-muted-foreground">
          All prices shown in {currency}
        </div>
      </div>
{/*
      <p className="text-sm text-foreground/70 mb-6">
        A confirmation email has been sent to your email address.
      </p> */}

      <Button
        onClick={onClose}
        className="bg-primary text-primary-foreground hover:bg-primary/90 px-6 py-3 rounded-full font-medium transition-colors"
      >
        Close
      </Button>
    </div>
  );
};

export default PaymentConfirmation;
