import React, { useState } from "react";
import { CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import { Button } from "../ui/button";

interface StripeCheckoutProps {
  clientSecret: string;
  amount: number;
  currency: string;
  onSuccess: () => void;
  onError: (error: string) => void;
  externalProcessing?: boolean;
}

const StripeCheckout: React.FC<StripeCheckoutProps> = ({
  clientSecret,
  amount,
  currency,
  onSuccess,
  onError,
  externalProcessing = false,
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [cardholderName, setCardholderName] = useState("");
  const [cardError, setCardError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      onError("Stripe has not been initialized");
      return;
    }

    if (!cardholderName) {
      onError("Please enter the cardholder name");
      return;
    }

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      onError("Card element not found");
      return;
    }

    setIsProcessing(true);

    try {
      // Create payment method
      const { error: pmError, paymentMethod } =
        await stripe.createPaymentMethod({
          type: "card",
          card: cardElement,
          billing_details: {
            name: cardholderName,
          },
        });

      if (pmError) {
        throw new Error(pmError.message);
      }

      // Confirm the payment
      const { error: confirmError } = await stripe.confirmCardPayment(
        clientSecret,
        {
          payment_method: paymentMethod.id,
        }
      );

      if (confirmError) {
        throw new Error(confirmError.message);
      }

      // We'll let the parent component handle the cart completion
      // This component only handles the Stripe payment confirmation

      // Clear the cart in localStorage
      try {
        localStorage.setItem("cart", JSON.stringify([]));
      } catch (error) {
        console.error("[STRIPE CHECKOUT] Error clearing cart:", error);
      }

      onSuccess();
    } catch (error) {
      console.error("[STRIPE CHECKOUT] Payment error:", error);
      onError(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCardChange = (event: any) => {
    setCardError(event.error ? event.error.message : null);
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label
            htmlFor="cardholderName"
            className="block text-sm font-medium mb-1"
          >
            Cardholder Name
          </label>
          <input
            id="cardholderName"
            type="text"
            value={cardholderName}
            onChange={(e) => setCardholderName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            placeholder="John Doe"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Card Information
          </label>
          <div className="border border-gray-300 rounded-md p-3">
            <CardElement
              options={{
                style: {
                  base: {
                    fontSize: "16px",
                    color: "#424770",
                    "::placeholder": {
                      color: "#aab7c4",
                    },
                  },
                  invalid: {
                    color: "#9e2146",
                  },
                },
              }}
              onChange={handleCardChange}
            />
          </div>
          {cardError && (
            <div className="text-red-500 text-sm mt-1">{cardError}</div>
          )}
        </div>

        <div className="pt-2">
          <Button
            type="submit"
            className="w-full bg-[#285DA6] text-white hover:bg-[#285DA6]/90 px-6 py-3 rounded-lg font-medium transition-colors"
            disabled={
              isProcessing || externalProcessing || !stripe || !elements
            }
          >
            {isProcessing || externalProcessing ? (
              <div className="flex items-center justify-center">
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </div>
            ) : (
              `Pay ${currency} ${amount.toFixed(2)}`
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default StripeCheckout;
