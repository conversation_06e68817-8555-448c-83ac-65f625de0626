.hotel-results-container {
  width: 100%;
  margin: 0 auto;
}

.hotel-results-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

/* Hotel card styles removed - using HotelCard component */

@media (max-width: 1400px) {
  .hotel-results-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1100px) {
  .hotel-results-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hotel-results-grid {
    grid-template-columns: 1fr;
  }
}
