/* Perplexity Search Screen Styles */

.perplexity-search-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  padding: 1rem 0;
}

/* Default View Styles */
.perplexity-default-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.5rem 1.5rem;
  max-width: 850px;
  margin: 0 auto;
  width: 100%;
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.4s cubic-bezier(0.25, 1, 0.5, 1),
    transform 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.7),
    rgba(255, 255, 255, 0.9)
  );
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05),
    0 1px 8px rgba(53, 102, 171, 0.06);
  border: 1px solid rgba(53, 102, 171, 0.08);
  backdrop-filter: blur(10px);
}

.perplexity-default-view.fade-out {
  opacity: 0;
  transform: translateY(-20px);
}

.perplexity-search-interface {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.perplexity-search-interface.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.perplexity-default-heading {
  font-family: "Baskervville", serif;
  font-size: 2.75rem;
  font-weight: 400;
  color: var(--foreground);
  margin-bottom: 2.5rem;
  text-align: center;
  letter-spacing: 0.02em;
  line-height: 1.2;
  position: relative;
}

.perplexity-default-heading::after {
  content: "";
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 2px;
  background: linear-gradient(to right, #3566ab, rgba(53, 102, 171, 0.3));
}

.perplexity-default-search-container {
  width: 100%;
  max-width: 700px;
  border: 1px solid rgba(53, 102, 171, 0.15);
  border-radius: 1.25rem;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.98),
    rgba(255, 255, 255, 0.95)
  );
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(53, 102, 171, 0.1);
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  backdrop-filter: blur(8px);
}

.perplexity-default-search-container:focus-within {
  border-color: rgba(53, 102, 171, 0.25);
  background: white;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1),
    0 4px 12px rgba(53, 102, 171, 0.15);
  transform: translateY(-3px) scale(1.01);
}

.perplexity-default-search-input {
  width: 100%;
  border: none;
  background: transparent;
  font-size: 1.125rem;
  line-height: 1.6;
  resize: none;
  padding: 0;
  max-height: 150px;
  min-height: 28px;
  font-family: "Baskervville", serif;
  margin-bottom: 1rem;
  color: #1b1b1b;
  letter-spacing: 0.01em;
}

.perplexity-default-search-input:focus {
  outline: none;
  border: none;
  box-shadow: none;
}

.perplexity-default-search-input::placeholder {
  color: rgba(27, 27, 27, 0.5);
  font-style: italic;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.perplexity-default-search-input:focus::placeholder {
  opacity: 0.5;
}

.perplexity-default-search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(53, 102, 171, 0.1);
  padding-top: 1rem;
  margin-top: 0.5rem;
}

.perplexity-search-toggle {
  display: flex;
  gap: 0.5rem;
}

.perplexity-toggle-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  background-color: transparent;
  border: none;
  color: var(--foreground-muted);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.perplexity-toggle-button:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.perplexity-toggle-button.active {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
}

.perplexity-action-icons {
  display: flex;
  gap: 0.5rem;
}

.perplexity-action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  background-color: transparent;
  border: none;
  color: var(--foreground-muted);
  cursor: pointer;
  transition: all 0.2s ease;
}

.perplexity-action-icon:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
  color: var(--foreground);
}

.perplexity-search-button-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 0.75rem;
  background: linear-gradient(135deg, #3566ab 0%, #285da6 100%);
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.15);
}

.perplexity-search-button-small:hover {
  background: linear-gradient(135deg, #3a6fbb 0%, #3566ab 100%);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.3),
    inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

.perplexity-search-button-small:active {
  transform: translateY(1px) scale(0.98);
  box-shadow: 0 1px 4px rgba(53, 102, 171, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

/* Thread Header */
.perplexity-thread-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
}

.perplexity-user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.perplexity-user-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: rgba(var(--primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
}

.perplexity-username {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--foreground);
}

.perplexity-thread-actions {
  display: flex;
  gap: 0.75rem;
}

.perplexity-action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  background-color: transparent;
  border: 1px solid rgba(var(--primary-rgb), 0.2);
  color: var(--foreground);
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.perplexity-action-button:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
  border-color: rgba(var(--primary-rgb), 0.3);
}

/* Search Filters */
.search-filters {
  width: 100%;
  margin-bottom: 1rem;
}

.search-filter-bar {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.98),
    rgba(255, 255, 255, 0.95)
  );
  border: 1px solid rgba(53, 102, 171, 0.12);
  border-radius: 1.25rem;
  padding: 1.25rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(53, 102, 171, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  backdrop-filter: blur(8px);
}

.search-filter-bar:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(53, 102, 171, 0.12);
  border-color: rgba(53, 102, 171, 0.2);
  transform: translateY(-2px);
}

@media (min-width: 768px) {
  .search-filter-bar {
    flex-direction: row;
    align-items: center;
    padding: 0.5rem;
  }
}

/* Search Filter */
.search-filter {
  position: relative;
  flex: 1;
}

.search-filter-button {
  width: 100%;
  text-align: left;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-filter-button:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.search-filter-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 0.25rem;
}

.search-filter-value {
  font-size: 0.875rem;
  color: var(--foreground);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Search Dropdown */
.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border-radius: 0.5rem;
  border: 1px solid rgba(var(--primary-rgb), 0.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 0.5rem;
  overflow: hidden;
}

.search-dropdown-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
  font-size: 0.875rem;
}

.search-dropdown-input:focus {
  outline: none;
}

.search-dropdown-suggestions {
  max-height: 200px;
  overflow-y: auto;
}

.search-dropdown-suggestion {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-dropdown-suggestion:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

/* Date Dropdown */
.date-dropdown {
  width: 300px;
}

.date-picker-container {
  padding: 1rem;
}

.date-picker-inputs {
  display: flex;
  gap: 1rem;
}

.date-picker-input {
  flex: 1;
}

.date-picker-input label {
  display: block;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.date-picker-input input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid rgba(var(--primary-rgb), 0.15);
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* Guests Dropdown */
.guests-dropdown {
  width: 300px;
  right: 0;
  left: auto;
}

.guest-selector {
  padding: 1rem;
}

.guest-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
}

.guest-type:last-child {
  border-bottom: none;
}

.guest-type-label {
  font-weight: 600;
  font-size: 0.875rem;
}

.guest-type-sublabel {
  font-size: 0.75rem;
  color: var(--foreground-muted);
}

.guest-counter {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.guest-counter-button {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(var(--primary-rgb), 0.3);
  border-radius: 50%;
  background: transparent;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.guest-counter-button:hover:not(:disabled) {
  background-color: rgba(var(--primary-rgb), 0.05);
  border-color: rgba(var(--primary-rgb), 0.5);
}

.guest-counter-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Perplexity Search Input */
.perplexity-search-input-container {
  display: flex;
  align-items: flex-end;
  width: 100%;
  border: 1px solid rgba(53, 102, 171, 0.15);
  border-radius: 1.25rem;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.98),
    rgba(255, 255, 255, 0.95)
  );
  backdrop-filter: blur(8px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(53, 102, 171, 0.1);
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
}

.perplexity-search-input-container:focus-within {
  border-color: rgba(53, 102, 171, 0.25);
  background: white;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1),
    0 4px 12px rgba(53, 102, 171, 0.15);
  transform: translateY(-3px) scale(1.01);
}

.perplexity-search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 1.125rem;
  line-height: 1.6;
  resize: none;
  padding: 0;
  max-height: 150px;
  min-height: 28px;
  font-family: "Baskervville", serif;
  color: #1b1b1b;
  letter-spacing: 0.01em;
}

.perplexity-search-input:focus {
  outline: none;
  border: none;
  box-shadow: none;
}

.perplexity-search-input::placeholder {
  color: rgba(27, 27, 27, 0.5);
  font-style: italic;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.perplexity-search-input:focus::placeholder {
  opacity: 0.5;
}

.perplexity-search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #3566ab 0%, #285da6 100%);
  color: white;
  border: none;
  margin-left: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.3),
    inset 0 1px 2px rgba(255, 255, 255, 0.25);
}

.perplexity-search-button:hover:not(:disabled) {
  transform: scale(1.08) translateY(-2px);
  background: linear-gradient(135deg, #3a6fbb 0%, #3566ab 100%);
  box-shadow: 0 8px 20px rgba(53, 102, 171, 0.4),
    inset 0 1px 3px rgba(255, 255, 255, 0.3);
}

.perplexity-search-button:active:not(:disabled) {
  transform: scale(0.98) translateY(1px);
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.25),
    inset 0 1px 1px rgba(255, 255, 255, 0.2);
}

.perplexity-search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: linear-gradient(135deg, #8ca7d3 0%, #6b8dc1 100%);
}

/* Search Spinner */
.search-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s cubic-bezier(0.5, 0.1, 0.5, 0.9) infinite;
  box-shadow: 0 0 10px rgba(53, 102, 171, 0.2);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Perplexity Empty State */
.perplexity-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 3rem 1rem;
  margin-top: 2rem;
}

.perplexity-empty-icon {
  margin-bottom: 1.5rem;
  color: var(--primary);
  opacity: 0.7;
}

.perplexity-empty-state h2 {
  font-family: "Baskervville", serif;
  font-size: 1.75rem;
  margin-bottom: 1rem;
  font-weight: 400;
}

.perplexity-empty-state p {
  font-size: 1rem;
  color: var(--foreground-muted);
  max-width: 600px;
  margin-bottom: 2rem;
}

.perplexity-example-queries {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  max-width: 700px;
}

.perplexity-example button {
  width: 100%;
  text-align: left;
  padding: 1rem 1.5rem;
  background-color: rgba(var(--primary-rgb), 0.05);
  border: 1px solid rgba(var(--primary-rgb), 0.1);
  border-radius: 0.75rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--foreground);
}

.perplexity-example button:hover {
  background-color: rgba(var(--primary-rgb), 0.1);
  border-color: rgba(var(--primary-rgb), 0.2);
  transform: translateY(-2px);
}

/* Perplexity Search Results */
.perplexity-search-results {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s cubic-bezier(0.25, 1, 0.5, 1),
    transform 0.6s cubic-bezier(0.25, 1, 0.5, 1);
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0.95)
  );
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06),
    0 1px 8px rgba(53, 102, 171, 0.08);
  border: 1px solid rgba(53, 102, 171, 0.1);
  backdrop-filter: blur(10px);
}

.perplexity-search-results.visible {
  opacity: 1;
  transform: translateY(0);
  animation: subtle-glow 3s infinite alternate;
}

@keyframes subtle-glow {
  from {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06),
      0 1px 8px rgba(53, 102, 171, 0.08);
  }
  to {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08),
      0 2px 12px rgba(53, 102, 171, 0.12);
  }
}

.perplexity-query {
  margin-bottom: 0.5rem;
}

.perplexity-query h2 {
  font-family: "Baskervville", serif;
  font-size: 2rem;
  font-weight: 400;
  color: var(--foreground);
  letter-spacing: 0.02em;
  position: relative;
  display: inline-block;
  margin-bottom: 0.5rem;
}

.perplexity-query h2::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(to right, #3566ab, rgba(53, 102, 171, 0.3));
}

/* Perplexity Tabs */
.perplexity-tabs {
  display: flex;
  gap: 1rem;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
  padding-bottom: 0.75rem;
  margin-bottom: 1.5rem;
}

.perplexity-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: transparent;
  border: none;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--foreground-muted);
  cursor: pointer;
  transition: all 0.3s ease;
}

.perplexity-tab:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.perplexity-tab.active {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
}

/* Perplexity Hotel Cards */
.perplexity-hotel-cards {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding: 0.5rem 0;
  margin-bottom: 1.5rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--primary-rgb), 0.3) transparent;
}

.perplexity-hotel-cards::-webkit-scrollbar {
  height: 6px;
}

.perplexity-hotel-cards::-webkit-scrollbar-track {
  background: transparent;
}

.perplexity-hotel-cards::-webkit-scrollbar-thumb {
  background-color: rgba(var(--primary-rgb), 0.3);
  border-radius: 6px;
}

.perplexity-hotel-card-wrapper {
  flex: 0 0 auto;
  width: 200px;
}

.perplexity-hotel-card-link {
  text-decoration: none;
  color: inherit;
}

/* Perplexity hotel card styles removed - using HotelCard component */

/* Perplexity Response */
.perplexity-response {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--foreground);
  font-family: "Baskervville", serif;
  letter-spacing: 0.01em;
}

.perplexity-response p {
  margin-bottom: 2rem;
}

.perplexity-hotel-list {
  margin: 2rem 0;
  padding-left: 2rem;
}

.perplexity-hotel-list li {
  margin-bottom: 2rem;
  position: relative;
  padding-left: 0.5rem;
  border-left: 2px solid rgba(53, 102, 171, 0.2);
  padding-bottom: 1rem;
}

.perplexity-hotel-list li:last-child {
  margin-bottom: 0;
}

.perplexity-hotel-list li::before {
  content: "";
  position: absolute;
  left: -6px;
  top: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3566ab 0%, #285da6 100%);
  box-shadow: 0 2px 4px rgba(53, 102, 171, 0.3);
}

.perplexity-hotel-list h3 {
  font-family: "Baskervville", serif;
  font-size: 1.4rem;
  margin-bottom: 0.75rem;
  font-weight: 500;
  color: #1b1b1b;
  letter-spacing: 0.02em;
}

.perplexity-price-info {
  font-weight: 600;
  color: #3566ab;
  margin-top: 0.75rem;
  font-family: "Karla", sans-serif;
  font-size: 1.1rem;
  display: inline-block;
  padding: 0.5rem 1rem;
  background: rgba(53, 102, 171, 0.08);
  border-radius: 0.5rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .perplexity-search-container {
    padding: 0 1rem;
  }

  .perplexity-default-heading {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .perplexity-default-search-container {
    max-width: 100%;
  }

  .perplexity-query h2 {
    font-size: 1.5rem;
  }

  .perplexity-query h2::after {
    width: 50px;
  }

  .perplexity-hotel-card-wrapper {
    width: 180px;
  }

  .perplexity-hotel-image {
    height: 132px;
  }

  .perplexity-search-toggle {
    gap: 0.25rem;
  }

  .perplexity-toggle-button {
    padding: 0.5rem;
  }

  .perplexity-toggle-button span {
    font-size: 0.75rem;
  }

  .perplexity-action-icons {
    gap: 0.25rem;
  }

  .perplexity-search-input-container {
    padding: 1rem 1.25rem;
  }

  .perplexity-search-button {
    width: 2.75rem;
    height: 2.75rem;
  }

  .perplexity-search-results {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .perplexity-default-heading {
    font-size: 1.75rem;
    margin-bottom: 1.25rem;
  }

  .perplexity-default-search-input {
    font-size: 0.9rem;
  }

  .perplexity-search-input {
    font-size: 1rem;
  }

  .perplexity-search-button {
    width: 2.5rem;
    height: 2.5rem;
    margin-left: 0.75rem;
  }

  .perplexity-empty-state h2 {
    font-size: 1.5rem;
  }

  .perplexity-hotel-card-wrapper {
    width: 160px;
  }

  .perplexity-hotel-image {
    height: 120px;
  }

  .perplexity-hotel-name {
    font-size: 0.85rem;
    padding: 0.75rem;
  }

  .perplexity-toggle-button {
    padding: 0.4rem;
  }

  .perplexity-toggle-button span {
    display: none;
  }

  .perplexity-action-icon,
  .perplexity-search-button-small {
    width: 1.75rem;
    height: 1.75rem;
  }

  .perplexity-search-input-container {
    padding: 0.875rem 1rem;
  }

  .perplexity-search-results {
    padding: 1.25rem;
    gap: 1.5rem;
  }

  .perplexity-response {
    font-size: 1rem;
  }

  .perplexity-hotel-list h3 {
    font-size: 1.25rem;
  }

  .perplexity-price-info {
    font-size: 1rem;
    padding: 0.4rem 0.75rem;
  }
}
