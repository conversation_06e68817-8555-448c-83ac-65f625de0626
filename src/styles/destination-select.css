.destination-select-container {
  margin-bottom: 1.5rem;
}

.destination-select-container .label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Style for the dropdown */
.destination-select-container button {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 0.375rem;
  background-color: var(--background-color, white);
  transition: all 0.2s ease;
}

.destination-select-container button:hover {
  border-color: var(--primary-color, #285DA6);
}

.destination-select-container button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(40, 93, 166, 0.2);
  border-color: var(--primary-color, #285DA6);
}

/* Dropdown list */
.destination-select-container ul {
  max-height: 15rem;
  overflow-y: auto;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.destination-select-container li {
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.destination-select-container li:hover {
  background-color: rgba(40, 93, 166, 0.1);
}

.destination-select-container li[aria-selected="true"] {
  background-color: rgba(40, 93, 166, 0.2);
  color: var(--primary-color, #285DA6);
}

/* Responsive styles */
@media (max-width: 768px) {
  .destination-select-container {
    margin-bottom: 1rem;
  }
}
