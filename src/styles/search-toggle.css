.search-toggle-container {
  display: flex;
  justify-content: center;
  width: 100%;
  position: absolute;
  top: -36px; /* Moved higher from -28px */
  z-index: 10;
}

.search-toggle-container-header {
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
  padding: 8px 0;
}

.search-toggle-wrapper-header {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 8px 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 150;
  pointer-events: auto; /* Ensure clicks are captured */
  user-select: none; /* Prevent text selection */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.search-toggle-wrapper {
  position: relative;
  display: flex;
  background-color: #f5f5f5;
  border-radius: 24px;
  padding: 3px;
  width: 320px; /* Increased width to accommodate longer text */
  height: 40px; /* Increased height */
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12); /* Enhanced shadow */
  border: 1px solid rgba(0, 0, 0, 0.05); /* Added subtle border */
}

.toggle-option {
  flex: 1;
  border: none;
  background: transparent;
  padding: 6px 16px;
  font-size: 14px; /* Increased font size */
  font-weight: 600; /* Increased font weight */
  color: #666;
  z-index: 5; /* Increased z-index to ensure clickability */
  cursor: pointer;
  transition: color 0.3s ease;
  border-radius: 20px;
  font-family: "Karla", sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px; /* Increased gap */
  position: relative; /* Added to ensure z-index works */
}

.toggle-option.active {
  color: #fff;
}

.ai-option {
  display: flex;
  align-items: center;
  gap: 6px;
}

.sparkle-icon {
  color: #ffd700;
}

.toggle-slider {
  position: absolute;
  top: 3px;
  left: 3px;
  width: calc(50% - 3px);
  height: calc(100% - 6px);
  background: linear-gradient(135deg, #3566ab, #4b7bc2);
  border-radius: 20px;
  transition: transform 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.4); /* Enhanced shadow */
  z-index: 1; /* Ensure it's below the buttons */
  pointer-events: none; /* Make it non-interactive so clicks pass through to buttons */
}

.toggle-slider.ai-active {
  transform: translateX(0);
}

.toggle-slider:not(.ai-active) {
  transform: translateX(100%);
}
