/* Premium Search Bar Styling */

/* Layered backdrop blur effect */
.search-bar-premium {
  backdrop-filter: blur(8px);
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  transition: all 0.3s ease;
  overflow: hidden;
}

/* Premium border styling */
.search-bar-premium-border {
  border: 1px solid rgba(var(--primary-rgb), 0.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* Enhanced hover states */
.search-bar-premium:hover {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
  box-shadow: 0 8px 24px rgba(var(--primary-rgb), 0.08);
  border-color: rgba(var(--primary-rgb), 0.25);
  transform: translateY(-1px);
}

/* Input field premium styling */
.search-field-premium {
  border: none;
  background-color: transparent;
  transition: all 0.3s ease;
}

.search-field-premium:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Button premium styling */
.search-button-premium {
  background: linear-gradient(135deg, #285DA6 0%, rgba(40, 93, 166, 0.85) 100%);
  box-shadow: 0 2px 8px rgba(40, 93, 166, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.15);
  border: none;
  margin-left: 0.5rem;
  transition: all 0.3s ease;
}

.search-button-premium:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(40, 93, 166, 0.35), inset 0 1px 2px rgba(255, 255, 255, 0.25);
  filter: brightness(1.05);
}

/* Toggle container premium styling */
.toggle-container-premium {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(8px);
  border: 1px solid rgba(var(--primary-rgb), 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* Concierge input styling */
.concierge-input {
  border: none;
  outline: none !important;
  box-shadow: none !important;
  caret-color: var(--primary);
  caret-width: 1px;
  font-size: 0.875rem;
  padding: 0;
  width: 100%;
}

.toggle-container-premium:hover {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
  border-color: rgba(var(--primary-rgb), 0.25);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.08);
}

/* Date Range Picker Styling */
.date-range-start {
  position: relative;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.25);
}

.date-range-end {
  position: relative;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.25);
}

.date-range-middle {
  position: relative;
  background-color: rgba(var(--primary-rgb), 0.1);
  transition: all 0.3s ease;
}

.date-range-middle:hover {
  background-color: rgba(var(--primary-rgb), 0.2);
  transform: scale(1.05);
}
