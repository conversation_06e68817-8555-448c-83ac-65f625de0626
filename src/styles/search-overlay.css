/* Search Overlay Styles */
.tab-content {
  display: none;
}

.tab-content.active {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* Clean search overlay styling */
.search-overlay-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-overlay-content {
  width: 100%;
  text-align: center;
  /* No need for padding or margins as container-custom handles this */
}

.search-overlay-title {
  font-size: 2.5rem;
  font-weight: 400;
  margin-bottom: 1rem;
  line-height: 1.2;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  font-family: "Baskervville", serif;
  color: var(--foreground);
}

.search-overlay-subtitle {
  font-size: 1.125rem;
  font-weight: 400;
  margin-bottom: 2rem;
  color: var(--foreground);
  opacity: 0.8;
  max-width: 650px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  font-family: "Baskervville", serif;
}

.luxury-accent {
  color: #3566ab;
  position: relative;
  display: inline-block;
}

/* Search container styling for the overlay */
#search-overlay .search-container {
  max-width: 750px;
  width: 100%;
  margin: 0 auto 2rem;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
}

#search-overlay .ai-search-container {
  background-color: white;
  border-radius: 1.5rem;
  padding: 0.75rem 1.5rem;
  border: 1px solid rgba(53, 102, 171, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  transform-origin: center;
}

#search-overlay .ai-search-container:hover {
  border-color: rgba(53, 102, 171, 0.3);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px) scale(1.01);
}

#search-overlay .ai-search-container:focus-within {
  border: 1px solid rgba(53, 102, 171, 0.4);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  transform: translateY(-4px) scale(1.03);
  padding: 1rem 1.75rem;
}

#search-overlay .ai-search-input {
  color: var(--foreground);
}

#search-overlay .search-suggestions {
  margin-top: 1.5rem;
  align-items: flex-start;
}

#search-overlay .suggestion-tabs {
  justify-content: flex-start;
}

#search-overlay .suggestion-terms {
  justify-content: flex-start;
}

#search-overlay .suggestion-tab {
  background-color: rgba(53, 102, 171, 0.1);
  border: 1px solid rgba(53, 102, 171, 0.2);
  color: var(--foreground);
}

#search-overlay .active-tab {
  background-color: rgba(53, 102, 171, 0.8);
  color: white;
}

#search-overlay .suggestion-term {
  background-color: rgba(53, 102, 171, 0.1);
  border: 1px solid rgba(53, 102, 171, 0.2);
  color: var(--foreground);
}

#search-overlay .suggestion-term:hover {
  background-color: rgba(53, 102, 171, 0.2);
  border-color: rgba(53, 102, 171, 0.3);
}

/* Ensure the options container is visible and properly styled */
#options-container {
  display: none;
}

#options-container.hidden {
  display: none;
}

#options-container:not(.hidden) {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Chip selection styles */
#search-overlay .chip-option {
  background-color: rgba(53, 102, 171, 0.1);
  border: 1px solid rgba(53, 102, 171, 0.2);
  color: var(--foreground);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

#search-overlay .chip-option:hover {
  background-color: rgba(53, 102, 171, 0.2);
  border-color: rgba(53, 102, 171, 0.3);
}

#search-overlay .chip-option.chip-selected {
  background-color: rgba(53, 102, 171, 0.8);
  border-color: rgba(53, 102, 171, 0.4);
  color: white;
  box-shadow: 0 0 10px rgba(53, 102, 171, 0.3);
}

#search-overlay .chip-icon {
  font-size: 1rem;
}

/* Options container styling */
#search-overlay .options-container {
  background-color: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-top: 1rem;
  border: 1px solid rgba(53, 102, 171, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

#search-overlay .options-header h3 {
  color: var(--foreground);
  font-size: 1.25rem;
  margin-bottom: 1rem;
  font-family: "Baskervville", serif;
  text-align: center;
}

#search-overlay .options-section {
  margin-bottom: 1.5rem;
}

#search-overlay .options-section h4 {
  color: var(--foreground);
  font-size: 1rem;
  margin-bottom: 0.75rem;
  font-family: "Karla", sans-serif;
  letter-spacing: 0.05em;
}

#search-overlay .chip-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

#search-overlay .options-footer {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

#search-overlay .close-options-button {
  background-color: rgba(53, 102, 171, 0.1);
  color: var(--foreground);
  border: 1px solid rgba(53, 102, 171, 0.2);
  padding: 0.5rem 1.5rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: "Karla", sans-serif;
}

#search-overlay .close-options-button:hover {
  background-color: rgba(53, 102, 171, 0.2);
  border-color: rgba(53, 102, 171, 0.3);
}
