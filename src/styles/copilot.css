/* CopilotKit custom styles */

.copilot-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Full height for AI search page */
.ai-search-container .copilot-wrapper {
  height: 100vh;
}

/* Increase font size for chat messages */
.copilotkit-chat-message-content {
  font-size: 1.1rem !important;
}

/* Remove fixed height constraints */
.copilotkit-chat-container {
  height: 100% !important;
  min-height: 600px !important;
  padding-top: 1rem !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure the chat interface takes full height */
.copilotkit-chat {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Style the chat input area */
.copilotkit-chat-input-container {
  padding: 1rem !important;
  border-top: 1px solid #e5e7eb;
  display: flex !important;
  visibility: visible !important;
}

/* Style the message container */
.copilotkit-chat-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex !important;
  flex-direction: column !important;
  visibility: visible !important;
}

/* Fix for CopilotKit UI visibility */
.copilotkit-chat-input {
  display: flex !important;
  visibility: visible !important;
}

.copilotkit-chat-input-textarea {
  display: block !important;
  visibility: visible !important;
}

.copilotkit-chat-input-button {
  display: flex !important;
  visibility: visible !important;
}

/* Ensure the AI search container is visible */
.container.overflow-hidden.relative.rounded-lg.h-\[80vh\].mt-16 {
  overflow: visible !important;
  z-index: 10;
}

/* Mobile Responsive Improvements */
@media (max-width: 768px) {
  .copilotkit-chat-message-content {
    font-size: 0.95rem !important;
  }

  .copilotkit-chat-container {
    min-height: 500px !important;
    padding-top: 0.5rem !important;
  }

  .copilotkit-chat-input-container {
    padding: 0.75rem !important;
  }

  .copilotkit-chat-messages-container {
    padding: 0.5rem !important;
  }
}

@media (max-width: 480px) {
  .copilotkit-chat-message-content {
    font-size: 0.9rem !important;
  }

  .copilotkit-chat-container {
    min-height: 400px !important;
  }

  .copilotkit-chat-input-container {
    padding: 0.5rem !important;
  }

  .copilotkit-chat-messages-container {
    padding: 0.25rem !important;
  }
}
