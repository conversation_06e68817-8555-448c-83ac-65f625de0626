/* Web Stories Styles */
.web-stories-container {
  position: relative;
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0;
  overflow: hidden;
}

.web-stories-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 0 1rem;
}

.web-stories-nav-container {
  display: flex;
  gap: 0.5rem;
}

.web-stories-nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(40, 93, 166, 0.1) 0%, rgba(40, 93, 166, 0.2) 100%);
  border: 1px solid rgba(40, 93, 166, 0.2);
  color: #285DA6;
  cursor: pointer;
  transition: all 0.3s ease;
}

.web-stories-nav-button:hover {
  background: linear-gradient(135deg, rgba(40, 93, 166, 0.2) 0%, rgba(40, 93, 166, 0.3) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 93, 166, 0.15);
}

.web-stories-nav-button:active {
  transform: translateY(0);
}

.web-stories-track {
  position: relative;
  display: flex;
  width: 100%;
  height: 500px;
  overflow: visible;
}

.web-story {
  position: absolute;
  width: 100%;
  height: 100%;
  transition: all 0.6s cubic-bezier(0.25, 1, 0.5, 1);
  will-change: transform, opacity;
}

.web-story-active {
  z-index: 10;
}

.web-story-link {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: white;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.web-story-link:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(40, 93, 166, 0.2);
}

.web-story-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.web-story-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.7s ease;
}

.web-story-link:hover .web-story-image {
  transform: scale(1.05);
}

.web-story-gradient-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, transparent 100%);
  z-index: 1;
}

.web-story-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2rem;
  z-index: 2;
}

.web-story-content-title {
  font-family: 'Baskervville', serif;
  font-size: 1.75rem;
  font-weight: 400;
  margin: 0 0 0.5rem 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.web-story-content-subtitle {
  font-family: 'Karla', sans-serif;
  font-size: 1rem;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  max-width: 80%;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.web-stories-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.web-stories-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(40, 93, 166, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.web-stories-indicator-active {
  width: 24px;
  border-radius: 4px;
  background-color: #285DA6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .web-stories-track {
    height: 400px;
  }

  .web-stories-title {
    font-size: 1.5rem;
  }

  .web-story-content-title {
    font-size: 1.5rem;
  }

  .web-story-content-subtitle {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .web-stories-track {
    height: 350px;
  }

  .web-stories-title {
    font-size: 1.25rem;
  }

  .web-story-content {
    padding: 1.5rem;
  }

  .web-story-content-title {
    font-size: 1.25rem;
  }

  .web-story-content-subtitle {
    font-size: 0.75rem;
    max-width: 100%;
  }
}

/* Premium luxury enhancements */
.web-story-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  z-index: 3;
  pointer-events: none;
}

.web-story-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #285DA6, rgba(40, 93, 166, 0.5));
  z-index: 4;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.web-story-link:hover::after {
  opacity: 1;
}
