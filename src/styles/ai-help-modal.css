/* AI Help Modal Overlay */
.ai-help-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999; /* Very high z-index to ensure it's above everything */
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.ai-help-modal-overlay.visible {
  opacity: 1;
  visibility: visible;
}

/* Modal Container - Center popup */
.ai-help-modal-container {
  position: relative;
  width: 90%;
  max-width: 670px;
  max-height: 90vh;
  background-color: white;
  overflow-y: auto;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  transform: scale(0.95);
  opacity: 0;
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
  border-radius: 12px;
}

.ai-help-modal-overlay.visible .ai-help-modal-container {
  transform: scale(1);
  opacity: 1;
}

/* Close button */
.ai-help-modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  color: #666;
  font-size: 18px;
  transition: background-color 0.2s ease, color 0.2s ease;
  z-index: 10;
}

.ai-help-modal-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

/* Modal Content */
.ai-help-modal-content {
  padding: 25px;
  overflow-y: auto;
}

/* Modal Header */
.ai-help-modal-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.ai-help-modal-title {
  font-family: 'Baskervville', serif;
  font-size: 22px;
  color: #285DA6;
  margin-bottom: 8px;
}

.ai-help-modal-subtitle {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* Modal Sections */
.ai-help-modal-section {
  margin-bottom: 20px;
}

.ai-help-modal-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.ai-help-modal-section-content {
  font-size: 14px;
  color: #555;
  line-height: 1.5;
}

/* Example Prompts */
.ai-help-example-prompts {
  margin-top: 10px;
}

.ai-help-example-prompt {
  background-color: #f8f9fa;
  border-left: 3px solid #285DA6;
  padding: 10px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.ai-help-example-prompt:hover {
  background-color: #eef1f5;
  transform: translateX(2px);
}

/* Pro Tips */
.ai-help-pro-tips {
  background-color: #f0f7ff;
  border-radius: 6px;
  padding: 15px;
  margin-top: 15px;
}

.ai-help-pro-tips-title {
  font-weight: 600;
  color: #285DA6;
  margin-bottom: 8px;
  font-size: 14px;
}

.ai-help-pro-tips-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.ai-help-pro-tips-list li {
  position: relative;
  padding-left: 20px;
  margin-bottom: 6px;
  line-height: 1.4;
  font-size: 13px;
}

.ai-help-pro-tips-list li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #285DA6;
  font-weight: bold;
}

/* Try Example Button */
.ai-help-try-example-button {
  display: block;
  width: 100%;
  padding: 10px 16px;
  background-color: #285DA6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 20px;
  text-align: center;
}

.ai-help-try-example-button:hover {
  background-color: #1e4a8d;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .ai-help-modal-container {
    width: 90%;
  }

  .ai-help-modal-content {
    padding: 30px 20px;
  }

  .ai-help-modal-title {
    font-size: 24px;
  }

  .ai-help-modal-section-title {
    font-size: 18px;
  }
}
