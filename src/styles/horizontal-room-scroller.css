/* Horizontal Room Scroller Styles */

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Animation for slide-in from right */
@keyframes slide-in-right {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out forwards;
}

/* Animation for fade-in */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.25s ease-out forwards;
}

/* Smooth scrolling for the container */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* Hover effect for room cards */
.room-card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.room-card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .scroll-container {
    scroll-snap-type: x mandatory;
  }

  .scroll-item {
    scroll-snap-align: start;
  }
}

/* Scroll buttons hover effects */
.scroll-button {
  opacity: 0.8;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.scroll-button:hover {
  opacity: 1;
  transform: scale(1.05);
}

/* Modal backdrop blur effect */
.modal-backdrop {
  backdrop-filter: blur(4px);
}

/* Thumbnail gallery hover effect */
.thumbnail-hover {
  transition: transform 0.2s ease;
}

.thumbnail-hover:hover {
  transform: scale(1.05);
}

/* Price animation */
@keyframes price-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.price-pulse {
  animation: price-pulse 2s infinite;
}

/* Select button hover effect */
.select-button {
  transition: all 0.3s ease;
}

.select-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.3);
}

.select-button:active {
  transform: translateY(0);
}
