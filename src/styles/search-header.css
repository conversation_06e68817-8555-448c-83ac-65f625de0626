.search-header {
  width: 100%;
  padding: 16px 0;
  border-bottom: 1px solid #ebebeb;
  margin-bottom: 24px;
}

.search-container {
  max-width: 848px;
  margin: 0 auto;
  background-color: #f7f7f7;
  border-radius: 32px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.05);
}

.search-mode-toggle {
  display: flex;
  padding: 8px;
  border-bottom: 1px solid #ebebeb;
}

.toggle-button {
  flex: 1;
  padding: 8px 16px;
  background: transparent;
  border: none;
  border-radius: 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.toggle-button.active {
  background-color: white;
  font-weight: 600;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

.search-fields {
  display: flex;
  align-items: center;
  height: 64px;
}

.search-field {
  flex: 1;
  height: 100%;
  padding: 8px 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.search-field:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 25%;
  height: 50%;
  width: 1px;
  background-color: #ebebeb;
}

.search-field label {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
}

.search-field input {
  border: none;
  background: transparent;
  font-size: 14px;
  width: 100%;
  outline: none;
  padding: 0;
}

.search-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #3566ab;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.search-button:hover {
  transform: scale(1.05);
}

.search-icon {
  width: 18px;
  height: 18px;
  color: white;
}

@media (max-width: 768px) {
  .search-fields {
    flex-direction: column;
    height: auto;
  }

  .search-field {
    width: 100%;
    padding: 12px 16px;
  }

  .search-field:not(:last-child)::after {
    display: none;
  }

  .search-button {
    margin: 12px auto;
  }
}
