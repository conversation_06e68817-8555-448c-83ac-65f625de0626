/* AI Search Mobile Responsiveness and Typography Improvements */

/* Mobile Container Adjustments */
.ai-search-mobile-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .ai-search-mobile-container {
    height: 85vh !important;
    margin-top: 0.5rem !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    border-radius: 0.75rem !important;
  }
}

@media (max-width: 480px) {
  .ai-search-mobile-container {
    height: 88vh !important;
    margin-top: 0.25rem !important;
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
    border-radius: 0.5rem !important;
  }
}

/* Chat Interface Mobile Improvements */
.ai-search-chat-interface {
  font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
    sans-serif !important;
}

/* COMPREHENSIVE FIRST MESSAGE VISIBILITY FIX FOR AI SEARCH */

/* Override all conflicting container padding */
.ai-search-mobile-container .copilotkit-chat-messages-container,
.ai-search-mobile-container .copilotKitMessagesContainer,
.ai-search-mobile-container div[class*="copilotkit-chat-messages-container"],
.ai-search-mobile-container div[class*="copilotKitMessagesContainer"] {
  padding: 0 !important;
  padding-bottom: 1rem !important;
  overflow-y: auto !important;
  scroll-behavior: smooth !important;
  display: flex !important;
  flex-direction: column !important;
  /* Ensure container starts at top for first message visibility */
  scroll-snap-type: y mandatory !important;
  scroll-padding-top: 0 !important;
}

/* Set minimal padding on messages element for first message visibility */
.ai-search-mobile-container .copilotkit-chat-messages,
.ai-search-mobile-container .copilotKitChatMessages,
.ai-search-mobile-container div[class*="copilotkit-chat-messages"],
.ai-search-mobile-container div[class*="copilotKitChatMessages"] {
  padding: 0.5rem !important;
  padding-top: 0.5rem !important;
  overflow-y: visible !important;
  scroll-behavior: smooth !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 1rem !important;
}

/* Completely reset first message positioning and spacing */
.ai-search-mobile-container .copilotkit-chat-messages > div:first-child,
.ai-search-mobile-container .copilotKitChatMessages > div:first-child,
.ai-search-mobile-container
  div[class*="copilotkit-chat-messages"]
  > div:first-child,
.ai-search-mobile-container
  div[class*="copilotKitChatMessages"]
  > div:first-child,
.ai-search-mobile-container .copilotkit-chat-messages > div:first-of-type,
.ai-search-mobile-container .copilotKitChatMessages > div:first-of-type,
.ai-search-mobile-container
  div[class*="copilotkit-chat-messages"]
  > div:first-of-type,
.ai-search-mobile-container
  div[class*="copilotKitChatMessages"]
  > div:first-of-type {
  position: static !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  transform: none !important;
  z-index: auto !important;
}

@media (max-width: 768px) {
  .ai-search-chat-interface {
    font-size: 0.9rem !important;
  }

  /* Maintain first message visibility on tablet */
  .ai-search-mobile-container .copilotkit-chat-messages,
  .ai-search-mobile-container .copilotKitChatMessages,
  .ai-search-mobile-container div[class*="copilotkit-chat-messages"],
  .ai-search-mobile-container div[class*="copilotKitChatMessages"] {
    padding: 0.25rem !important;
    padding-top: 0.25rem !important;
  }
}

@media (max-width: 480px) {
  /* Maintain first message visibility on mobile */
  .ai-search-mobile-container .copilotkit-chat-messages,
  .ai-search-mobile-container .copilotKitChatMessages,
  .ai-search-mobile-container div[class*="copilotkit-chat-messages"],
  .ai-search-mobile-container div[class*="copilotKitChatMessages"] {
    padding: 0.25rem !important;
    padding-top: 0.25rem !important;
  }
}

/* Markdown Content Typography Fixes */
.ai-markdown-content {
  font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
    sans-serif !important;
  line-height: 1.6 !important;
  color: var(--foreground) !important;
}

.ai-markdown-content p {
  font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
    sans-serif !important;
  font-size: 1rem !important;
  line-height: 1.6 !important;
  margin: 0.75rem 0 !important;
  color: var(--foreground) !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

.ai-markdown-content h1,
.ai-markdown-content h2,
.ai-markdown-content h3,
.ai-markdown-content h4,
.ai-markdown-content h5,
.ai-markdown-content h6 {
  font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
    sans-serif !important;
  font-weight: 600 !important;
  color: var(--foreground) !important;
  margin: 1.5rem 0 1rem 0 !important;
  line-height: 1.3 !important;
}

.ai-markdown-content h1 {
  font-size: 1.5rem !important;
}

.ai-markdown-content h2 {
  font-size: 1.375rem !important;
}

.ai-markdown-content h3 {
  font-size: 1.25rem !important;
}

.ai-markdown-content h4,
.ai-markdown-content h5,
.ai-markdown-content h6 {
  font-size: 1.125rem !important;
}

.ai-markdown-content ul,
.ai-markdown-content ol {
  font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
    sans-serif !important;
  font-size: 1rem !important;
  line-height: 1.6 !important;
  margin: 0.75rem 0 !important;
  padding-left: 1.5rem !important;
}

.ai-markdown-content li {
  font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
    sans-serif !important;
  font-size: 1rem !important;
  line-height: 1.6 !important;
  margin: 0.25rem 0 !important;
  color: var(--foreground) !important;
}

.ai-markdown-content strong,
.ai-markdown-content b {
  font-weight: 600 !important;
  color: var(--foreground) !important;
}

.ai-markdown-content em,
.ai-markdown-content i {
  font-style: italic !important;
  color: var(--foreground) !important;
}

.ai-markdown-content code {
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono",
    "Source Code Pro", monospace !important;
  font-size: 0.9rem !important;
  background-color: var(--muted) !important;
  padding: 0.125rem 0.25rem !important;
  border-radius: 0.25rem !important;
  color: var(--foreground) !important;
}

.ai-markdown-content pre {
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono",
    "Source Code Pro", monospace !important;
  font-size: 0.875rem !important;
  background-color: var(--muted) !important;
  padding: 1rem !important;
  border-radius: 0.5rem !important;
  overflow-x: auto !important;
  margin: 1rem 0 !important;
  color: var(--foreground) !important;
}

.ai-markdown-content blockquote {
  border-left: 4px solid var(--border) !important;
  padding-left: 1rem !important;
  margin: 1rem 0 !important;
  font-style: italic !important;
  color: var(--muted-foreground) !important;
}

/* Mobile Typography Adjustments */
@media (max-width: 768px) {
  .ai-markdown-content p {
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
    margin: 0.5rem 0 !important;
  }

  .ai-markdown-content h1 {
    font-size: 1.25rem !important;
  }

  .ai-markdown-content h2 {
    font-size: 1.125rem !important;
  }

  .ai-markdown-content h3 {
    font-size: 1rem !important;
  }

  .ai-markdown-content h4,
  .ai-markdown-content h5,
  .ai-markdown-content h6 {
    font-size: 0.95rem !important;
  }

  .ai-markdown-content ul,
  .ai-markdown-content ol {
    font-size: 0.9rem !important;
    padding-left: 1.25rem !important;
  }

  .ai-markdown-content li {
    font-size: 0.9rem !important;
  }

  .ai-markdown-content code {
    font-size: 0.8rem !important;
  }

  .ai-markdown-content pre {
    font-size: 0.8rem !important;
    padding: 0.75rem !important;
  }
}

@media (max-width: 480px) {
  .ai-markdown-content p {
    font-size: 0.85rem !important;
    line-height: 1.4 !important;
  }

  .ai-markdown-content h1 {
    font-size: 1.125rem !important;
  }

  .ai-markdown-content h2 {
    font-size: 1rem !important;
  }

  .ai-markdown-content h3,
  .ai-markdown-content h4,
  .ai-markdown-content h5,
  .ai-markdown-content h6 {
    font-size: 0.9rem !important;
  }

  .ai-markdown-content ul,
  .ai-markdown-content ol,
  .ai-markdown-content li {
    font-size: 0.85rem !important;
  }
}

/* Message Content Mobile Improvements */
.ai-message-content {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
}

@media (max-width: 768px) {
  .ai-message-content {
    max-width: 100% !important;
    padding: 0.75rem !important;
  }
}

@media (max-width: 480px) {
  .ai-message-content {
    padding: 0.5rem !important;
    border-radius: 0.5rem !important;
  }
}

/* Touch Improvements for Mobile */
.suggestion-chip {
  touch-action: manipulation !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* General touch-manipulation for interactive elements */
button,
.suggestion-chip,
.ai-search-hotel-card,
[role="button"] {
  touch-action: manipulation !important;
}

@media (max-width: 768px) {
  .suggestion-chip {
    min-height: 44px !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
}

/* CopilotKit Mobile Overrides */
@media (max-width: 768px) {
  .copilotkit-chat-container {
    min-height: 550px !important;
    padding-top: 0.5rem !important;
  }

  .copilotkit-chat-input-container {
    padding: 0.75rem !important;
  }

  .copilotkit-chat-messages-container {
    padding: 0.5rem !important;
  }

  /* Override global rules that conflict with AI search first message visibility */
  .copilotkit-chat-messages,
  .copilotKitChatMessages,
  div[class*="copilotkit-chat-messages"],
  div[class*="copilotKitChatMessages"] {
    padding-top: 0.75rem !important;
  }

  /* But prioritize AI search container rules */
  .ai-search-mobile-container .copilotkit-chat-messages,
  .ai-search-mobile-container .copilotKitChatMessages,
  .ai-search-mobile-container div[class*="copilotkit-chat-messages"],
  .ai-search-mobile-container div[class*="copilotKitChatMessages"] {
    padding: 0.25rem !important;
    padding-top: 0.25rem !important;
  }
}

@media (max-width: 480px) {
  .copilotkit-chat-container {
    min-height: 450px !important;
  }

  .copilotkit-chat-input-container {
    padding: 0.5rem !important;
  }

  .copilotkit-chat-messages-container {
    padding: 0.25rem !important;
  }

  /* Override global rules that conflict with AI search first message visibility */
  .copilotkit-chat-messages,
  .copilotKitChatMessages,
  div[class*="copilotkit-chat-messages"],
  div[class*="copilotKitChatMessages"] {
    padding-top: 0.5rem !important;
  }

  /* But prioritize AI search container rules */
  .ai-search-mobile-container .copilotkit-chat-messages,
  .ai-search-mobile-container .copilotKitChatMessages,
  .ai-search-mobile-container div[class*="copilotkit-chat-messages"],
  .ai-search-mobile-container div[class*="copilotKitChatMessages"] {
    padding: 0.25rem !important;
    padding-top: 0.25rem !important;
  }
}

/* Hotel Card Mobile Improvements */
.ai-search-hotel-card {
  transition: transform 0.2s ease !important;
}

@media (max-width: 768px) {
  .ai-search-hotel-card {
    margin-bottom: 0.75rem !important;
  }

  .ai-search-hotel-card .group:hover {
    transform: none !important;
  }

  .ai-search-hotel-card img {
    transition: none !important;
  }

  .ai-search-hotel-card .group-hover\:scale-110:hover {
    transform: none !important;
  }
}

/* Hotel List Mobile Layout */
@media (max-width: 768px) {
  .chat-hotel-list-container {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .chat-hotel-list-container > div {
    width: 100% !important;
    max-width: 100% !important;
  }
}

@media (max-width: 480px) {
  .chat-hotel-list-container {
    gap: 0.5rem !important;
  }
}

/* Destination List Mobile Layout */
@media (max-width: 768px) {
  .chat-destination-list-container {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .chat-destination-list-container > div {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* Scrolling Improvements for Mobile */
@media (max-width: 768px) {
  .ai-search-mobile-container {
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
  }

  .copilotkit-chat-messages-container {
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
  }
}

/* Input Area Mobile Improvements */
@media (max-width: 768px) {
  .copilotkit-chat-input-textarea {
    font-size: 16px !important; /* Prevents zoom on iOS */
    min-height: 44px !important; /* Better touch target */
  }

  .copilotkit-chat-input-button {
    min-height: 44px !important;
    min-width: 44px !important;
  }
}

/* Loading States Mobile */
@media (max-width: 768px) {
  .copilot-loading-container {
    padding: 1rem !important;
  }

  .copilot-loading-container p {
    font-size: 0.9rem !important;
  }
}
