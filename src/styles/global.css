@import url("https://fonts.googleapis.com/css2?family=Baskervville:ital,wght@0,400;1,400&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Karla:wght@400;700&display=swap");
@import "./mobile-menu.css";

/* Custom Fonts */
@font-face {
  font-family: "Funktional Grotesk";
  src: url("/funktional-grotesk-regular.otf") format("opentype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Default Theme Colors */
  --background: #ffffff;
  --foreground: #000000;
  --primary: #000000;
  --primary-rgb: 0, 0, 0;
  --primary-foreground: #ffffff;
  --secondary: #c3c3c3;
  --secondary-foreground: #000000;
  --accent: #58e8d5;
  --accent-foreground: #000000;

  --card: #ffffff;
  --card-foreground: #151921;
  --popover: #ffffff;
  --popover-foreground: #151921;
  --muted: #f5f7fa;
  --muted-foreground: #65728a;

  --border: #e1ecf6;
  --input: #e1ecf6;
  --ring: #0e71b3;

  --destructive: #e63946;
  --destructive-foreground: #ffffff;

  --radius: 0.375rem; /* 6px */
}

.theme-alpine {
  --background: #f9f9f7;
  --foreground: #1b1b1b;
  --primary: #0e71b3;
  --primary-rgb: 14, 113, 179;
  --primary-foreground: #ffffff;
  --secondary: #e8e6e3;
  --secondary-foreground: #1b1b1b;
  --accent: #e8e6e3;
  --accent-foreground: #1b1b1b;

  --card: #ffffff;
  --card-foreground: #1b1b1b;
  --popover: #ffffff;
  --popover-foreground: #1b1b1b;
  --muted: #f0eee9;
  --muted-foreground: #78736a;

  --border: #e8e6e3;
  --input: #e8e6e3;
  --ring: #0e71b3;
}

.theme-midnight {
  --background: #151921;
  --foreground: #ffffff;
  --primary: #4096d3;
  --primary-rgb: 64, 150, 211;
  --primary-foreground: #ffffff;
  --secondary: #222c3c;
  --secondary-foreground: #ffffff;
  --accent: #273449;
  --accent-foreground: #ffffff;

  --card: #1c2230;
  --card-foreground: #ffffff;
  --popover: #1c2230;
  --popover-foreground: #ffffff;
  --muted: #2a3141;
  --muted-foreground: #a1a1aa;

  --border: #2a3141;
  --input: #2a3141;
  --ring: #4096d3;
}

.theme-frost {
  --background: #ffffff;
  --foreground: #151921;
  --primary: #0e71b3;
  --primary-rgb: 14, 113, 179;
  --primary-foreground: #ffffff;
  --secondary: #f0f7fc;
  --secondary-foreground: #151921;
  --accent: #e8f4ff;
  --accent-foreground: #151921;

  --card: #f8f9fa;
  --card-foreground: #151921;
  --popover: #f8f9fa;
  --popover-foreground: #151921;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;

  --border: #e1ecf6;
  --input: #e1ecf6;
  --ring: #0e71b3;
}

@layer base {
  * {
    border-color: var(--border);
  }

  html {
    font-family: "Baskervville", serif;
    scroll-behavior: smooth;
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: "Baskervville", serif;
    transition-property: color, background-color, border-color;
    transition-duration: 300ms;
    transition-timing-function: ease-in-out;
    padding-top: 76px; /* Add padding to account for fixed header */
  }

  .font-karla {
    font-family: "Karla", sans-serif;
  }

  /* Number formatting utilities */
  .tabular-nums {
    font-variant-numeric: tabular-nums lining-nums;
    font-feature-settings: "tnum" 1, "lnum" 1;
  }

  .lining-nums {
    font-variant-numeric: lining-nums;
    font-feature-settings: "lnum" 1;
  }

  .proportional-nums {
    font-variant-numeric: proportional-nums lining-nums;
    font-feature-settings: "pnum" 1, "lnum" 1;
  }

  /* Global number styling - force lining numerals for all numbers */
  .price,
  .currency,
  .number,
  .date,
  .time,
  [class*="price"],
  [class*="currency"],
  [class*="amount"],
  [class*="cost"],
  [class*="total"],
  [class*="date"],
  [class*="time"] {
    font-variant-numeric: lining-nums;
    font-feature-settings: "lnum" 1;
  }

  /* Phone number styling */
  a[href^="tel:"] {
    font-variant-numeric: lining-nums !important;
    font-feature-settings: "lnum" 1 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }

  h1 {
    font-family: "Baskervville", serif;
    font-weight: normal;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    font-size: 2.25rem;
  }

  @media (min-width: 768px) {
    h1 {
      font-size: 3rem;
    }
  }

  h2 {
    font-family: "Baskervville", serif;
    font-weight: normal;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    font-size: 2rem; /* 32px */
  }

  h3 {
    font-family: "Baskervville", serif;
    font-weight: normal;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    font-size: 1.75rem; /* 28px */
  }

  h4,
  h5,
  h6 {
    font-family: "Baskervville", serif;
    font-weight: normal;
    letter-spacing: 0.1em;
    text-transform: uppercase;
  }

  p {
    font-family: "Baskervville", serif;
    font-size: 1rem;
  }

  /* Specific numeric content styling */
  .numeric-content,
  .hotel-price,
  .room-price,
  .booking-price,
  .total-price,
  .per-night,
  .discount-price,
  .original-price,
  .final-price {
    font-variant-numeric: lining-nums;
    font-feature-settings: "lnum" 1;
  }

  /* Global override for common numeric patterns */
  body {
    font-variant-numeric: lining-nums;
    font-feature-settings: "lnum" 1;
  }
}

@layer base {
  *:focus-visible {
    outline: none;
    transition: all 0.2s ease;
  }

  .sr-only {
    position: absolute;
    margin: -1px;
    height: 1px;
    width: 1px;
    overflow: hidden;
    padding: 0;
  }

  .sr-only:focus-visible {
    position: static;
    margin: 0;
    height: auto;
    width: auto;
    padding: 0.5rem;
    background-color: var(--background);
    color: var(--foreground);
    border: 1px solid var(--ring);
  }
}

/* Utility classes */
.container-custom {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 2rem;
  padding-right: 2rem;
  max-width: 1440px;
}

/* Consistent padding for all screen sizes */
@media (min-width: 768px) {
  .container-custom {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

@media (min-width: 1280px) {
  .container-custom {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

/* Section styles */
.section-intro {
  max-width: 48rem; /* 3xl */
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2rem; /* 32px */
  line-height: 2.5rem;
  font-family: "Baskervville", serif;
  font-weight: normal;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.125rem;
  line-height: 1.75rem;
  color: color-mix(in srgb, var(--foreground) 70%, transparent);
  font-weight: 300;
}

.section-micro-headline {
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: color-mix(in srgb, var(--foreground) 80%, transparent);
  margin-bottom: 0.75rem;
  font-family: "Karla", sans-serif;
  font-weight: normal;
}

@media (min-width: 768px) {
  .section-micro-headline {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.poetic-intro {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-style: italic;
  color: color-mix(in srgb, var(--foreground) 90%, transparent);
  margin-bottom: 2rem;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  font-family: "Baskervville", serif;
}

@media (min-width: 768px) {
  .poetic-intro {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

/* Hotel tag styles are now applied directly in the HotelCard component */

/* Shadow glow effects */
.shadow-glow {
  box-shadow: 0 0 15px rgba(var(--primary-rgb), 0.2);
}

.shadow-glow-lg {
  box-shadow: 0 0 25px rgba(var(--primary-rgb), 0.3);
}

/* Background patterns */
.pattern-grid-lg {
  background-image: linear-gradient(var(--primary) 1px, transparent 1px),
    linear-gradient(90deg, var(--primary) 1px, transparent 1px);
  background-size: 40px 40px;
}

.pattern-dots {
  background-image: radial-gradient(
    var(--primary-foreground) 1px,
    transparent 1px
  );
  background-size: 20px 20px;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.btn-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
  padding: 0.75rem 1.5rem;
  transition-property: all;
  transition-duration: 300ms;
  border: 1px solid var(--primary);
  font-family: "Karla", sans-serif;
  font-weight: bold;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.btn-primary:hover {
  background-color: color-mix(in srgb, var(--primary) 90%, transparent);
  border-color: color-mix(in srgb, var(--primary) 80%, transparent);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
  padding: 0.75rem 1.25rem;
  transition-property: all;
  transition-duration: 300ms;
  font-family: "Karla", sans-serif;
  font-weight: bold;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  min-width: 180px;
  text-align: center;
}

.btn-outline:hover {
  background-color: color-mix(in srgb, var(--primary) 10%, transparent);
}

.btn-secondary {
  background-color: var(--secondary);
  color: var(--secondary-foreground);
  padding: 0.75rem 1.25rem;
  transition-property: all;
  transition-duration: 300ms;
  border: 1px solid var(--secondary);
  font-family: "Karla", sans-serif;
  font-weight: bold;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  min-width: 180px;
  text-align: center;
}

.btn-secondary:hover {
  background-color: color-mix(in srgb, var(--secondary) 90%, transparent);
  border-color: color-mix(in srgb, var(--secondary) 80%, transparent);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-glow-soft {
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0px);
  }
}

@keyframes pan-image {
  0% {
    transform: scale(1.05) translate(0, 0);
  }

  33% {
    transform: scale(1.05) translate(-1%, -1%);
  }

  66% {
    transform: scale(1.05) translate(1%, 1%);
  }

  100% {
    transform: scale(1.05) translate(0, 0);
  }
}

@keyframes navShrink {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pan-image {
  animation: pan-image 30s ease-in-out infinite;
}

.animate-nav-shrink {
  animation: navShrink 0.3s ease-out forwards;
}

.hover-magnetic {
  transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.hover-magnetic:hover {
  transform: translateY(-2px) scale(1.01);
}

.fade-up-section {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.fade-up-section.visible {
  opacity: 1;
  transform: translateY(0);
}

@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-delay: -1ms !important;
    animation-duration: 1ms !important;
    animation-iteration-count: 1 !important;
    background-attachment: initial !important;
    scroll-behavior: auto !important;
    transition-duration: 0s !important;
    transition-delay: 0s !important;
  }
}

p.poweredBy {
  visibility: hidden !important;
}

.copilotKitMessages {
  margin-top: 60px;
}
