/* Airbnb-style Header */
.airbnb-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: white;
  border-bottom: 1px solid rgba(53, 102, 171, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
  padding: 16px 0;
}

.airbnb-header.scrolled {
  box-shadow: 0 2px 10px rgba(53, 102, 171, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Logo */
.logo-container {
  flex: 1;
}

.logo {
  height: 40px;
  object-fit: contain;
}

/* Search Container */
.search-container {
  flex: 2;
  display: flex;
  justify-content: center;
}

/* User Menu */
.user-menu {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}

.menu-items {
  display: flex;
  gap: 24px;
}

.menu-item {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  text-decoration: none;
  transition: color 0.2s ease;
}

.menu-item:hover {
  color: #3566ab;
}

.user-profile {
  position: relative;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #3566ab;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-avatar:hover {
  background-color: #2a5296;
}

.login-button {
  padding: 8px 16px;
  border-radius: 24px;
  background-color: #3566ab;
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
}

.login-button:hover {
  background-color: #2a5296;
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .menu-items {
    display: none;
  }
  
  .search-container {
    flex: 1;
  }
}

@media (max-width: 576px) {
  .airbnb-header {
    padding: 12px 0;
  }
  
  .logo {
    height: 32px;
  }
  
  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
  
  .login-button {
    padding: 6px 12px;
    font-size: 12px;
  }
}
