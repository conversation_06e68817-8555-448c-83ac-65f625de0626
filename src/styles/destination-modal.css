/* Destination Modal Overlay */
.destination-detail-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: flex-end;
  z-index: 99999; /* Very high z-index to ensure it's above everything */
  overflow: hidden;
}

/* Modal Container - Right side sheet */
.destination-detail-modal-container {
  position: relative;
  width: 70%;
  height: 100%;
  background-color: white;
  overflow: hidden;
  box-shadow: -5px 0 25px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  animation: sheetSlideIn 0.3s ease-out;
}

@keyframes sheetSlideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Closing animation */
.destination-detail-modal-container.closing {
  animation: sheetSlideOut 0.3s ease-out forwards;
}

@keyframes sheetSlideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

/* Modal Content */
.destination-detail-modal-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

/* Close Button */
.destination-detail-modal-overlay .modal-close-button {
  position: absolute;
  top: 16px;
  left: -50px;
  width: 40px;
  height: 40px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #333;
  border: none;
  cursor: pointer;
  z-index: 1001;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s;
}

.destination-detail-modal-overlay .modal-close-button:hover {
  background-color: #f3f4f6;
  transform: scale(1.1);
}

/* Alternative close button for mobile */
.destination-detail-modal-overlay .mobile-close-button {
  display: none;
  position: absolute;
  top: 16px;
  right: 16px;
  width: 36px;
  height: 36px;
  background-color: white;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #333;
  border: none;
  cursor: pointer;
  z-index: 1001;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
  .destination-detail-modal-overlay .modal-close-button {
    display: none;
  }

  .destination-detail-modal-overlay .mobile-close-button {
    display: flex;
  }
}

/* Sticky header in modal */
.destination-detail-modal-content .sticky-header-container {
  position: sticky;
  top: 0;
  z-index: 50;
  width: 100%;
}

/* Hero section in modal */
.destination-detail-modal-content #gallery {
  height: 400px;
}

/* Make sure the location section is visible */
.destination-detail-modal-content #location {
  display: block;
}

/* Force mobile layout in modal */
.destination-detail-modal-content .grid-cols-1.lg\:grid-cols-3 {
  grid-template-columns: 1fr !important;
}

/* Make all grid columns stack in modal */
.destination-detail-modal-content .lg\:col-span-2,
.destination-detail-modal-content .col-span-1 {
  grid-column: span 1 / span 1 !important;
}

/* Adjust padding and margins for mobile-like view */
.destination-detail-modal-content .py-8 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.destination-detail-modal-content .gap-12 {
  gap: 1.5rem !important;
}

/* Make hero section more compact */
.destination-detail-modal-content .pt-8 {
  padding-top: 1rem !important;
}

.destination-detail-modal-content .pb-6 {
  padding-bottom: 0.75rem !important;
}

.destination-detail-modal-content .mb-12 {
  margin-bottom: 1rem !important;
}

/* Make navigation links in sticky header more responsive */
.destination-detail-modal-content .sticky-header-container .space-x-4 {
  gap: 0.75rem;
}

.destination-detail-modal-content .sticky-header-container a {
  font-size: 0.8rem;
  white-space: nowrap;
}

/* Adjust destination info in sticky header */
.destination-detail-modal-content .sticky-header-container h1 {
  font-size: 1rem;
}

.destination-detail-modal-content .sticky-header-container .text-sm {
  font-size: 0.75rem;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .destination-detail-modal-container {
    width: 80%;
  }
}

@media (max-width: 768px) {
  .destination-detail-modal-container {
    width: 100%;
  }

  .destination-detail-modal-content #gallery {
    height: 300px;
  }

  .destination-detail-modal-overlay .modal-close-button {
    top: 12px;
    right: 12px;
  }
}
