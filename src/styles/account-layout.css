/* Account Layout Styles */

.account-sidebar {
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.account-sidebar::-webkit-scrollbar {
  width: 4px;
}

.account-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.account-sidebar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
}

.account-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* Sidebar Navigation Animations */
.account-sidebar nav button {
  position: relative;
  overflow: hidden;
}

.account-sidebar nav button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.account-sidebar nav button:hover::before {
  left: 100%;
}

/* Active state indicator */
.account-sidebar nav button.active {
  background: linear-gradient(135deg, #285DA6 0%, #1A3A6E 100%);
  box-shadow: 0 4px 12px rgba(40, 93, 166, 0.3);
  transform: translateX(2px);
}

/* Mobile responsive adjustments */
@media (max-width: 1024px) {
  .account-sidebar {
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  }
}

/* Content area animations */
.account-content {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card hover effects */
.account-card {
  transition: all 0.2s ease-in-out;
}

.account-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Stats cards animation */
.stats-card {
  animation: slideInLeft 0.5s ease-out;
}

.stats-card:nth-child(2) {
  animation-delay: 0.1s;
}

.stats-card:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Quick action buttons */
.quick-action-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.quick-action-btn:hover::before {
  left: 100%;
}

.quick-action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Table enhancements */
.trips-table {
  border-collapse: separate;
  border-spacing: 0;
}

.trips-table th {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: sticky;
  top: 0;
  z-index: 10;
}

.trips-table tr {
  transition: all 0.2s ease;
}

.trips-table tr:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Mobile card animations */
.trip-card {
  transition: all 0.2s ease;
}

.trip-card:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
  transform: translateX(4px);
  border-left: 4px solid #285DA6;
}

/* Status badges */
.status-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Loading states */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Smooth transitions for section changes */
.section-transition {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 640px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
}

/* Focus states for accessibility */
.account-sidebar nav button:focus,
.quick-action-btn:focus {
  outline: 2px solid #285DA6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .account-sidebar,
  .quick-action-btn,
  .mobile-header {
    display: none !important;
  }
  
  .account-content {
    margin: 0 !important;
    padding: 0 !important;
  }
}
