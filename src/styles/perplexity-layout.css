/* Perplexity Layout Styles */

.perplexity-layout {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* Sidebar */
.perplexity-sidebar {
  width: 260px;
  height: 100%;
  background-color: var(--background);
  border-right: 1px solid rgba(var(--primary-rgb), 0.1);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 1rem;
  flex-shrink: 0;
}

.perplexity-sidebar-logo {
  padding: 0.5rem 0 1.5rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* Navigation */
.perplexity-sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1.5rem;
}

.perplexity-nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  color: var(--foreground);
  font-size: 0.875rem;
  font-family: '<PERSON>a', sans-serif;
  transition: all 0.2s ease;
  text-align: left;
  background: transparent;
  border: none;
  cursor: pointer;
}

.perplexity-nav-item:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.perplexity-nav-item.active {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
}

.perplexity-nav-item svg {
  opacity: 0.7;
}

.perplexity-nav-item.active svg {
  opacity: 1;
  color: var(--primary);
}

/* Chat History */
.perplexity-chat-history {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.perplexity-chat-history h3 {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--foreground-muted);
  margin-bottom: 0.5rem;
  padding: 0 0.75rem;
  font-family: 'Karla', sans-serif;
  letter-spacing: 0.05em;
}

.perplexity-chat-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.perplexity-chat-item {
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: transparent;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid transparent;
}

.perplexity-chat-item:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
  border-color: rgba(var(--primary-rgb), 0.1);
}

.perplexity-chat-title {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: var(--foreground);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.perplexity-chat-preview {
  font-size: 0.75rem;
  color: var(--foreground-muted);
  margin-bottom: 0.25rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.perplexity-chat-timestamp {
  font-size: 0.7rem;
  color: var(--foreground-muted);
  opacity: 0.7;
}

/* New Thread Button */
.perplexity-new-thread {
  margin-top: 1rem;
  padding: 0.5rem 0;
}

.perplexity-new-thread-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  background-color: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Karla', sans-serif;
}

.perplexity-new-thread-button:hover {
  background-color: color-mix(in srgb, var(--primary) 90%, transparent);
  transform: translateY(-1px);
}

.perplexity-new-thread-button:active {
  transform: translateY(1px);
}

/* Main Content */
.perplexity-main-content {
  flex: 1;
  height: 100%;
  overflow-y: auto;
  background-color: var(--background);
  padding: 1.5rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .perplexity-sidebar {
    position: fixed;
    left: -260px;
    top: 0;
    bottom: 0;
    z-index: 100;
    transition: left 0.3s ease;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
  
  .perplexity-sidebar.open {
    left: 0;
  }
  
  .perplexity-main-content {
    width: 100%;
  }
  
  .sidebar-toggle {
    display: block;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 101;
    background-color: var(--background);
    border: 1px solid rgba(var(--primary-rgb), 0.1);
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
