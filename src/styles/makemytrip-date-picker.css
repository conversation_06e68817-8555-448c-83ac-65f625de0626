/* MakeMyTrip Date Picker Styles */

/* Date Picker Popover - Enhanced for MakeMyTripRoomCard */
.mmt-date-picker-popover {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  border-radius: 16px; /* More rounded corners */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  width: 35vw;
  max-width: 90vw;
  overflow: hidden;
  border: 1px solid rgba(53, 102, 171, 0.1);
}

/* Date Picker Popover positioned above the input field when there's not enough space below */
.mmt-date-picker-popover-top {
  top: auto;
  bottom: 60px;
}

/* Ensure the date picker container has proper styling */
.mmt-date-picker-container {
  width: 100%;
  padding: 1rem;
  background-color: white;
  border-radius: 16px; /* More rounded corners */
}

/* Override the default date picker styles for MakeMyTripRoomCard */
.mmt-room-card .date-picker-popover,
.mmt-room-card .rdp {
  width: 580px !important; /* Increased width */
  border-radius: 16px !important; /* More rounded corners */
}

/* Make sure the calendar grid has proper spacing */
.mmt-room-card .rdp-months {
  justify-content: space-around;
  padding: 0.5rem;
}

/* Ensure the date picker has proper styling when used in MakeMyTripRoomCard */
.mmt-room-card .w-auto {
  width: 580px !important; /* Increased width */
  border-radius: 16px !important; /* More rounded corners */
}

/* Ensure the date picker container has proper styling when used in MakeMyTripRoomCard */
.mmt-room-card .date-picker-container {
  width: 100%;
  padding: 1rem;
  background-color: white;
  border-radius: 16px; /* More rounded corners */
}

/* MakeMyTrip Guest Selector Styles */
.mmt-guest-selector-wrapper > div {
  width: 320px !important; /* Increased width */
  padding: 1rem !important; /* Increased padding */
  border-radius: 16px !important; /* More rounded corners */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(53, 102, 171, 0.1) !important;
}

/* Increase the size of buttons in the guest selector */
.mmt-guest-selector-wrapper button {
  width: 36px !important;
  height: 36px !important;
}

/* Increase the font size in the guest selector */
.mmt-guest-selector-wrapper .text-base,
.mmt-guest-selector-wrapper .text-sm.font-medium {
  font-size: 1rem !important;
}

.mmt-guest-selector-wrapper .text-xs,
.mmt-guest-selector-wrapper .text-sm.text-gray-500 {
  font-size: 0.875rem !important;
}

/* Increase spacing between items */
.mmt-guest-selector-wrapper .space-y-3 > * {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

/* Ensure proper spacing between buttons and text */
.mmt-guest-selector-wrapper .space-x-3 > * {
  margin-left: 0.5rem !important;
  margin-right: 0.5rem !important;
}
