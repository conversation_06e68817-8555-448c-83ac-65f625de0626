.featured-hotels {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  transition: all 0.3s ease;
}

/* Split View Styles */
.split-view-active {
  max-width: 100%;
  padding: 0;
}

.split-view-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: calc(100vh - 100px);
  transition: all 0.3s ease;
}

.split-view-left {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
  transition: all 0.3s ease;
  max-width: 100%;
}

.split-view-container.active .split-view-left {
  max-width: 35%;
}

.split-view-container.active .split-view-right {
  max-width: 65%;
}

.split-view-container.active .hotel-grid {
  grid-template-columns: repeat(2, 1fr);
}

@media (max-width: 1100px) {
  .split-view-container.active .hotel-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .split-view-container.active .split-view-left,
  .split-view-container.active .split-view-right {
    max-width: 100%;
  }
}

.split-view-right {
  flex: 1.5;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #e5e7eb;
  background-color: #fff;
  transition: all 0.3s ease;
  overflow: hidden;
}

.hotel-detail-content {
  flex: 1;
  overflow-y: auto;
  height: 100%;
  padding: 0;
  background-color: #fff;
}

/* Sticky header in split view */
.sticky-header-container {
  position: sticky;
  top: 0;
  z-index: 50;
  width: 100%;
}

/* Make navigation links in sticky header more responsive */
.split-view-right .sticky-header-container .space-x-4 {
  gap: 0.5rem;
}

.split-view-right .sticky-header-container a {
  font-size: 0.7rem;
  white-space: nowrap;
}

/* Hero section in split view */
.split-view-right #gallery {
  height: 350px;
}

/* Make hero section more compact */
.split-view-right .pt-8 {
  padding-top: 1rem !important;
}

.split-view-right .pb-6 {
  padding-bottom: 0.75rem !important;
}

.split-view-right .mb-12 {
  margin-bottom: 1rem !important;
}

/* Ensure photo modal has higher z-index than sticky header */
.split-view-right .fixed {
  z-index: 9999;
}

/* Override some styles for the HotelMainContent component in split view */
.split-view-right .container-custom {
  padding-left: 20px;
  padding-right: 20px;
  max-width: 100%;
  width: 100%;
}

/* Force mobile layout in split view */
.split-view-right .grid-cols-1.lg\:grid-cols-3 {
  grid-template-columns: 1fr !important;
}

/* Make all grid columns stack in split view */
.split-view-right .lg\:col-span-2,
.split-view-right .col-span-1 {
  grid-column: span 1 / span 1 !important;
}

/* Adjust the sticky booking box in split view */
.split-view-right .sticky {
  position: relative;
  top: 0;
  margin-top: 20px;
  width: 100%;
  z-index: 10; /* Lower z-index than sticky header */
}

/* Adjust the booking box width in split view */
.split-view-right .booking-box {
  width: 100% !important;
  max-width: 100% !important;
}

/* Adjust padding and margins for mobile-like view */
.split-view-right .py-8 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.split-view-right .gap-12 {
  gap: 1.5rem !important;
}

/* Make room cards more compact */
.split-view-right .room-card {
  margin-bottom: 1rem;
}

/* Make sure the location section is visible */
.split-view-right #location {
  display: block;
}

/* Adjust spacing for components in split view */
.split-view-right .py-8 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.split-view-right .mb-12 {
  margin-bottom: 1rem;
}

/* Selected hotel card styles */
.hotel-card-wrapper.selected {
  background-color: #f3f4f6;
  border-radius: 8px;
}

.hotel-card-wrapper {
  cursor: pointer;
}

.hotel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 0;
  width: 100%;
}

@media (max-width: 1400px) {
  .hotel-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 18px;
  }
}

@media (max-width: 1100px) {
  .hotel-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .hotel-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 14px;
  }

  .split-view-container {
    flex-direction: column;
    height: auto;
  }

  .split-view-container.active .split-view-left {
    max-width: 100%;
    margin-bottom: 20px;
  }

  .split-view-right {
    height: auto;
    min-height: 100vh;
  }

  /* Adjust HotelMainContent styles for mobile */
  .split-view-right .container-custom {
    padding-left: 10px;
    padding-right: 10px;
  }

  .split-view-right .py-8 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  /* Make room cards more compact on mobile */
  .split-view-right .space-y-8 {
    margin-top: 0.5rem;
  }
}
