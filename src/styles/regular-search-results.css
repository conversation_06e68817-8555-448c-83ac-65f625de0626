/* Regular Search Results Styles */

.regular-search-results {
  width: 100%;
}

/* Hotel Results Section */
.hotel-results-section {
  width: 100%;
  padding: 0;
}

.loading-indicator,
.no-results {
  text-align: center;
  padding: 40px 0;
  color: #717171;
  font-size: 16px;
}

/* Hotel Grid */
.hotel-grid {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 24px;
  margin-top: 16px;
  width: 100%;
}

@media (max-width: 1400px) {
  .hotel-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1100px) {
  .hotel-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hotel-grid {
    grid-template-columns: 1fr;
  }
}

/* Hotel card styles removed - using HotelCard component */
