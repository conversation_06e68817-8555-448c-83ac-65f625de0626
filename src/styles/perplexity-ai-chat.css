/* Perplexity-style AI Chat Component */

.perplexity-ai-container {
  width: 100%;
  margin: 0 auto;
  background-color: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 76px); /* Full height minus header height */
  padding-bottom: 80px; /* Space for the fixed input bar */
}

/* Chat area */
.perplexity-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
  position: relative;
}

/* Messages container */
.perplexity-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px 100px 32px; /* Added bottom padding to account for fixed input bar */
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: #fff;
  scroll-behavior: smooth;
}

/* Individual message */
.perplexity-message {
  display: flex;
  flex-direction: column;
  max-width: 100%;
}

.perplexity-message-content {
  font-family: "Baskervville", serif;
  font-size: 16px;
  line-height: 1.6;
  color: #1b1b1b;
}

/* User query styling */
.perplexity-query {
  margin-bottom: 32px;
  position: relative;
  padding-left: 40px;
}

.perplexity-query::before {
  content: "";
  position: absolute;
  left: 0;
  top: 4px;
  width: 28px;
  height: 28px;
  background-color: #f0f0f2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #555;
  font-weight: 600;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23555' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='12' cy='7' r='4'%3E%3C/circle%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 16px;
}

.perplexity-query h2 {
  font-family: "Baskervville", serif;
  font-size: 20px;
  font-weight: 500;
  color: #1b1b1b;
  margin-bottom: 8px;
  line-height: 1.4;
}

/* Response styling */
.perplexity-response {
  font-family: "Baskervville", serif;
  font-size: 16px;
  line-height: 1.6;
  color: #1b1b1b;
  position: relative;
  padding-left: 40px;
}

.perplexity-response::before {
  content: "";
  position: absolute;
  left: 0;
  top: 4px;
  width: 28px;
  height: 28px;
  background-color: #3566ab;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='13 2 3 14 12 14 11 22 21 10 12 10 13 2'%3E%3C/polygon%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 16px;
}

.perplexity-response p {
  margin-bottom: 16px;
}

.perplexity-response p:last-child {
  margin-bottom: 0;
}

/* List styling */
.perplexity-hotel-list {
  margin: 16px 0;
  padding-left: 24px;
}

.perplexity-hotel-list li {
  margin-bottom: 20px;
  position: relative;
}

.perplexity-hotel-list li:last-child {
  margin-bottom: 0;
}

.perplexity-hotel-list h3 {
  font-family: "Baskervville", serif;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1b1b1b;
}

.perplexity-price-info {
  font-weight: 500;
  color: #3566ab;
  margin-top: 8px;
}

/* Source citations */
.perplexity-sources {
  display: flex;
  gap: 8px;
  margin-top: 24px;
  flex-wrap: wrap;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.perplexity-source {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 10px;
  background-color: #f5f5f7;
  border-radius: 16px;
  font-family: "Karla", sans-serif;
  font-size: 13px;
  color: #555;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.perplexity-source:hover {
  background-color: #eaeaec;
  border-color: rgba(0, 0, 0, 0.1);
}

.perplexity-source-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #555;
}

/* Input area */
.perplexity-input-container {
  padding: 16px 32px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  background-color: white;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95);
}

.perplexity-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: white;
  border-radius: 12px;
  padding: 8px 12px;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
  max-width: 900px;
  margin: 0 auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.perplexity-input-wrapper:focus-within {
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 0, 0, 0.2);
}

.perplexity-input {
  flex: 1;
  border: none;
  background: transparent;
  font-family: "Karla", sans-serif;
  font-size: 15px;
  line-height: 1.5;
  resize: none;
  padding: 0;
  height: 24px;
  min-height: 24px;
  outline: none;
  color: #1b1b1b;
}

.perplexity-input::placeholder {
  color: #9a9a9a;
  font-style: normal;
}

.perplexity-send-button {
  background-color: #0c7792;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.perplexity-send-button:hover {
  background-color: #0a6a82;
}

.perplexity-send-button:active {
  transform: scale(0.95);
}

.perplexity-send-button:disabled {
  background-color: #c3c3c3;
  cursor: not-allowed;
}

/* Loading state */
.perplexity-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  background-color: white;
}

.perplexity-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(53, 102, 171, 0.1);
  border-radius: 50%;
  border-top-color: #3566ab;
  animation: perplexity-spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes perplexity-spin {
  to {
    transform: rotate(360deg);
  }
}

.perplexity-loading-text {
  font-family: "Baskervville", serif;
  font-size: 18px;
  color: #1b1b1b;
}

/* Tabs for sources, images, etc. */
.perplexity-tabs {
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background-color: white;
  padding: 0 16px;
}

.perplexity-tab {
  padding: 16px 24px;
  font-family: "Karla", sans-serif;
  font-size: 15px;
  color: #555;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.perplexity-tab.active {
  color: #3566ab;
  border-bottom-color: #3566ab;
  font-weight: 500;
}

.perplexity-tab:hover:not(.active) {
  color: #333;
  background-color: #f9f9fa;
}

/* Typing indicator */
.perplexity-typing {
  display: flex;
  gap: 4px;
  padding: 8px 0;
}

.perplexity-typing-dot {
  width: 8px;
  height: 8px;
  background-color: #3566ab;
  border-radius: 50%;
  opacity: 0.6;
  animation: perplexity-typing 1.4s infinite ease-in-out both;
}

.perplexity-typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.perplexity-typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes perplexity-typing {
  0%,
  80%,
  100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .perplexity-messages {
    padding: 20px;
  }

  .perplexity-input-container {
    padding: 12px 20px;
  }

  .perplexity-query h2 {
    font-size: 20px;
  }

  .perplexity-tab {
    padding: 12px 16px;
    font-size: 14px;
  }
}

/* Markdown content styling */
.perplexity-response h1,
.perplexity-response h2,
.perplexity-response h3,
.perplexity-response h4 {
  margin: 24px 0 16px;
  font-family: "Baskervville", serif;
  font-weight: 600;
  color: #1b1b1b;
}

.perplexity-response h1 {
  font-size: 24px;
}

.perplexity-response h2 {
  font-size: 22px;
}

.perplexity-response h3 {
  font-size: 20px;
}

.perplexity-response h4 {
  font-size: 18px;
}

.perplexity-response ul,
.perplexity-response ol {
  margin: 16px 0;
  padding-left: 24px;
}

.perplexity-response li {
  margin-bottom: 8px;
}

.perplexity-response a {
  color: #3566ab;
  text-decoration: underline;
}

.perplexity-response code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 5px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
}

.perplexity-response pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
  font-family: monospace;
  font-size: 14px;
}

/* Hotel card styling for horizontal scrolling */
.perplexity-hotel-cards {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  padding: 8px 0 16px;
  margin: 16px 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(53, 102, 171, 0.3) transparent;
}

.perplexity-hotel-cards::-webkit-scrollbar {
  height: 6px;
}

.perplexity-hotel-cards::-webkit-scrollbar-track {
  background: transparent;
}

.perplexity-hotel-cards::-webkit-scrollbar-thumb {
  background-color: rgba(53, 102, 171, 0.3);
  border-radius: 6px;
}

/* Perplexity hotel card styles removed - using HotelCard component */

.perplexity-hotel-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.perplexity-hotel-info {
  padding: 12px;
}

.perplexity-hotel-name {
  font-family: "Baskervville", serif;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #1b1b1b;
}

.perplexity-hotel-location {
  font-family: "Karla", sans-serif;
  font-size: 14px;
  color: #555;
  margin-bottom: 8px;
}

.perplexity-hotel-price {
  font-family: "Karla", sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #3566ab;
}
