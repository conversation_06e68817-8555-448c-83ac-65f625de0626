/* Hamburger Menu Styles */
.hamburger-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: var(--background);
  z-index: 200; /* Increased z-index to be higher than header */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease, visibility 0.5s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.hamburger-menu-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Hide header when hamburger menu is open */
body:has(.hamburger-menu-overlay.open) .header-wrapper {
  z-index: 99 !important;
}

.hamburger-menu-container {
  width: 100%;
  height: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  z-index: 201; /* Ensure container content is above the header */
}

.hamburger-menu-header {
  display: flex;
  justify-content: flex-end;
  padding: 1rem 0;
  position: relative;
  z-index: 201; /* Ensure the close button is above everything */
}

.hamburger-close-button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: var(--foreground);
  transition: opacity 0.3s ease;
}

.hamburger-close-button:hover {
  opacity: 0.7;
}

.hamburger-menu-nav {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hamburger-menu-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: center;
}

.hamburger-menu-nav li {
  margin-bottom: 2rem;
}

.hamburger-menu-nav a {
  font-family: "Baskervville", serif;
  font-size: 2.5rem;
  color: var(--foreground);
  text-decoration: none;
  position: relative;
  transition: color 0.3s ease;
  letter-spacing: 0.05em;
}

.hamburger-menu-nav a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: var(--foreground);
  transition: width 0.3s ease;
}

.hamburger-menu-nav a:hover::after,
.hamburger-menu-nav a.active::after {
  width: 100%;
}

.hamburger-menu-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem 0;
}

.hamburger-menu-social {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.hamburger-menu-social a {
  color: var(--foreground);
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.hamburger-menu-social a:hover {
  opacity: 1;
}

.hamburger-menu-contact {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.hamburger-menu-contact a {
  font-family: "Karla", sans-serif;
  font-size: 0.875rem;
  color: var(--foreground);
  text-decoration: none;
  letter-spacing: 0.05em;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.hamburger-menu-contact a:hover {
  opacity: 1;
}

/* Animation for menu items */
.hamburger-menu-overlay.open .hamburger-menu-nav li {
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
}

.hamburger-menu-overlay.open .hamburger-menu-nav li:nth-child(1) {
  animation-delay: 0.1s;
}

.hamburger-menu-overlay.open .hamburger-menu-nav li:nth-child(2) {
  animation-delay: 0.2s;
}

.hamburger-menu-overlay.open .hamburger-menu-nav li:nth-child(3) {
  animation-delay: 0.3s;
}

.hamburger-menu-overlay.open .hamburger-menu-nav li:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .hamburger-menu-nav a {
    font-size: 2rem;
  }

  .hamburger-menu-nav li {
    margin-bottom: 1.5rem;
  }

  .hamburger-menu-container {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hamburger-menu-nav a {
    font-size: 1.75rem;
  }

  .hamburger-menu-nav li {
    margin-bottom: 1.25rem;
  }

  .hamburger-menu-container {
    padding: 1rem;
  }
}
