/* MakeMyTrip Room Card Styles - 3-Column Layout Implementation */

/* Main container */
.mmt-room-card {
  position: relative;
  margin-bottom: 2rem;
  background-color: white;
  border: 1px solid #e7e7e7;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;
  /* Variables for scroll behavior */
  --header-height: 56px;
  --category-header-height: 60px;
  --transition-duration: 0.3s;
  --scroll-phase: 1; /* Default to phase 1 */
}

.mmt-room-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* 3-Column Layout */
.mmt-three-col-layout {
  display: flex;
  flex-direction: column;
}

@media (min-width: 1024px) {
  .mmt-three-col-layout {
    flex-direction: row;
  }
}

/* Room category container */
.room-category-section {
  position: relative;
  margin-bottom: 2rem;
}

/* Room category header - becomes sticky when scrolling */
.room-category-header {
  position: sticky;
  top: 0;
  background-color: white;
  padding: 1rem 0;
  z-index: 30; /* Higher than room cards */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: top 0.3s ease;
}

/* Sticky state for room category header */
.room-category-header.is-sticky {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Room selection container */
.room-selection-container {
  --header-height: 56px;
}

/* First column - Room category (33.33% width) */
.mmt-room-category {
  padding: 1.5rem;
  border-right: 1px solid #e7e7e7;
  position: relative;
  width: 100%;
  background-color: white;
}

@media (min-width: 1024px) {
  .mmt-room-category {
    width: 33.33%;
    position: sticky;
    top: var(--header-height, 56px);
    align-self: flex-start;
    height: auto;
    max-height: calc(100vh - var(--header-height, 56px));
    overflow-y: auto;
    z-index: 20;
    transition: top 0.3s ease, position 0.3s ease;
  }

  /* Enhanced sticky behavior for MakeMyTrip implementation */
  .mmt-room-card.phase-1 .mmt-room-category {
    position: sticky;
    top: var(--header-height, 56px);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  }

  .mmt-room-card.phase-2 .mmt-room-category {
    position: sticky;
    top: var(--header-height, 56px);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  }

  /* In phase 3, allow the room category to scroll with the page */
  .mmt-room-card.phase-3 .mmt-room-category {
    position: relative;
    top: 0;
    box-shadow: none;
  }
}

/* Columns 2 & 3 container */
.mmt-details-pricing-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
}

@media (min-width: 1024px) {
  .mmt-details-pricing-container {
    width: 66.67%;
    /* Enhanced scroll behavior for MakeMyTrip implementation */
    overflow-y: auto;
    max-height: calc(100vh - var(--header-height, 56px));
    transition: overflow-y 0.3s ease, max-height 0.3s ease;
  }

  /* In phase 1, the details container scrolls independently */
  .mmt-room-card.phase-1 .mmt-details-pricing-container {
    overflow-y: auto;
    max-height: calc(100vh - var(--header-height, 56px));
    scrollbar-width: thin;
    scrollbar-color: rgba(53, 102, 171, 0.3) transparent;
  }

  /* In phase 2, prepare for transition */
  .mmt-room-card.phase-2 .mmt-details-pricing-container {
    overflow-y: auto;
    max-height: calc(100vh - var(--header-height, 56px));
    scrollbar-width: thin;
    scrollbar-color: rgba(53, 102, 171, 0.3) transparent;
  }

  /* In phase 3, the details container scrolls with the page */
  .mmt-room-card.phase-3 .mmt-details-pricing-container {
    overflow-y: visible;
    max-height: none;
  }

  /* Customize scrollbar for webkit browsers */
  .mmt-details-pricing-container::-webkit-scrollbar {
    width: 6px;
  }

  .mmt-details-pricing-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .mmt-details-pricing-container::-webkit-scrollbar-thumb {
    background-color: rgba(53, 102, 171, 0.3);
    border-radius: 3px;
  }
}

/* Meal plan row - contains both details and pricing */
.mmt-meal-plan-row {
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #e7e7e7;
  padding: 1.5rem;
}

@media (min-width: 1024px) {
  .mmt-meal-plan-row {
    flex-direction: row;
  }
}

/* Column 2 - Meal plan details (50% of the remaining width) */
.mmt-meal-plan-details {
  width: 100%;
  padding-right: 1rem;
}

@media (min-width: 1024px) {
  .mmt-meal-plan-details {
    width: 50%; /* 50% of the 66.67% remaining width = 33.33% of total */
  }
}

/* Column 3 - Meal plan pricing (50% of the remaining width) */
.mmt-meal-plan-pricing {
  width: 100%;
  padding-top: 1rem;
}

@media (min-width: 1024px) {
  .mmt-meal-plan-pricing {
    width: 50%; /* 50% of the 66.67% remaining width = 33.33% of total */
    padding-top: 0;
  }
}

/* Additional spacing at the bottom of the container */
.mmt-details-pricing-container {
  padding-bottom: 1rem;
}

/* Phase detection elements */
.mmt-phase-detector {
  position: absolute;
  height: 1px;
  width: 100%;
  opacity: 0;
  pointer-events: none;
  z-index: -1;
  left: 0;
}

.mmt-phase-detector-start {
  top: 0;
}

.mmt-phase-detector-middle {
  top: 50%;
}

.mmt-phase-detector-end {
  bottom: 0;
}

/* Debug styles - uncomment for testing */
/*
.mmt-phase-detector {
  opacity: 0.5;
  height: 5px;
  background-color: red;
}
.mmt-phase-detector-start { background-color: green; }
.mmt-phase-detector-middle { background-color: blue; }
.mmt-phase-detector-end { background-color: purple; }
*/

/* Room name */
.mmt-room-name {
  font-family: 'Karla', sans-serif;
  font-size: 1.25rem;
  font-weight: 700;
  color: #3566ab;
  margin-bottom: 0.5rem;
}

/* Room specifications */
.mmt-room-specs {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  font-size: 0.75rem;
  color: #4a4a4a;
  margin-bottom: 0.75rem;
}

.mmt-room-specs-divider {
  margin: 0 0.5rem;
  color: #ccc;
}

/* Photos badge */
.mmt-photos-badge {
  background-color: rgba(255, 255, 255, 0.9);
  color: #4a4a4a;
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mmt-photos-badge svg {
  margin-left: 0.25rem;
}

/* Super Package badge */
.mmt-super-package {
  display: inline-block;
  background-color: #fff8e6;
  color: #996008;
  border: 1px solid #f0d283;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Clearfix for proper layout */
.mmt-clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* Ensure proper stacking for room cards */
.space-y-8 > .mmt-room-card {
  position: relative;
  z-index: 1;
}

/* Ensure the next room card has proper stacking context */
.mmt-room-card + .mmt-room-card {
  margin-top: 2rem;
  position: relative;
  z-index: 0;
}

/* Room category rooms container */
.room-category-rooms {
  position: relative;
  z-index: 1;
}

/* Scroll behavior variables and classes */
.mmt-room-card {
  position: relative;
  z-index: 10;
}

/* Scroll phase classes */
.mmt-room-card.phase-1 {
  --scroll-phase: 1;
}

.mmt-room-card.phase-2 {
  --scroll-phase: 2;
}

.mmt-room-card.phase-3 {
  --scroll-phase: 3;
}

/* Force hardware acceleration for smoother transitions */
.mmt-room-card {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform, scroll-position;
}

/* Ensure smooth transitions between phases */
.mmt-room-card .mmt-room-category,
.mmt-room-card .mmt-details-pricing-container {
  transition: position 0.3s ease, top 0.3s ease, max-height 0.3s ease, overflow-y 0.3s ease;
}

/* Amenity features in grid layout */
.mmt-amenity-feature {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: #4a4a4a;
  margin-bottom: 0.5rem;
}

.mmt-amenity-feature svg {
  margin-right: 0.5rem;
  color: #3566ab;
  flex-shrink: 0;
}

/* Meal plan title */
.mmt-meal-plan-title {
  font-family: 'Karla', sans-serif;
  font-size: 1rem;
  font-weight: 700;
  color: #3566ab;
  margin-bottom: 0.75rem;
}

/* Meal plan features */
.mmt-meal-plan-features {
  margin-bottom: 1.25rem;
}

.mmt-meal-plan-feature {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
  color: #4a4a4a;
}

.mmt-meal-plan-feature svg {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
  margin-top: 0.125rem;
  flex-shrink: 0;
  color: #3566ab;
}

/* Price display */
.mmt-price-display {
  margin-bottom: 1rem;
  text-align: right;
}

.mmt-original-price {
  font-size: 0.875rem;
  color: #4a4a4a;
  text-decoration: line-through;
  margin-bottom: 0.25rem;
}

.mmt-discounted-price {
  font-size: 1.625rem;
  font-weight: 700;
  color: #000;
  margin-bottom: 0.25rem;
}

.mmt-taxes-fees {
  font-size: 0.75rem;
  color: #4a4a4a;
}

/* Select button */
.mmt-select-button {
  width: 100%;
  padding: 0.75rem;
  background-color: #3566ab;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 700;
  font-size: 0.875rem;
  text-align: center;
  transition: all 0.2s ease;
  text-transform: uppercase;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(53, 102, 171, 0.2);
  margin-bottom: 1rem;
}

.mmt-select-button:hover {
  background-color: #2a5089;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(53, 102, 171, 0.3);
}

.mmt-select-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(53, 102, 171, 0.2);
}

/* Credit card offer */
.mmt-credit-card-offer {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: rgba(0, 200, 83, 0.1);
  border: 1px solid rgba(0, 200, 83, 0.2);
  border-radius: 4px;
  font-size: 0.75rem;
}

.mmt-credit-card-offer-text {
  color: #333;
  margin-bottom: 0.375rem;
  line-height: 1.4;
}

.mmt-avail-link {
  color: #3566ab;
  font-weight: 700;
  font-size: 0.75rem;
  text-transform: uppercase;
  text-decoration: none;
  display: inline-block;
  transition: color 0.2s ease;
}

.mmt-avail-link:hover {
  color: #2a5089;
  text-decoration: underline;
}

/* More details link */
.mmt-more-details {
  color: #3566ab;
  font-size: 0.75rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: color 0.2s ease;
}

.mmt-more-details:hover {
  color: #2a5089;
  text-decoration: underline;
}

.mmt-more-details svg {
  margin-left: 0.25rem;
  width: 12px;
  height: 12px;
}
