/* Room Search Filters Mobile Styles */

/* Mobile date picker modal styles */
@media (max-width: 768px) {
  /* Ensure the mobile modal date picker has proper sizing */
  .mmt-date-picker-container {
    min-width: auto !important;
    width: 100% !important;
    padding: 1rem !important;
  }

  /* Adjust calendar grid for mobile */
  .mmt-date-picker-container .grid-cols-7 {
    gap: 0.5rem;
  }

  /* Make calendar days more touch-friendly on mobile */
  .mmt-date-picker-container .h-10.w-10 {
    height: 2.75rem !important;
    width: 2.75rem !important;
    font-size: 0.875rem !important;
  }

  /* Adjust month navigation for mobile */
  .mmt-date-picker-container .text-base {
    font-size: 0.875rem !important;
  }

  /* Ensure proper spacing in mobile modal */
  .mmt-date-picker-container .mb-6 {
    margin-bottom: 1rem !important;
  }

  .mmt-date-picker-container .mb-4 {
    margin-bottom: 0.75rem !important;
  }

  .mmt-date-picker-container .mt-4 {
    margin-top: 0.75rem !important;
  }

  /* Mobile guest selector modal styles */
  .mobile-modal-enter .space-y-3 > div {
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(53, 102, 171, 0.1);
  }

  .mobile-modal-enter .space-y-3 > div:last-child {
    border-bottom: none;
  }

  /* Make guest selector buttons more touch-friendly on mobile */
  .mobile-modal-enter .w-7.h-7 {
    width: 2.5rem !important;
    height: 2.5rem !important;
  }

  /* Adjust guest selector text for mobile */
  .mobile-modal-enter .text-sm {
    font-size: 1rem !important;
  }

  .mobile-modal-enter .text-xs {
    font-size: 0.875rem !important;
  }
}

/* Prevent background scroll when mobile modal is open */
.mobile-modal-open {
  overflow: hidden !important;
}

/* Animation for mobile modal */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes modalFadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

.mobile-modal-enter {
  animation: modalFadeIn 0.2s ease-out;
}

.mobile-modal-exit {
  animation: modalFadeOut 0.15s ease-in;
}

/* Backdrop animation */
@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.mobile-modal-backdrop {
  animation: backdropFadeIn 0.2s ease-out;
}
