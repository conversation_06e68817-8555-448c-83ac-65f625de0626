/* Airbnb Header Search Styles */
.search-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 848px;
  position: relative;
}

.search-toggle-container {
  display: flex;
  justify-content: center;
  width: 100%;
  position: absolute;
  top: -28px;
}

.search-toggle-wrapper {
  position: relative;
  display: flex;
  background-color: #f5f5f5;
  border-radius: 24px;
  padding: 3px;
  width: 320px; /* Increased width to accommodate longer text */
  height: 36px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.toggle-option {
  flex: 1;
  border: none;
  background: transparent;
  padding: 6px 16px;
  font-size: 13px;
  font-weight: 500;
  color: #666;
  z-index: 2;
  cursor: pointer;
  transition: color 0.3s ease;
  border-radius: 20px;
  font-family: "Karla", sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.toggle-option.active {
  color: #fff;
}

.ai-option {
  display: flex;
  align-items: center;
  gap: 6px;
}

.sparkle-icon {
  color: #ffd700;
}

.toggle-slider {
  position: absolute;
  top: 3px;
  left: 3px;
  width: calc(50% - 3px);
  height: calc(100% - 6px);
  background: linear-gradient(135deg, #3566ab, #4b7bc2);
  border-radius: 20px;
  transition: transform 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: 0 2px 6px rgba(53, 102, 171, 0.3);
}

.toggle-slider.ai-active {
  transform: translateX(0);
}

.toggle-slider:not(.ai-active) {
  transform: translateX(100%);
}

/* AI Mode Prompt Styles */
.ai-mode-prompt {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    rgba(53, 102, 171, 0.05),
    rgba(53, 102, 171, 0.1)
  );
  border: 1px solid rgba(53, 102, 171, 0.15);
  height: 64px; /* Fixed height to match the regular search bar */
  border-radius: 8px;
  margin-bottom: 16px;
}

.ai-prompt-container {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 0 24px;
  height: 100%;
  width: 100%;
  max-width: 600px;
}

.ai-icon {
  color: #3566ab;
  flex-shrink: 0;
}

.ai-prompt-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.ai-prompt-title {
  font-family: "Karla", sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #3566ab;
}

.ai-prompt-subtitle {
  font-family: "Karla", sans-serif;
  font-size: 14px;
  color: #666;
  font-style: italic;
}
.airbnb-header-search {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 28px; /* More rounded corners */
  border: 1px solid rgba(53, 102, 171, 0.15); /* Brand color border */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 56px; /* Reduced height */
  width: 848px;
  max-width: 848px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0.7;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.airbnb-header-search:hover {
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.1);
  border-color: rgba(53, 102, 171, 0.3);
}

.search-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 16px; /* Reduced padding */
  flex: 1;
  min-width: 0;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.search-section:hover {
  background-color: rgba(53, 102, 171, 0.04);
}

/* Styling for check-in and check-out sections */
.search-section.check-in,
.search-section.check-out {
  flex: 1;
  cursor: pointer;
}

.search-section.who {
  cursor: pointer;
}

.search-section:not(:last-of-type)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 20px; /* Reduced height */
  width: 1px;
  background-color: rgba(53, 102, 171, 0.15); /* Brand color divider */
}

.search-label {
  font-size: 11px; /* Smaller font */
  font-weight: 600;
  color: #3566ab; /* Brand color */
  margin-bottom: 2px; /* Reduced margin */
  font-family: "Karla", sans-serif;
}

.search-placeholder {
  font-size: 13px; /* Smaller font */
  color: #717171;
  font-weight: 400;
  font-family: "Karla", sans-serif;
}

.search-value {
  font-size: 13px; /* Smaller font */
  color: #3566ab; /* Brand color */
  font-weight: 500;
  font-family: "Karla", sans-serif;
}

.search-section input {
  border: none;
  background: transparent;
  font-size: 13px; /* Smaller font */
  color: #3566ab; /* Brand color */
  width: 100%;
  outline: none;
  padding: 0;
  font-family: "Karla", sans-serif;
}

.search-section input::placeholder {
  color: #717171;
  font-family: "Karla", sans-serif;
}

/* AI Search Input Styles */
.ai-search-input-section {
  flex: 1;
}

.ai-search-input {
  border: none;
  background: transparent;
  font-size: 16px;
  color: #222222;
  width: 100%;
  outline: none;
  padding: 0;
  height: 24px;
  font-family: "Karla", sans-serif;
}

.ai-search-input::placeholder {
  color: #717171;
  font-style: normal;
  font-family: "Karla", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #3566ab;
  color: white;
  height: 42px; /* Slightly larger button */
  width: 42px;
  min-width: 42px;
  border-radius: 50%;
  border: none;
  margin-right: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.25);
}

.search-button:hover {
  transform: scale(1.05);
  background-color: #2a5089; /* Darker on hover */
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.35);
}

.search-button:active {
  transform: scale(0.98);
}

/* Responsive */
@media (max-width: 992px) {
  .airbnb-header-search {
    max-width: 400px;
  }

  .search-section {
    padding: 0 8px;
  }
}

/* Enhanced Destination Select Styles */
.destination-select-container {
  position: relative;
  width: 100%;
  font-family: "Karla", sans-serif;
}

.destination-select-field {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  position: relative;
}

.destination-select-field.active {
  color: #3566ab;
}

.destination-select-field.has-value {
  color: #3566ab;
}

.destination-select-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3566ab;
  opacity: 0.8;
}

.destination-select-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.destination-placeholder {
  color: #717171;
  font-size: 14px;
}

.selected-destination {
  color: #3566ab;
  font-weight: 500;
  font-size: 14px;
}

.destination-select-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #717171;
}

.destination-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: -12px;
  width: calc(100% + 24px);
  background-color: white;
  border-radius: 16px; /* More rounded corners */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px; /* Slightly more padding */
  border: 1px solid rgba(53, 102, 171, 0.1);
}

.destination-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.destination-option:hover {
  background-color: rgba(53, 102, 171, 0.08);
}

.destination-option.selected {
  background-color: rgba(53, 102, 171, 0.12);
}

.destination-option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3566ab;
}

.destination-option-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.destination-name {
  font-size: 14px;
  font-weight: 500;
  color: #222222;
}

.destination-country {
  font-size: 12px;
  color: #717171;
}

.destination-empty {
  padding: 16px;
  text-align: center;
  color: #717171;
  font-size: 14px;
}

/* Date Picker Popover - Compact Version */
.date-picker-popover {
  position: absolute;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  border-radius: 20px; /* More rounded corners */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  width: 580px; /* Default width */
  max-width: 90vw;
  overflow: hidden;
  border: 1px solid rgba(53, 102, 171, 0.1);
}

/* Date Picker Popover positioned above the search form for hero section */
.date-picker-popover-top {
  top: auto;
  bottom: 57px;
}

/* Smaller date picker specifically for search page */
.search-wrapper .date-picker-popover,
.expanded-search-wrapper .date-picker-popover,
.airbnb-header-search .date-picker-popover {
  width: 420px; /* Even smaller width for search context */
}

/* Smaller calendar cells for search context */
.search-wrapper .date-picker-popover [class*="h-8"],
.expanded-search-wrapper .date-picker-popover [class*="h-8"],
.airbnb-header-search .date-picker-popover [class*="h-8"] {
  height: 28px !important;
  width: 28px !important;
  font-size: 11px !important;
}

/* Smaller day headers for search context */
.search-wrapper .date-picker-popover [class*="h-6"],
.expanded-search-wrapper .date-picker-popover [class*="h-6"],
.airbnb-header-search .date-picker-popover [class*="h-6"] {
  height: 20px !important;
  font-size: 9px !important;
}

/* Reduced padding for search context date picker */
.search-wrapper .date-picker-popover > div,
.expanded-search-wrapper .date-picker-popover > div,
.airbnb-header-search .date-picker-popover > div {
  padding: 16px !important;
}

/* Smaller text and spacing for search context date picker */
.search-wrapper .date-picker-popover .text-base,
.expanded-search-wrapper .date-picker-popover .text-base,
.airbnb-header-search .date-picker-popover .text-base {
  font-size: 12px !important;
}

.search-wrapper .date-picker-popover .mb-3,
.expanded-search-wrapper .date-picker-popover .mb-3,
.airbnb-header-search .date-picker-popover .mb-3 {
  margin-bottom: 6px !important;
}

.search-wrapper .date-picker-popover .mb-2,
.expanded-search-wrapper .date-picker-popover .mb-2,
.airbnb-header-search .date-picker-popover .mb-2 {
  margin-bottom: 4px !important;
}

/* Mobile-specific date picker styles */
@media (max-width: 768px) {
  .search-toggle-container {
    top: -38px !important;
  }

  .date-picker-popover {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 90vw !important;
    max-width: 350px !important;
    z-index: 9999 !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  }

  .date-picker-popover-top {
    top: 50% !important;
    bottom: auto !important;
    transform: translate(-50%, -50%) !important;
  }
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px; /* Reduced padding */
  border-bottom: 1px solid #eee;
  background-color: #f9f9fb;
}

.date-picker-header h3 {
  font-size: 14px; /* Smaller font */
  font-weight: 600;
  margin: 0;
  color: #3566ab;
  font-family: "Karla", sans-serif;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 28px; /* Smaller button */
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(53, 102, 171, 0.1);
  color: #3566ab;
}

/* Guest Selector Container */
.guest-selector-container {
  position: relative;
}

/* Mobile-specific guest selector styles */
@media (max-width: 768px) {
  .guest-selector-container > div {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 90vw !important;
    max-width: 350px !important;
    z-index: 9999 !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
    bottom: auto !important;
    right: auto !important;
    margin: 0 !important;
  }

  /* Mobile-specific destination selector styles */
  .destination-select-dropdown {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 90vw !important;
    max-width: 350px !important;
    z-index: 9999 !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  }
}

@media (max-width: 768px) {
  /* Only hide in header, not in hero section */
  .header-wrapper .airbnb-header-search {
    display: none;
  }

  /* Make it visible in hero section */
  .hero-content .airbnb-header-search {
    display: flex;
    width: 100%;
    max-width: 100%;
    height: auto;
    flex-direction: column;
    padding: 0.5rem;
    border-radius: 16px;
  }

  .date-picker-popover {
    width: 95vw;
    top: 100px;
  }

  /* .date-picker-popover-top {
    top: auto;
    bottom: 100px;
  } */

  .hero-content .search-section {
    width: 100%;
    padding: 10px;
    border-bottom: 1px solid #eee;
  }

  .hero-content .search-section:not(:last-of-type)::after {
    display: none;
  }

  .hero-content .search-button {
    margin: 10px auto;
    width: 90%;
    border-radius: 24px;
    height: 40px;
  }

  .hero-content .ai-search-input-section {
    padding: 10px;
  }

  .hero-content .ai-search-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    font-size: 16px;
    font-family: "Karla", sans-serif;
  }

  .hero-content .ai-search-input::placeholder {
    font-style: normal;
    font-family: "Karla", sans-serif;
    font-size: 16px;
    font-weight: 500;
  }
}
