/* Video Hero Section Styles */
.hero-section {
  position: relative;
  min-height: 100vh; /* Full viewport height */
  margin-top: -76px; /* Negative margin to offset header height */
  padding-top: 76px; /* Padding to ensure content is visible below header */
  border-radius: 0;
  overflow: hidden;
}

/* Video Hero Section Styles */
section {
  position: relative;
  overflow: hidden;
}

/* Container for hero section */
.hero-section-container {
  padding: 0;
  max-width: 100%;
  width: 100%;
}

/* Video Container */
.video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.video-element {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.video-active {
  opacity: 1;
  z-index: 2;
}

.video-inactive {
  opacity: 0;
  z-index: 1;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.5) 100%
  );
  z-index: 3;
  pointer-events: none;

  /* Add subtle animated snow effect */
  background-image: radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.15) 0%,
      transparent 1px
    ),
    radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 1px
    );
  background-size: 40px 40px, 80px 80px;
  animation: snowfall 20s linear infinite;
}

@keyframes snowfall {
  0% {
    background-position: 0 0, 0 0;
  }
  100% {
    background-position: 40px 40px, 80px 80px;
  }
}

/* Content Container */
.content-container {
  position: relative;
  z-index: 4;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-content {
  width: 100%;
  max-width: 1440px;
  padding: 2rem;
  margin: 0 auto;
  text-align: center;
  z-index: 3;
  padding-top: 65px;
}

/* Controls Container */
.controls-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 5;
  padding: 20px;
  pointer-events: none; /* Important: This allows clicks to pass through to the content below */
}

/* Video Controls */
.video-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  pointer-events: auto; /* Important: This makes the controls clickable */
}

.video-control-button {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.video-control-button:hover {
  background-color: rgba(53, 102, 171, 0.8);
  transform: scale(1.1);
}

/* Destination Indicator */
.destination-indicator {
  position: absolute;
  bottom: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  pointer-events: auto; /* Important: This makes the indicator clickable */
}

.destination-name {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  margin-bottom: 10px;
  font-family: "Karla", sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.destination-name:hover {
  background-color: rgba(0, 0, 0, 0.7);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.destination-label {
  font-size: 0.75rem;
  opacity: 0.8;
  font-weight: 400;
}

.current-destination {
  position: relative;
  display: inline-block;
  font-weight: 700;
}

.current-destination::after {
  content: "";
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent 0%,
    rgba(255, 255, 255, 0.8) 50%,
    transparent 100%
  );
}

.destination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.destination-nav-button {
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.3s ease;
  padding: 0;
}

.destination-nav-button:hover {
  background-color: rgba(53, 102, 171, 0.8);
  transform: scale(1.1);
}

.destination-nav-button:active {
  transform: scale(0.95);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .video-controls {
    bottom: 70px;
    right: 10px;
  }

  .destination-indicator {
    bottom: 70px;
    left: 10px;
  }

  .destination-name {
    font-size: 0.8rem;
    padding: 6px 12px;
    flex-wrap: wrap;
  }

  .destination-label {
    font-size: 0.7rem;
  }

  .destination-nav-button {
    width: 28px;
    height: 28px;
  }
}

@media (max-width: 480px) {
  .video-controls {
    bottom: 60px;
    right: 10px;
  }

  .video-control-button {
    width: 32px;
    height: 32px;
  }

  .destination-indicator {
    bottom: 60px;
    left: 10px;
  }

  .destination-name {
    font-size: 0.7rem;
    padding: 4px 10px;
    gap: 4px;
  }

  .destination-label {
    display: none;
  }

  .destination-nav-button {
    width: 24px;
    height: 24px;
  }
}
