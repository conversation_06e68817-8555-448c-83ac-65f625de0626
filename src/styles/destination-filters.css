.destination-filters-container {
  width: 100%;
  overflow-x: auto;
  padding: 16px 0;
  border-bottom: 1px solid #ebebeb;
  position: relative;
}

.destination-filters-scroll {
  display: flex;
  gap: 24px;
  padding: 0 80px;
  min-width: min-content;
}

.destination-filter-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  min-width: 80px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #717171;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.2s, border-bottom 0.2s;
  position: relative;
}

.destination-filter-item::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: transparent;
  transition: background-color 0.2s;
}

.destination-filter-item.active {
  color: #222222;
  font-weight: 600;
}

.destination-filter-item.active::after {
  background-color: #222222;
}

.destination-filter-item:hover {
  color: #222222;
}

@media (max-width: 768px) {
  .destination-filters-scroll {
    padding: 0 24px;
  }
}
