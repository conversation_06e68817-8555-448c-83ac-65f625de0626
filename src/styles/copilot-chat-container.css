/* Styles for the Copilot Chat Container on the search page */

.copilot-chat-section {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.copilot-chat-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
}

.copilot-chat-header h2 {
  margin-bottom: 0.75rem;
  color: #1b1b1b;
  font-family: 'Baskervville', serif;
  font-size: 2rem;
  font-weight: 400;
  letter-spacing: 0.02em;
  line-height: 1.2;
  position: relative;
  display: inline-block;
}

.copilot-chat-header h2::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(to right, #3566ab, rgba(53, 102, 171, 0.3));
}

.copilot-chat-header p {
  font-size: 1.125rem;
  color: #4a4a4a;
  font-family: 'Baskervville', serif;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.5;
}

.copilot-chat-wrapper {
  width: 100%;
  height: 70vh;
  min-height: 600px;
  border-radius: 1.25rem;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(53, 102, 171, 0.1);
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
  border: 1px solid rgba(53, 102, 171, 0.15);
  backdrop-filter: blur(8px);
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  position: sticky;
  top: 100px;
  will-change: transform;
}

.copilot-chat-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #3566ab, rgba(53, 102, 171, 0.5));
  z-index: 2;
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
}

.copilot-loading-container {
  width: 100%;
  height: 70vh;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  border-radius: 1.25rem;
  border: 1px solid rgba(53, 102, 171, 0.15);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(53, 102, 171, 0.1);
  backdrop-filter: blur(8px);
  position: relative;
}

.copilot-loading-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #3566ab, rgba(53, 102, 171, 0.5));
  z-index: 2;
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
}

.copilot-loading-container .animate-spin {
  border-color: rgba(53, 102, 171, 0.3);
  border-bottom-color: #3566ab;
  box-shadow: 0 0 15px rgba(53, 102, 171, 0.2);
}

.copilot-loading-container p {
  font-family: 'Baskervville', serif;
  color: #4a4a4a;
  font-size: 1.125rem;
  margin-top: 1.5rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .copilot-chat-wrapper {
    height: 80vh;
  }

  .copilot-loading-container {
    height: 80vh;
  }
}

/* Ensure the Copilot chat fills the container */
.copilot-chat-wrapper > div {
  height: 100%;
}

/* Style the chat interface */
.copilot-chat-wrapper .copilotKitChatInterface,
.copilot-chat-wrapper .copilotkit-chat-interface {
  height: 100% !important;
  max-height: none !important;
  border-radius: 1.25rem;
  overflow: hidden;
  background: transparent;
  font-family: 'Baskervville', serif;
}

/* Style the input container */
.copilot-chat-wrapper .copilotKitInputContainer,
.copilot-chat-wrapper .copilotkit-chat-input-container {
  padding: 1.25rem !important;
  border-top: 1px solid rgba(53, 102, 171, 0.1);
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.95));
  backdrop-filter: blur(8px);
  position: relative;
  z-index: 5;
}

/* Style the input field */
.copilot-chat-wrapper .copilotKitInput,
.copilot-chat-wrapper .copilotkit-chat-input {
  border: 1px solid rgba(53, 102, 171, 0.15);
  border-radius: 1rem;
  padding: 1rem 1.25rem;
  font-size: 1.125rem;
  font-family: 'Baskervville', serif;
  resize: none;
  width: 100%;
  outline: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(53, 102, 171, 0.08);
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  background: rgba(255, 255, 255, 0.8);
  color: #1b1b1b;
  letter-spacing: 0.01em;
  line-height: 1.6;
}

.copilot-chat-wrapper .copilotKitInput:focus,
.copilot-chat-wrapper .copilotkit-chat-input:focus {
  border-color: rgba(53, 102, 171, 0.3);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08), 0 3px 8px rgba(53, 102, 171, 0.12);
  background: white;
  transform: translateY(-2px);
}

.copilot-chat-wrapper .copilotKitInput::placeholder,
.copilot-chat-wrapper .copilotkit-chat-input::placeholder {
  color: #9a9a9a;
  font-style: italic;
  opacity: 0.8;
}

/* Style the send button */
.copilot-chat-wrapper .copilotKitSendButton,
.copilot-chat-wrapper .copilotkit-chat-send-button {
  background: linear-gradient(135deg, #3566ab 0%, #285DA6 100%);
  color: white;
  border: none;
  border-radius: 1rem;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-family: 'Karla', sans-serif;
  font-weight: 600;
  letter-spacing: 0.02em;
  cursor: pointer;
  margin-top: 0.75rem;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.3), inset 0 1px 2px rgba(255, 255, 255, 0.25);
}

.copilot-chat-wrapper .copilotKitSendButton:hover,
.copilot-chat-wrapper .copilotkit-chat-send-button:hover {
  background: linear-gradient(135deg, #3a6fbb 0%, #3566ab 100%);
  transform: translateY(-2px) scale(1.03);
  box-shadow: 0 8px 20px rgba(53, 102, 171, 0.4), inset 0 1px 3px rgba(255, 255, 255, 0.3);
}

.copilot-chat-wrapper .copilotKitSendButton:active,
.copilot-chat-wrapper .copilotkit-chat-send-button:active {
  transform: translateY(1px) scale(0.98);
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.2);
}
