/* Onboarding Tour Overlay */
.onboarding-tour-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99999;
  pointer-events: none;
}

/* Dark overlay with spotlight effect */
.onboarding-spotlight {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(2px);
  pointer-events: auto;
}

/* Highlighted element border */
.onboarding-highlight {
  position: absolute;
  border: 3px solid #3566ab;
  border-radius: 8px;
  box-shadow: 0 0 0 4px rgba(53, 102, 171, 0.2);
  pointer-events: none;
  z-index: 100000;
  animation: onboarding-pulse 2s infinite;
}

@keyframes onboarding-pulse {
  0% {
    box-shadow: 0 0 0 4px rgba(53, 102, 171, 0.2);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(53, 102, 171, 0.1);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(53, 102, 171, 0.2);
  }
}

/* Tooltip Container */
.onboarding-tooltip {
  position: absolute;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  max-width: 320px;
  min-width: 280px;
  z-index: 100001;
  pointer-events: auto;
  transform-origin: center;
  animation: onboarding-tooltip-appear 0.3s ease-out;
}

@keyframes onboarding-tooltip-appear {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Tooltip positioning adjustments */
.onboarding-tooltip-bottom {
  transform: translateX(-50%);
}

.onboarding-tooltip-top {
  transform: translateX(-50%) translateY(-100%);
}

.onboarding-tooltip-left {
  transform: translateX(-100%) translateY(-50%);
}

.onboarding-tooltip-right {
  transform: translateY(-50%);
}

/* Tooltip Content */
.onboarding-tooltip-content {
  padding: 20px;
}

.onboarding-tooltip-title {
  font-family: 'Baskervville', serif;
  font-size: 18px;
  font-weight: 600;
  color: #285DA6;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.onboarding-tooltip-text {
  font-size: 14px;
  color: #555;
  line-height: 1.5;
  margin: 0 0 20px 0;
}

/* Tooltip Actions */
.onboarding-tooltip-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.onboarding-skip-button {
  background: none;
  border: none;
  color: #666;
  font-size: 13px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.onboarding-skip-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.onboarding-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.onboarding-step-counter {
  font-size: 12px;
  color: #888;
  font-weight: 500;
}

.onboarding-next-button {
  background-color: #285DA6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
}

.onboarding-next-button:hover {
  background-color: #1e4a8d;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 93, 166, 0.3);
}

.onboarding-next-button:active {
  transform: translateY(0);
}

/* Replay Section */
.onboarding-replay-section {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
}

.onboarding-replay-button {
  background: none;
  border: 1px solid #285DA6;
  color: #285DA6;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.onboarding-replay-button:hover {
  background-color: #285DA6;
  color: white;
}

/* Tooltip Arrows */
.onboarding-tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.onboarding-tooltip-arrow-bottom {
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
}

.onboarding-tooltip-arrow-top {
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

.onboarding-tooltip-arrow-left {
  top: 50%;
  right: -8px;
  transform: translateY(-50%);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left: 8px solid white;
}

.onboarding-tooltip-arrow-right {
  top: 50%;
  left: -8px;
  transform: translateY(-50%);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid white;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .onboarding-tooltip {
    max-width: 280px;
    min-width: 240px;
    margin: 0 20px;
  }

  .onboarding-tooltip-content {
    padding: 16px;
  }

  .onboarding-tooltip-title {
    font-size: 16px;
  }

  .onboarding-tooltip-text {
    font-size: 13px;
    margin-bottom: 16px;
  }

  .onboarding-tooltip-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .onboarding-progress {
    justify-content: space-between;
    width: 100%;
  }

  .onboarding-next-button {
    padding: 10px 20px;
    width: auto;
  }

  /* Adjust positioning for mobile */
  .onboarding-tooltip-bottom,
  .onboarding-tooltip-top {
    left: 50% !important;
    transform: translateX(-50%);
  }

  .onboarding-tooltip-top {
    transform: translateX(-50%) translateY(-100%);
  }

  .onboarding-tooltip-left,
  .onboarding-tooltip-right {
    left: 50% !important;
    top: auto !important;
    transform: translateX(-50%);
  }
}

/* Ensure highlighted elements are clickable during tour */
.onboarding-tour-overlay .onboarding-highlight + * {
  pointer-events: auto;
  position: relative;
  z-index: 100002;
}

/* Special handling for small screens */
@media (max-width: 480px) {
  .onboarding-tooltip {
    max-width: calc(100vw - 40px);
    min-width: calc(100vw - 40px);
  }

  .onboarding-spotlight {
    backdrop-filter: blur(1px);
  }
}
