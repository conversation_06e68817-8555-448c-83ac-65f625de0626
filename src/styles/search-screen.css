/* Search Screen Styles */

/* Destination Filters */
.destination-filters-container {
  width: 100%;
  overflow-x: auto;
  padding: 16px 0;
  border-bottom: 1px solid #ebebeb;
  position: relative;
}

.destination-filters-scroll {
  display: flex;
  gap: 24px;
  padding: 0 0;
  min-width: min-content;
}

.destination-filter-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  min-width: 80px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #717171;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.2s, border-bottom 0.2s;
  position: relative;
}

.destination-filter-item::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: transparent;
  transition: background-color 0.2s;
}

.destination-filter-item.active {
  color: #222222;
  font-weight: 600;
}

.destination-filter-item.active::after {
  background-color: #222222;
}

.destination-filter-item:hover {
  color: #222222;
}

.search-screen-container {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0.5rem 0;
}

@media (min-width: 768px) {
  .search-screen-container {
    padding: 1rem 0;
  }
}

/* Tab Navigation */
.search-tabs {
  display: flex;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.15);
  margin-bottom: 2rem;
}

.search-tab {
  /* padding: 1rem 1.5rem; */
  font-family: "Karla", sans-serif;
  font-weight: 500;
  font-size: 1rem;
  color: var(--foreground);
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-tab:hover {
  color: var(--primary);
}

.search-tab.active {
  color: var(--primary);
  border-bottom: 2px solid var(--primary);
}

/* Search Content */
.search-content {
  width: 100%;
  padding: 0;
}

/* Explore Tab Content */
.explore-content {
  width: 100%;
}

/* Search Bar Container */
.search-bar-container {
  margin-bottom: 2rem;
}

.search-bar {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.9)
  );
  backdrop-filter: blur(8px);
  border: 1px solid rgba(var(--primary-rgb), 0.15);
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-bar:hover {
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.98),
    rgba(255, 255, 255, 0.95)
  );
  box-shadow: 0 8px 24px rgba(var(--primary-rgb), 0.08);
  border-color: rgba(var(--primary-rgb), 0.25);
  transform: translateY(-1px);
}

@media (min-width: 768px) {
  .search-bar {
    flex-direction: row;
    align-items: center;
    padding: 0.5rem;
  }
}

/* Search Fields */
.search-field {
  position: relative;
  flex: 1;
}

.search-field-button {
  width: 100%;
  text-align: left;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-field-button:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.search-field-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 0.25rem;
}

.search-field-value {
  font-size: 0.875rem;
  color: var(--foreground);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Search Button */
.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    rgba(var(--primary-rgb), 0.85) 100%
  );
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.25),
    inset 0 1px 1px rgba(255, 255, 255, 0.15);
}

.search-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.35),
    inset 0 1px 2px rgba(255, 255, 255, 0.25);
  filter: brightness(1.05);
}

/* Search Dropdown */
.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border-radius: 0.5rem;
  border: 1px solid rgba(var(--primary-rgb), 0.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 0.5rem;
  overflow: hidden;
}

.search-dropdown-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
  font-size: 0.875rem;
}

.search-dropdown-input:focus {
  outline: none;
}

.search-dropdown-suggestions {
  max-height: 200px;
  overflow-y: auto;
}

.search-dropdown-suggestion {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-dropdown-suggestion:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

/* Date Dropdown */
.date-dropdown {
  width: 300px;
}

.date-picker-container {
  padding: 1rem;
}

.date-picker-inputs {
  display: flex;
  gap: 1rem;
}

.date-picker-input {
  flex: 1;
}

.date-picker-input label {
  display: block;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.date-picker-input input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid rgba(var(--primary-rgb), 0.15);
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* Guests Dropdown */
.guests-dropdown {
  width: 300px;
  right: 0;
  left: auto;
}

.guest-selector {
  padding: 1rem;
}

.guest-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
}

.guest-type:last-child {
  border-bottom: none;
}

.guest-type-label {
  font-weight: 600;
  font-size: 0.875rem;
}

.guest-type-sublabel {
  font-size: 0.75rem;
  color: var(--foreground-muted);
}

.guest-counter {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.guest-counter-button {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(var(--primary-rgb), 0.3);
  border-radius: 50%;
  background: transparent;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.guest-counter-button:hover:not(:disabled) {
  background-color: rgba(var(--primary-rgb), 0.05);
  border-color: rgba(var(--primary-rgb), 0.5);
}

.guest-counter-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Search Results */
.search-results {
  margin-top: 2rem;
}

.search-results-title {
  font-family: "Baskervville", serif;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.search-results-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
}

@media (min-width: 640px) {
  .search-results-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .search-results-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Guide Me Tab Content */
.guide-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media (min-width: 1024px) {
  .guide-content {
    flex-direction: row;
  }
}

/* Chat Container */
.chat-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 600px;
  border: 1px solid rgba(var(--primary-rgb), 0.15);
  border-radius: 1rem;
  overflow: hidden;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

@media (min-width: 1024px) {
  .chat-container {
    width: 40%;
  }
}

/* Chat Messages */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.chat-message {
  max-width: 80%;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

.user-message {
  align-self: flex-end;
  background-color: var(--primary);
  color: white;
  border-bottom-right-radius: 0.25rem;
}

.system-message {
  align-self: flex-start;
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--foreground);
  border-bottom-left-radius: 0.25rem;
}

/* Chat Input */
.chat-input-container {
  display: flex;
  align-items: flex-end;
  padding: 1rem;
  border-top: 1px solid rgba(var(--primary-rgb), 0.1);
  background-color: white;
}

.chat-input {
  flex: 1;
  resize: none;
  border: none;
  padding: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  max-height: 120px;
  min-height: 40px;
  background-color: rgba(var(--primary-rgb), 0.05);
  border-radius: 0.5rem;
}

.chat-input:focus {
  outline: none;
}

.chat-send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    rgba(var(--primary-rgb), 0.85) 100%
  );
  color: white;
  border: none;
  margin-left: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.25),
    inset 0 1px 1px rgba(255, 255, 255, 0.15);
}

.chat-send-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.35),
    inset 0 1px 2px rgba(255, 255, 255, 0.25);
  filter: brightness(1.05);
}

.chat-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Chat Results */
.chat-results {
  width: 100%;
}

@media (min-width: 1024px) {
  .chat-results {
    width: 60%;
  }
}

.chat-results-title {
  font-family: "Baskervville", serif;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.chat-results-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
}

@media (min-width: 640px) {
  .chat-results-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media (min-width: 1024px) {
  .chat-results-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1280px) {
  .chat-results-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
