/* Traditional Search Styles */

.traditional-search-container {
  width: 100%;
  padding: 1.2rem;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.9)
  );
  backdrop-filter: blur(8px);
  border: 1px solid rgba(var(--primary-rgb), 0.15);
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.traditional-search-container:hover {
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.98),
    rgba(255, 255, 255, 0.95)
  );
  box-shadow: 0 8px 24px rgba(var(--primary-rgb), 0.08);
  border-color: rgba(var(--primary-rgb), 0.25);
  transform: translateY(-1px);
}

.traditional-search-wrapper {
  margin-bottom: 1rem;
}

/* Search field styling - no bottom margin needed */

/* Search button styling */
.search-field.flex.items-end {
  display: flex;
  align-items: flex-end;
}

/* Make search button round and match the height of inputs */
.search-button-premium.h-10.w-10 {
  border-radius: 50%;
  min-width: auto;
  padding: 0;
  margin-left: 0;
  margin-right: auto;
  width: 2.5rem !important;
  height: 2.5rem !important;
}

/* Responsive adjustments for search button */
@media (max-width: 1024px) {
  .search-field.flex.items-end {
    justify-content: flex-end;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .traditional-search-container {
    padding: 1rem;
  }
}

/* Transition effects for switching between search modes */
.search-container {
  transition: all 0.3s ease;
}

/* Smooth transitions for search interfaces */
.ai-search-container,
.traditional-search-wrapper,
.search-suggestions,
.guide-container {
  transition: opacity 0.15s ease-in-out;
}

/* We don't need these anymore since we're using display:none/block */

/* Native date input styling */
input[type="date"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-color: var(--background);
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
}

/* Hide the calendar icon in some browsers */
input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  cursor: pointer;
}

/* Ensure date inputs have proper padding for the icon */
.search-field .relative input[type="date"] {
  padding-left: 2.5rem;
  height: 2.5rem;
  border-radius: 0.375rem;
}

/* Custom styles for destination select */
.search-field .destination-select button {
  padding-left: 2.5rem;
}
