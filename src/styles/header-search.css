/* Header Search Styles */
.header-search-wrapper {
  width: 100%;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.header-search-container {
  border-radius: 2rem;
  padding: 0.75rem 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  transform-origin: center;
  display: flex;
  align-items: center;
  width: 100%;
  margin: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.header-search-container:hover {
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2),
    inset 0 1px 2px rgba(255, 255, 255, 0.15);
}

.header-search-container.is-focused,
.header-search-container:focus-within {
  border-color: rgba(255, 255, 255, 0.25);
  transform: scale(1.02);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25),
    inset 0 1px 3px rgba(255, 255, 255, 0.2);
}

.header-search-container.is-typing {
  border-color: rgba(255, 255, 255, 0.25);
}

/* Shimmer effect */
.header-shimmer::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: header-shimmer 4s infinite;
  transform: skewX(-20deg);
  z-index: 1;
}

@keyframes header-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 200%;
  }
}

.header-mode-active {
  background: linear-gradient(
    135deg,
    rgba(65, 124, 207, 1) 0%,
    rgba(53, 102, 171, 1) 100%
  ) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25),
    inset 0 1px 3px rgba(255, 255, 255, 0.2) !important;
}

.header-search-icon {
  color: rgba(255, 255, 255, 0.8);
  opacity: 0.9;
  transition: all 0.3s ease;
  position: relative;
  top: 0;
  stroke-width: 1.5;
}

.header-search-container:focus-within .header-search-icon,
.header-search-container.is-focused .header-search-icon,
.header-search-container.is-typing .header-search-icon {
  opacity: 1;
  color: white;
}

.icon-pulse {
  animation: icon-pulse 2s infinite ease-in-out;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3));
}

@keyframes icon-pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.4));
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

.header-search-input {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.95);
  font-size: 1rem;
  width: 100%;
  padding: 0.25rem 0;
  outline: none !important;
  font-family: "Baskervville", serif;
  line-height: 1.5;
  min-height: 1.5rem;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: none !important;
  display: flex;
  align-items: center;
  letter-spacing: 0.02em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-weight: 400;
}

.header-search-container:focus-within .header-search-input {
  font-size: 1.05rem;
  min-height: 1.5rem;
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.header-search-input::placeholder {
  color: rgba(255, 255, 255, 0.8);
  font-style: normal;
  opacity: 0.95;
  transition: opacity 0.3s ease;
  font-size: 1rem;
  letter-spacing: 0.02em;
  font-family: "Baskervville", serif;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.header-search-input:focus::placeholder {
  opacity: 0.7;
  color: rgba(255, 255, 255, 0.85);
}

.header-search-input:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
}

.header-search-button {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.25);
  height: 2.5rem;
  border-radius: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  position: relative;
  margin-left: auto;
  padding: 0 1.25rem;
  transition: all 0.3s ease;
  align-self: center;
  flex-shrink: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1),
    inset 0 1px 1px rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.header-search-button .button-text {
  font-family: "Karla", sans-serif;
  font-weight: 700;
  font-size: 0.8rem;
  color: #ffffff;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.header-search-button .magic-wand-icon {
  color: #ffffff;
  opacity: 1;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.header-search-button:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.15) 100%
  );
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15),
    inset 0 1px 2px rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.header-search-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: none;
  transform: none;
}

.header-search-button.animate-pulse {
  animation: button-pulse 1.5s infinite;
}

@keyframes button-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

.header-search-button.animate-pulse .magic-wand-icon {
  animation: wand-pulse 1.5s infinite;
}

@keyframes wand-pulse {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(5deg);
  }
  75% {
    transform: rotate(-5deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

/* Animation for the header search container */
#header-search-container {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

#header-search-container.opacity-0 {
  opacity: 0;
  transform: translateX(-10px);
}

#header-search-container.opacity-100 {
  opacity: 1;
  transform: translateX(0);
}

/* Header styles when scrolled */
.header-scrolled {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  background-color: var(--background);
  border-bottom-color: rgba(255, 255, 255, 0.08);
}

.header-scrolled.search-visible {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

/* Secondary header styles */
.secondary-header {
  transition: transform 0.3s ease, opacity 0.3s ease;
  position: relative;
  overflow: hidden;
}

@keyframes snowfall {
  0% {
    background-position: 0 0, 0 0;
  }
  100% {
    background-position: 40px 40px, 80px 80px;
  }
}

.secondary-header > div {
  position: relative;
  z-index: 1;
}

.secondary-header.search-visible {
  transform: translateY(0) !important;
  opacity: 1 !important;
}

/* Add a subtle separator between headers */
.primary-header {
  border-bottom: none !important;
}

.primary-header.search-visible::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.01),
    rgba(255, 255, 255, 0.05) 20%,
    rgba(255, 255, 255, 0.05) 80%,
    rgba(255, 255, 255, 0.01)
  );
}

/* Premium header styling */
header {
  letter-spacing: 0.05em;
}

.hamburger-icon {
  stroke-width: 1.5;
  transition: all 0.3s ease;
}

#hamburger-button:hover .hamburger-icon {
  opacity: 0.7;
}

/* Responsive styles */
@media (max-width: 768px) {
  .header-search-wrapper {
    max-width: 100%;
  }

  .header-search-container {
    padding: 0.6rem 1rem;
  }

  .header-search-input {
    font-size: 0.9rem;
  }

  .header-search-container:focus-within .header-search-input {
    font-size: 0.95rem;
  }

  .header-search-button {
    height: 2.25rem;
    padding: 0 1rem;
  }

  .header-search-button .button-text {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .header-search-container {
    padding: 0.5rem 0.75rem;
  }

  .header-search-input {
    font-size: 0.85rem;
  }

  .header-search-button {
    padding: 0 0.75rem;
    height: 2rem;
  }

  .header-search-button .button-text {
    font-size: 0.7rem;
  }
}
