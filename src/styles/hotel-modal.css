/* Modal Overlay */
.hotel-detail-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: flex-end;
  z-index: 99999; /* Very high z-index to ensure it's above everything */
  overflow: hidden;
}

/* Modal Container - Right side sheet */
.hotel-detail-modal-container {
  position: relative;
  width: 70%;
  height: 100%;
  background-color: white;
  overflow: hidden;
  box-shadow: -5px 0 25px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  animation: sheetSlideIn 0.3s ease-out;
}

@keyframes sheetSlideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Closing animation */
.hotel-detail-modal-container.closing {
  animation: sheetSlideOut 0.3s ease-out forwards;
}

@keyframes sheetSlideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

/* Modal Content */
.hotel-detail-modal-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

/* Close Button */
.modal-close-button {
  position: absolute;
  top: 16px;
  left: -50px;
  width: 40px;
  height: 40px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #333;
  border: none;
  cursor: pointer;
  z-index: 1001;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s;
}

.modal-close-button:hover {
  background-color: #f3f4f6;
  transform: scale(1.1);
}

/* Alternative close button for mobile */
.mobile-close-button {
  display: none;
  position: absolute;
  top: 16px;
  right: 16px;
  width: 36px;
  height: 36px;
  background-color: white;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #333;
  border: none;
  cursor: pointer;
  z-index: 1001;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
  .modal-close-button {
    display: none;
  }

  .mobile-close-button {
    display: flex;
  }
}

/* Sticky header in modal */
.hotel-detail-modal-content .sticky-header-container {
  position: sticky;
  top: 0;
  z-index: 50;
  width: 100%;
}

/* Hero section in modal */
.hotel-detail-modal-content #gallery {
  height: 400px;
}

/* Make sure the location section is visible */
.hotel-detail-modal-content #location {
  display: block;
}

/* Force mobile layout in modal */
.hotel-detail-modal-content .grid-cols-1.lg\:grid-cols-3 {
  grid-template-columns: 1fr !important;
}

/* Make all grid columns stack in modal */
.hotel-detail-modal-content .lg\:col-span-2,
.hotel-detail-modal-content .col-span-1 {
  grid-column: span 1 / span 1 !important;
}

/* Adjust the sticky booking box in modal */
.hotel-detail-modal-content .sticky {
  position: relative;
  top: 0;
  margin-top: 20px;
  width: 100%;
  z-index: 10;
}

/* Adjust the booking box width in modal */
.hotel-detail-modal-content .booking-box {
  width: 100% !important;
  max-width: 100% !important;
}

/* Adjust padding and margins for mobile-like view */
.hotel-detail-modal-content .py-8 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.hotel-detail-modal-content .gap-12 {
  gap: 1.5rem !important;
}

/* Make room cards more compact */
.hotel-detail-modal-content .room-card {
  margin-bottom: 1rem;
}

/* Make hero section more compact */
.hotel-detail-modal-content .pt-8 {
  padding-top: 1rem !important;
}

.hotel-detail-modal-content .pb-6 {
  padding-bottom: 0.75rem !important;
}

.hotel-detail-modal-content .mb-12 {
  margin-bottom: 1rem !important;
}

/* Make navigation links in sticky header more responsive */
.hotel-detail-modal-content .sticky-header-container .space-x-4 {
  gap: 0.75rem;
}

.hotel-detail-modal-content .sticky-header-container a {
  font-size: 0.8rem;
  white-space: nowrap;
}

/* Adjust hotel info in sticky header */
.hotel-detail-modal-content .sticky-header-container h1 {
  font-size: 1rem;
}

.hotel-detail-modal-content .sticky-header-container .text-sm {
  font-size: 0.75rem;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .hotel-detail-modal-container {
    width: 80%;
  }
}

@media (max-width: 768px) {
  .hotel-detail-modal-container {
    width: 100%;
  }

  .hotel-detail-modal-content #gallery {
    height: 300px;
  }

  .modal-close-button {
    top: 12px;
    right: 12px;
  }
}
