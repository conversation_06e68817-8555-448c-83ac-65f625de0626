/* Airbnb-style Search Component */
.airbnb-search-container {
  position: relative;
  width: 100%;
  max-width: 850px;
  margin: 0 auto;
  z-index: 100;
}

/* Collapsed Search */
.airbnb-search-collapsed {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 40px;
  border: 1px solid rgba(53, 102, 171, 0.1);
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.08);
  height: 56px;
  padding: 0 8px 0 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.airbnb-search-collapsed:hover {
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.15);
  transform: translateY(-1px);
}

.search-tabs {
  display: flex;
  align-items: center;
  flex: 1;
}

.search-tab {
  display: flex;
  flex-direction: column;
  padding: 0 16px;
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-tab span:first-child {
  font-size: 12px;
  font-weight: 600;
  color: #222;
}

.search-tab span.placeholder {
  font-size: 14px;
  color: #717171;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-tab input {
  border: none;
  background: transparent;
  font-size: 14px;
  color: #222;
  width: 100%;
  outline: none;
  padding: 0;
}

.search-divider {
  height: 24px;
  width: 1px;
  background-color: #ddd;
  margin: 0 4px;
}

.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3566ab 0%, rgba(53, 102, 171, 0.85) 100%);
  color: white;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  border: none;
  margin-left: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.25);
}

.search-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.35);
  background: linear-gradient(135deg, #3566ab 0%, rgba(53, 102, 171, 0.95) 100%);
}

/* Expanded Search */
.airbnb-search-expanded {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 32px;
  box-shadow: 0 6px 20px rgba(53, 102, 171, 0.2);
  padding: 16px;
  z-index: 200;
  animation: expandSearch 0.3s ease forwards;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px 16px;
  border-bottom: 1px solid rgba(53, 102, 171, 0.1);
}

.ai-toggle {
  display: flex;
  align-items: center;
}

/* AI Search Mode */
.ai-search-content {
  padding: 16px 8px;
}

.ai-search-input {
  display: flex;
  align-items: center;
  background-color: #f7f7f7;
  border-radius: 24px;
  padding: 12px 16px;
  margin-bottom: 16px;
}

.ai-search-input input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #222;
  outline: none;
  padding: 0;
}

.ai-search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3566ab 0%, rgba(53, 102, 171, 0.85) 100%);
  color: white;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.25);
}

.ai-search-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.35);
  background: linear-gradient(135deg, #3566ab 0%, rgba(53, 102, 171, 0.95) 100%);
}

.ai-search-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.ai-search-examples {
  padding: 8px 0;
}

.ai-search-examples p {
  font-size: 14px;
  font-weight: 500;
  color: #222;
  margin-bottom: 8px;
}

.examples {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.examples button {
  text-align: left;
  background-color: #f7f7f7;
  border: none;
  border-radius: 16px;
  padding: 12px 16px;
  font-size: 14px;
  color: #222;
  cursor: pointer;
  transition: all 0.2s ease;
}

.examples button:hover {
  background-color: #eaeaea;
}

/* Regular Search Mode */
.regular-search-content {
  padding: 16px 8px;
}

.search-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.search-field {
  flex: 1;
  min-width: 150px;
  position: relative;
}

.search-field label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #222;
  margin-bottom: 4px;
}

.search-field input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 16px;
  font-size: 14px;
  color: #222;
  outline: none;
  transition: all 0.2s ease;
}

.search-field input:focus {
  border-color: #3566ab;
  box-shadow: 0 0 0 2px rgba(53, 102, 171, 0.2);
}

.field-icon {
  position: absolute;
  right: 16px;
  top: 38px;
  color: #717171;
}

.search-button-large {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: linear-gradient(135deg, #3566ab 0%, rgba(53, 102, 171, 0.85) 100%);
  color: white;
  height: 48px;
  min-width: 120px;
  border-radius: 24px;
  border: none;
  padding: 0 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.25);
  margin-top: 24px;
}

.search-button-large:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.35);
  background: linear-gradient(135deg, #3566ab 0%, rgba(53, 102, 171, 0.95) 100%);
}

/* Animation */
@keyframes expandSearch {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .airbnb-search-collapsed {
    height: 48px;
    padding: 0 8px 0 16px;
  }
  
  .search-tab {
    padding: 0 8px;
  }
  
  .search-fields {
    flex-direction: column;
  }
  
  .search-field {
    width: 100%;
  }
  
  .search-button-large {
    width: 100%;
    margin-top: 16px;
  }
}
