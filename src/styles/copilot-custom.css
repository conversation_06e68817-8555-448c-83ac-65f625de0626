/* Custom styles for Copilot Chat UI */

/* Remove all padding from the chat interface */
.copilotkit-chat-interface,
.copilotKitChatInterface {
  --copilotkit-chat-padding: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Remove padding from the chat container */
.copilotkit-chat-container,
.copilotKitChatContainer,
div[class*="copilotkit-chat-container"],
div[class*="copilotKitChatContainer"] {
  padding: 0 !important;
  margin: 0 !important;
}

/* Remove padding from the messages area */
.copilotkit-chat-messages-container,
.copilotKitMessagesContainer,
div[class*="copilotkit-chat-messages-container"],
div[class*="copilotKitMessagesContainer"],
.copilotkit-chat-messages,
.copilotKitChatMessages,
div[class*="copilotkit-chat-messages"],
div[class*="copilotKitChatMessages"] {
  padding: 0 !important;
  margin: 0 !important;
}

/* Adjust message font size and remove padding */
.copilotkit-chat-interface .copilotkit-chat-message-container {
  font-size: 1.125rem !important;
  padding: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  font-family: "Baskervville", serif !important;
  letter-spacing: 0.01em !important;
  line-height: 1.6 !important;
}

/* Remove padding from user messages */
.copilotkit-chat-interface .copilotkit-chat-message-user {
  padding: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Remove padding from assistant messages */
.copilotkit-chat-interface .copilotkit-chat-message-assistant {
  padding: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Custom message styling - remove all horizontal padding */
.flex.items-start.gap-4.py-4,
.flex.items-start.gap-4.px-0.py-4,
.flex.items-start.gap-4.px-6.py-4,
.flex.items-start.gap-4.px-6.py-4.flex-row-reverse,
.flex.items-start.gap-4.px-0.py-4.flex-row-reverse {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Adjust font size in message bubbles */
.relative.py-2.px-4.rounded-2xl.max-w-\[80\%\].text-xs,
.relative.py-2.px-4.rounded-2xl.max-w-\[80\%\].text-sm,
.relative.py-2.px-4.rounded-2xl.rounded-tr-sm.max-w-\[80\%\].text-sm,
.relative.py-2.px-4.rounded-2xl.rounded-tl-sm.max-w-\[80\%\].text-sm {
  font-size: 1.125rem !important;
  line-height: 1.6 !important;
  padding: 1rem 1.5rem !important;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.98),
    rgba(255, 255, 255, 0.95)
  ) !important;
  border: 1px solid rgba(53, 102, 171, 0.15) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(53, 102, 171, 0.08) !important;
  font-family: "Baskervville", serif !important;
  letter-spacing: 0.01em !important;
  backdrop-filter: blur(8px) !important;
}

/* Adjust font size in Markdown content */
.copilotKitMarkdown p,
.copilotkit-markdown p,
div[class*="copilotKitMarkdown"] p,
div[class*="copilotkit-markdown"] p,
.copilotKitMarkdown li,
.copilotkit-markdown li,
div[class*="copilotKitMarkdown"] li,
div[class*="copilotkit-markdown"] li,
.copilotKitMarkdown ul,
.copilotkit-markdown ul,
div[class*="copilotKitMarkdown"] ul,
div[class*="copilotkit-markdown"] ul,
.copilotKitMarkdown ol,
.copilotkit-markdown ol,
div[class*="copilotKitMarkdown"] ol,
div[class*="copilotkit-markdown"] ol {
  font-size: 1.125rem !important;
  line-height: 1.6 !important;
  margin-top: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  font-family: "Baskervville", serif !important;
  letter-spacing: 0.01em !important;
  color: #1b1b1b !important;
}

/* Adjust heading sizes in Markdown */
.copilotKitMarkdown h1,
.copilotkit-markdown h1,
div[class*="copilotKitMarkdown"] h1,
div[class*="copilotkit-markdown"] h1 {
  font-size: 1.5rem !important;
  margin-top: 1.25rem !important;
  margin-bottom: 1rem !important;
  font-family: "Baskervville", serif !important;
  font-weight: 500 !important;
  letter-spacing: 0.02em !important;
  color: #1b1b1b !important;
  position: relative !important;
  display: inline-block !important;
}

.copilotKitMarkdown h1::after,
.copilotkit-markdown h1::after,
div[class*="copilotKitMarkdown"] h1::after,
div[class*="copilotkit-markdown"] h1::after {
  content: "" !important;
  position: absolute !important;
  bottom: -0.5rem !important;
  left: 0 !important;
  width: 60px !important;
  height: 2px !important;
  background: linear-gradient(
    to right,
    #3566ab,
    rgba(53, 102, 171, 0.3)
  ) !important;
}

.copilotKitMarkdown h2,
.copilotkit-markdown h2,
div[class*="copilotKitMarkdown"] h2,
div[class*="copilotkit-markdown"] h2 {
  font-size: 1.35rem !important;
  margin-top: 1.25rem !important;
  margin-bottom: 1rem !important;
  font-family: "Baskervville", serif !important;
  font-weight: 500 !important;
  letter-spacing: 0.02em !important;
  color: #1b1b1b !important;
}

.copilotKitMarkdown h3,
.copilotkit-markdown h3,
div[class*="copilotKitMarkdown"] h3,
div[class*="copilotkit-markdown"] h3,
.copilotKitMarkdown h4,
.copilotkit-markdown h4,
div[class*="copilotKitMarkdown"] h4,
div[class*="copilotkit-markdown"] h4,
.copilotKitMarkdown h5,
.copilotkit-markdown h5,
div[class*="copilotKitMarkdown"] h5,
div[class*="copilotkit-markdown"] h5,
.copilotKitMarkdown h6,
.copilotkit-markdown h6,
div[class*="copilotKitMarkdown"] h6,
div[class*="copilotkit-markdown"] h6 {
  font-size: 1.25rem !important;
  margin-top: 1.25rem !important;
  margin-bottom: 1rem !important;
  font-family: "Baskervville", serif !important;
  font-weight: 500 !important;
  letter-spacing: 0.02em !important;
  color: #1b1b1b !important;
}

/* Adjust code blocks and inline code */
.copilotKitMarkdown pre,
.copilotkit-markdown pre,
div[class*="copilotKitMarkdown"] pre,
div[class*="copilotkit-markdown"] pre {
  font-size: 0.9rem !important; /* Increased from 0.775rem */
  padding: 0.5rem !important;
  margin: 0.5rem 0 !important;
  border-radius: 0.25rem !important;
}

.copilotKitMarkdown code,
.copilotkit-markdown code,
div[class*="copilotKitMarkdown"] code,
div[class*="copilotkit-markdown"] code {
  font-size: 0.9rem !important; /* Increased from 0.775rem */
  padding: 0.1rem 0.3rem !important;
  border-radius: 0.25rem !important;
}

/* Adjust padding for full-width components */
.px-6.py-4.w-full,
.px-0.py-4.w-full {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Adjust input area padding */
.copilotkit-chat-input-container {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Remove padding from the parent containers */
.flex.h-full.w-full,
.flex-1.overflow-hidden.w-full.max-w-\[800px\].mx-auto,
.flex-1.overflow-hidden.w-full.max-w-\[800px\].mx-auto.px-0,
.h-full.w-full.flex.flex-col {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* Allow normal page scrolling */
html,
body {
  overflow-y: auto !important;
  scroll-behavior: auto !important;
}

/* Control scrolling in specific containers */
.search-screen-container,
.search-content {
  overflow-y: auto !important;
  scroll-behavior: auto !important;
}

/* Completely isolate the Copilot container from page scrolling */
.isolated-container {
  contain: strict !important;
  isolation: isolate !important;
  position: relative !important;
  z-index: 1 !important;
  overflow: hidden !important;
  height: 650px !important;
  max-height: 650px !important;
}

/* Allow scrolling only in specific containers */
.scrollable-container,
.copilot-container,
.copilotkit-chat-messages-container,
.copilotKitMessagesContainer,
div[class*="copilotkit-chat-messages-container"],
div[class*="copilotKitMessagesContainer"] {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  scroll-behavior: auto !important;
  height: 650px !important;
  max-height: 650px !important;
  position: relative !important;
  contain: strict !important;
  isolation: isolate !important;
}

/* Make sure interactive elements work properly */
button,
input,
select,
textarea,
a,
[role="button"],
[role="switch"],
[role="checkbox"],
.switch-premium,
.switch-premium-thumb,
#ai-guide-toggle,
#concierge-toggle,
.toggle-container-premium {
  pointer-events: auto !important;
  cursor: pointer !important;
  touch-action: manipulation !important;
}

/* Make sure chat containers have manual scrolling with fixed height */
.copilotkit-chat-messages-container,
.copilotKitMessagesContainer,
div[class*="copilotkit-chat-messages-container"],
div[class*="copilotKitMessagesContainer"] {
  overflow-y: auto !important;
  height: 550px !important;
  max-height: 550px !important;
  min-height: 550px !important;
  position: static !important;
  -webkit-overflow-scrolling: touch !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding-top: 60px !important; /* Increased top padding to ensure initial message is visible */
}

/* Ensure the chat interface itself is scrollable and contained */
.copilotkit-chat-interface,
.copilotKitChatInterface,
div[class*="copilotkit-chat-interface"],
div[class*="copilotKitChatInterface"] {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  height: 650px !important;
  max-height: 650px !important;
  position: relative !important;
  contain: strict !important;
  isolation: isolate !important;
  z-index: 1 !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Prevent messages from affecting page scroll */
.copilotkit-chat-message,
.copilotKitChatMessage,
div[class*="copilotkit-chat-message"],
div[class*="copilotKitChatMessage"],
.copilotkit-chat-message-user,
.copilotkit-chat-message-assistant,
div[class*="copilotkit-chat-message-user"],
div[class*="copilotkit-chat-message-assistant"] {
  overflow-anchor: none !important;
  scroll-margin: 0 !important;
  scroll-snap-align: none !important;
  scroll-snap-stop: none !important;
  contain: content !important;
  margin-top: 5px !important; /* Added margin to space out messages */
}

/* Prevent focus from causing scrolling */
*:focus {
  scroll-margin-block: 0 !important;
  outline-offset: 0 !important;
}

/* Fix the input area at the bottom and prevent it from causing scrolling */
.copilotkit-chat-input-container,
div[class*="copilotkit-chat-input-container"] {
  position: sticky !important;
  bottom: 0 !important;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.95)
  ) !important;
  backdrop-filter: blur(8px) !important;
  z-index: 10 !important;
  overflow-anchor: none !important;
  scroll-margin: 0 !important;
  contain: layout !important;
  border-top: 1px solid rgba(53, 102, 171, 0.1) !important;
  padding: 1.25rem !important;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05) !important;
}

/* Prevent input focus from causing scrolling */
.copilotkit-chat-input,
div[class*="copilotkit-chat-input"],
.copilotkit-chat-input-textarea,
div[class*="copilotkit-chat-input-textarea"],
textarea {
  scroll-margin: 0 !important;
  overflow-anchor: none !important;
  scroll-snap-align: none !important;
  scroll-snap-stop: none !important;
  border: 1px solid rgba(53, 102, 171, 0.15) !important;
  border-radius: 1rem !important;
  padding: 1rem 1.25rem !important;
  font-size: 1.125rem !important;
  font-family: "Baskervville", serif !important;
  resize: none !important;
  outline: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(53, 102, 171, 0.08) !important;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1) !important;
  background: rgba(255, 255, 255, 0.8) !important;
  color: #1b1b1b !important;
  letter-spacing: 0.01em !important;
  line-height: 1.6 !important;
}

.copilotkit-chat-input:focus,
div[class*="copilotkit-chat-input"]:focus,
.copilotkit-chat-input-textarea:focus,
div[class*="copilotkit-chat-input-textarea"]:focus,
textarea:focus {
  border-color: rgba(53, 102, 171, 0.3) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08), 0 3px 8px rgba(53, 102, 171, 0.12) !important;
  background: white !important;
  transform: translateY(-2px) !important;
}

.copilotkit-chat-input::placeholder,
div[class*="copilotkit-chat-input"]::placeholder,
.copilotkit-chat-input-textarea::placeholder,
div[class*="copilotkit-chat-input-textarea"]::placeholder,
textarea::placeholder {
  color: #9a9a9a !important;
  font-style: italic !important;
  opacity: 0.8 !important;
}

/* Styles for Copilot in the header */
.copilot-chat-container {
  position: relative;
  z-index: 1000;
}

.copilot-chat-container .copilotKitChatInterface,
.copilot-chat-container .copilotkit-chat-interface {
  height: 550px !important;
  max-height: 550px !important;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.copilot-chat-container .copilotKitMessagesContainer,
.copilot-chat-container .copilotkit-chat-messages-container {
  height: 490px !important;
  max-height: 490px !important;
  min-height: 490px !important;
  padding-top: 60px !important; /* Increased top padding to ensure initial message is visible */
}

/* Adjust the expanded header height to accommodate the Copilot chat */
.primary-header.expanded-header {
  padding-bottom: 650px !important;
}

/* Styles for direct Copilot component without container in SearchScreen */
.search-screen-container .Copilot,
.search-screen-container .SkiBookingChat,
.copilot-direct-container {
  height: 650px !important;
  max-height: 650px !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  position: relative !important;
  z-index: 1 !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  font-family: "Baskervville", serif !important;
  scroll-behavior: smooth !important;
  -webkit-overflow-scrolling: touch !important;
  will-change: transform !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
  perspective: 1000px !important;
}

/* Remove conflicting first message padding - handled by container padding instead */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Style for the initial message */
.copilotkit-chat-message-assistant:first-child,
.copilotKitChatMessageAssistant:first-child,
div[class*="copilotkit-chat-message-assistant"]:first-child,
div[class*="copilotKitChatMessageAssistant"]:first-child {
  margin-top: 40px !important;
  padding-top: 20px !important;
  position: relative !important;
}

/* Add a premium welcome badge to the first message */
.copilotkit-chat-message-assistant:first-child::before,
.copilotKitChatMessageAssistant:first-child::before,
div[class*="copilotkit-chat-message-assistant"]:first-child::before,
div[class*="copilotKitChatMessageAssistant"]:first-child::before {
  content: "Luxury Concierge" !important;
  position: absolute !important;
  top: -10px !important;
  left: 50px !important;
  background: linear-gradient(135deg, #3566ab 0%, #285da6 100%) !important;
  color: white !important;
  padding: 4px 12px !important;
  border-radius: 20px !important;
  font-size: 0.75rem !important;
  font-family: "Karla", sans-serif !important;
  font-weight: 600 !important;
  letter-spacing: 0.05em !important;
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.3) !important;
  z-index: 10 !important;
  transform: translateY(-50%) !important;
}

/* Add minimal padding to the chat messages container for proper visibility */
.copilotkit-chat-messages,
.copilotKitChatMessages,
div[class*="copilotkit-chat-messages"],
div[class*="copilotKitChatMessages"] {
  padding-top: 1rem !important;
  content-visibility: auto !important;
  scroll-padding-top: 1rem !important;
  scroll-margin-top: 1rem !important;
}

/* Ensure first message has proper spacing without positioning conflicts */
.copilotkit-chat-messages > div:first-of-type,
.copilotKitChatMessages > div:first-of-type,
div[class*="copilotkit-chat-messages"] > div:first-of-type,
div[class*="copilotKitChatMessages"] > div:first-of-type {
  margin-top: 0 !important;
  position: static !important;
}

/* Mobile Responsive Markdown Improvements */
@media (max-width: 768px) {
  /* Adjust markdown content for mobile */
  .copilotKitMarkdown p,
  .copilotkit-markdown p,
  div[class*="copilotKitMarkdown"] p,
  div[class*="copilotkit-markdown"] p,
  .ai-markdown-content p {
    font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
      sans-serif !important;
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
    margin: 0.5rem 0 !important;
  }

  .copilotKitMarkdown h1,
  .copilotkit-markdown h1,
  div[class*="copilotKitMarkdown"] h1,
  div[class*="copilotkit-markdown"] h1,
  .ai-markdown-content h1 {
    font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
      sans-serif !important;
    font-size: 1.25rem !important;
    margin-top: 1rem !important;
    margin-bottom: 0.75rem !important;
  }

  .copilotKitMarkdown h2,
  .copilotkit-markdown h2,
  div[class*="copilotKitMarkdown"] h2,
  div[class*="copilotkit-markdown"] h2,
  .ai-markdown-content h2 {
    font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
      sans-serif !important;
    font-size: 1.125rem !important;
    margin-top: 1rem !important;
    margin-bottom: 0.75rem !important;
  }

  .copilotKitMarkdown h3,
  .copilotkit-markdown h3,
  div[class*="copilotKitMarkdown"] h3,
  div[class*="copilotkit-markdown"] h3,
  .copilotKitMarkdown h4,
  .copilotkit-markdown h4,
  div[class*="copilotKitMarkdown"] h4,
  div[class*="copilotkit-markdown"] h4,
  .ai-markdown-content h3,
  .ai-markdown-content h4 {
    font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
      sans-serif !important;
    font-size: 1rem !important;
    margin-top: 1rem !important;
    margin-bottom: 0.75rem !important;
  }

  .copilotKitMarkdown ul,
  .copilotkit-markdown ul,
  div[class*="copilotKitMarkdown"] ul,
  div[class*="copilotkit-markdown"] ul,
  .copilotKitMarkdown ol,
  .copilotkit-markdown ol,
  div[class*="copilotKitMarkdown"] ol,
  div[class*="copilotkit-markdown"] ol,
  .ai-markdown-content ul,
  .ai-markdown-content ol {
    font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
      sans-serif !important;
    font-size: 0.9rem !important;
    padding-left: 1.25rem !important;
    margin: 0.5rem 0 !important;
  }

  .copilotKitMarkdown li,
  .copilotkit-markdown li,
  div[class*="copilotKitMarkdown"] li,
  div[class*="copilotkit-markdown"] li,
  .ai-markdown-content li {
    font-family: "Funktional Grotesk", "Inter", "Helvetica Neue", Arial,
      sans-serif !important;
    font-size: 0.9rem !important;
    margin: 0.25rem 0 !important;
  }

  .copilotKitMarkdown code,
  .copilotkit-markdown code,
  div[class*="copilotKitMarkdown"] code,
  div[class*="copilotkit-markdown"] code,
  .ai-markdown-content code {
    font-size: 0.8rem !important;
  }

  .copilotKitMarkdown pre,
  .copilotkit-markdown pre,
  div[class*="copilotKitMarkdown"] pre,
  div[class*="copilotkit-markdown"] pre,
  .ai-markdown-content pre {
    font-size: 0.8rem !important;
    padding: 0.5rem !important;
    margin: 0.5rem 0 !important;
  }

  /* Adjust chat input for mobile */
  .copilotkit-chat-input,
  div[class*="copilotkit-chat-input"],
  .copilotkit-chat-input-textarea,
  div[class*="copilotkit-chat-input-textarea"] {
    font-size: 16px !important; /* Prevents zoom on iOS */
    padding: 0.75rem 1rem !important;
  }

  /* Adjust message containers for mobile */
  .copilotkit-chat-messages-container,
  .copilotKitMessagesContainer,
  div[class*="copilotkit-chat-messages-container"],
  div[class*="copilotKitMessagesContainer"] {
    height: 550px !important;
    max-height: 550px !important;
    min-height: 550px !important;
  }

  /* Adjust chat messages padding for mobile */
  .copilotkit-chat-messages,
  .copilotKitChatMessages,
  div[class*="copilotkit-chat-messages"],
  div[class*="copilotKitChatMessages"] {
    padding-top: 0.75rem !important;
  }

  .copilotkit-chat-interface,
  .copilotKitChatInterface,
  div[class*="copilotkit-chat-interface"],
  div[class*="copilotKitChatInterface"] {
    height: 650px !important;
    max-height: 650px !important;
  }
}

@media (max-width: 480px) {
  /* Extra small mobile adjustments */
  .copilotKitMarkdown p,
  .copilotkit-markdown p,
  div[class*="copilotKitMarkdown"] p,
  div[class*="copilotkit-markdown"] p,
  .ai-markdown-content p {
    font-size: 0.85rem !important;
    line-height: 1.4 !important;
  }

  .copilotKitMarkdown h1,
  .copilotkit-markdown h1,
  div[class*="copilotKitMarkdown"] h1,
  div[class*="copilotkit-markdown"] h1,
  .ai-markdown-content h1 {
    font-size: 1.125rem !important;
  }

  .copilotKitMarkdown h2,
  .copilotkit-markdown h2,
  div[class*="copilotKitMarkdown"] h2,
  div[class*="copilotkit-markdown"] h2,
  .ai-markdown-content h2 {
    font-size: 1rem !important;
  }

  .copilotKitMarkdown h3,
  .copilotkit-markdown h3,
  div[class*="copilotKitMarkdown"] h3,
  div[class*="copilotkit-markdown"] h3,
  .ai-markdown-content h3 {
    font-size: 0.95rem !important;
  }

  .copilotkit-chat-messages-container,
  .copilotKitMessagesContainer,
  div[class*="copilotkit-chat-messages-container"],
  div[class*="copilotKitMessagesContainer"] {
    height: 500px !important;
    max-height: 500px !important;
    min-height: 500px !important;
  }

  /* Adjust chat messages padding for small mobile */
  .copilotkit-chat-messages,
  .copilotKitChatMessages,
  div[class*="copilotkit-chat-messages"],
  div[class*="copilotKitChatMessages"] {
    padding-top: 0.5rem !important;
  }

  .copilotkit-chat-interface,
  .copilotKitChatInterface,
  div[class*="copilotkit-chat-interface"],
  div[class*="copilotKitChatInterface"] {
    height: 600px !important;
    max-height: 600px !important;
  }
}
