/* Phone number styling to ensure consistent font with text */
a[href^="tel:"] {
  font-variant-numeric: lining-nums !important;
  font-feature-settings: "lnum" 1 !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Override any potential Roboto Mono font application */
a[href^="tel:"] * {
  font-variant-numeric: lining-nums !important;
  font-feature-settings: "lnum" 1 !important;
}
