/* Luxury AI Assistant Styling */

.luxury-assistant-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  position: relative;
}

/* Main chat container */
.luxury-chat-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
  height: 700px;
  position: relative;
}

@media (min-width: 1024px) {
  .luxury-chat-container {
    grid-template-columns: 380px 1fr;
    height: 700px;
  }
}

/* Sidebar */
.luxury-sidebar {
  background: linear-gradient(135deg, #2a5296, #3566ab);
  color: white;
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.luxury-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/sidebar-pattern.png');
  background-size: cover;
  opacity: 0.05;
  z-index: 1;
}

.luxury-sidebar-content {
  padding: 40px 30px;
  position: relative;
  z-index: 2;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.luxury-logo {
  margin-bottom: 40px;
  display: flex;
  align-items: center;
}

.luxury-logo img {
  height: 40px;
  margin-right: 15px;
}

.luxury-logo-text {
  font-family: 'Baskervville', serif;
  font-size: 24px;
  font-weight: 400;
  letter-spacing: 0.05em;
}

.luxury-assistant-title {
  margin-bottom: 20px;
}

.luxury-assistant-title h2 {
  font-family: 'Baskervville', serif;
  font-size: 28px;
  font-weight: 400;
  margin-bottom: 10px;
  letter-spacing: 0.02em;
}

.luxury-assistant-title p {
  font-family: 'Karla', sans-serif;
  font-size: 16px;
  line-height: 1.5;
  opacity: 0.9;
  font-weight: 300;
}

.luxury-features {
  margin-top: auto;
  padding-top: 30px;
}

.luxury-feature {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.luxury-feature-icon {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.luxury-feature-text {
  font-family: 'Karla', sans-serif;
  font-size: 15px;
  line-height: 1.4;
}

/* Chat area */
.luxury-chat-area {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  background: #f9f9fa;
}

.luxury-chat-header {
  padding: 25px 30px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
}

.luxury-chat-title {
  font-family: 'Baskervville', serif;
  font-size: 20px;
  color: #1b1b1b;
}

.luxury-chat-controls {
  display: flex;
  gap: 15px;
}

.luxury-control-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f7;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.luxury-control-button:hover {
  background: #eaeaec;
}

.luxury-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.luxury-message {
  display: flex;
  gap: 16px;
  max-width: 85%;
}

.luxury-message.assistant {
  align-self: flex-start;
}

.luxury-message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.luxury-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e1e1e5;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.luxury-avatar.assistant {
  background: #3566ab;
  color: white;
}

.luxury-avatar.user {
  background: #f0f0f2;
  color: #1b1b1b;
}

.luxury-message-content {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  font-family: 'Baskervville', serif;
  font-size: 16px;
  line-height: 1.6;
  color: #1b1b1b;
  position: relative;
}

.luxury-message.assistant .luxury-message-content {
  border-top-left-radius: 0;
}

.luxury-message.user .luxury-message-content {
  background: #3566ab;
  color: white;
  border-top-right-radius: 0;
}

.luxury-message-sender {
  font-family: 'Karla', sans-serif;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 5px;
}

.luxury-message.assistant .luxury-message-sender {
  color: #3566ab;
}

.luxury-message.user .luxury-message-sender {
  color: white;
  text-align: right;
}

.luxury-chat-input {
  padding: 20px 30px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: white;
}

.luxury-input-container {
  display: flex;
  align-items: flex-end;
  gap: 15px;
  background: #f5f5f7;
  border-radius: 12px;
  padding: 15px 20px;
  transition: all 0.2s ease;
}

.luxury-input-container:focus-within {
  background: #f0f0f2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.luxury-input-field {
  flex: 1;
  border: none;
  background: transparent;
  font-family: 'Baskervville', serif;
  font-size: 16px;
  line-height: 1.5;
  resize: none;
  padding: 0;
  max-height: 120px;
  min-height: 24px;
  outline: none;
  color: #1b1b1b;
}

.luxury-input-field::placeholder {
  color: #9a9a9a;
  font-style: italic;
}

.luxury-send-button {
  background: #3566ab;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.luxury-send-button:hover {
  background: #2a5296;
  transform: translateY(-2px);
}

.luxury-send-button:active {
  transform: translateY(0);
}

/* Loading state */
.luxury-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f9f9fa;
}

.luxury-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(53, 102, 171, 0.1);
  border-radius: 50%;
  border-top-color: #3566ab;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.luxury-loading-text {
  font-family: 'Baskervville', serif;
  font-size: 18px;
  color: #1b1b1b;
}

/* Responsive adjustments */
@media (max-width: 1023px) {
  .luxury-sidebar {
    display: none;
  }
  
  .luxury-chat-header {
    padding: 20px;
  }
  
  .luxury-chat-messages {
    padding: 20px;
  }
  
  .luxury-chat-input {
    padding: 15px 20px;
  }
}

/* Markdown content styling */
.luxury-message-content p {
  margin-bottom: 15px;
}

.luxury-message-content p:last-child {
  margin-bottom: 0;
}

.luxury-message-content ul, 
.luxury-message-content ol {
  margin: 15px 0;
  padding-left: 20px;
}

.luxury-message-content li {
  margin-bottom: 8px;
}

.luxury-message-content h1,
.luxury-message-content h2,
.luxury-message-content h3,
.luxury-message-content h4 {
  margin: 20px 0 10px;
  font-family: 'Baskervville', serif;
  font-weight: 500;
}

.luxury-message-content h1 {
  font-size: 22px;
}

.luxury-message-content h2 {
  font-size: 20px;
}

.luxury-message-content h3 {
  font-size: 18px;
}

.luxury-message-content h4 {
  font-size: 16px;
}

.luxury-message-content a {
  color: #3566ab;
  text-decoration: underline;
}

.luxury-message.user .luxury-message-content a {
  color: white;
}

.luxury-message-content code {
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 5px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
}

.luxury-message.user .luxury-message-content code {
  background: rgba(255, 255, 255, 0.2);
}

.luxury-message-content pre {
  background: rgba(0, 0, 0, 0.05);
  padding: 15px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 15px 0;
}

.luxury-message.user .luxury-message-content pre {
  background: rgba(255, 255, 255, 0.1);
}

/* Typing indicator */
.luxury-typing {
  display: flex;
  gap: 4px;
  padding: 10px 0;
}

.luxury-typing-dot {
  width: 8px;
  height: 8px;
  background: #3566ab;
  border-radius: 50%;
  opacity: 0.6;
  animation: typing-animation 1.4s infinite ease-in-out both;
}

.luxury-typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.luxury-typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing-animation {
  0%, 80%, 100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1);
  }
}
