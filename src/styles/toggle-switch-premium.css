/* Premium Toggle Switch Styling */

/* Enhanced switch root */
.switch-premium {
  height: 1.5rem !important;
  width: 2.75rem !important;
  background-image: linear-gradient(
    to right,
    rgba(var(--muted-rgb), 0.5),
    rgba(var(--muted-rgb), 0.7)
  ) !important;
  border: 1px solid rgba(var(--primary-rgb), 0.1) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  padding: 0 0.1875rem !important; /* Padding to match images */
  display: flex !important;
  align-items: center !important;
  position: relative !important; /* For absolute positioning of thumb */
  box-sizing: content-box !important; /* Ensure padding doesn't affect width */
}

/* Enhanced checked state */
.switch-premium[data-state="checked"] {
  background-image: linear-gradient(
    to right,
    rgba(40, 93, 166, 0.8),
    #285da6
  ) !important;
  border-color: rgba(40, 93, 166, 0.3) !important;
  box-shadow: 0 2px 8px rgba(40, 93, 166, 0.25) !important;
}

/* Enhanced thumb */
.switch-premium-thumb {
  height: 1.125rem !important;
  width: 1.125rem !important;
  background-color: white !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  margin: 0 !important; /* Remove any default margin */
}

/* Enhanced checked thumb */
.switch-premium[data-state="checked"] .switch-premium-thumb {
  transform: translateX(1.45rem) !important; /* Reduced space on the right */
  box-shadow: 0 1px 4px rgba(40, 93, 166, 0.3) !important;
}

/* Unchecked thumb position */
.switch-premium[data-state="unchecked"] .switch-premium-thumb {
  transform: translateX(
    0.15rem
  ) !important; /* Add a bit of space on the left */
}

/* Hover effects */
.switch-premium:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.switch-premium[data-state="checked"]:hover {
  box-shadow: 0 2px 12px rgba(40, 93, 166, 0.35) !important;
}

/* Focus effects */
.switch-premium:focus-visible {
  outline: 2px solid rgba(40, 93, 166, 0.5) !important;
  outline-offset: 2px !important;
}

/* Make sure toggle is interactive */
.switch-premium,
.switch-premium-thumb,
.toggle-premium,
#ai-guide-toggle,
#concierge-toggle {
  pointer-events: auto !important;
  cursor: pointer !important;
  touch-action: manipulation !important;
  user-select: none !important;
}

/* Container for the toggle */
.toggle-container-premium {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
}

/* Custom Astro toggle styling */
.toggle-premium {
  background-image: linear-gradient(
    to right,
    rgba(var(--muted-rgb), 0.5),
    rgba(var(--muted-rgb), 0.7)
  );
  border: 1px solid rgba(var(--primary-rgb), 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-premium::after {
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#concierge-toggle:checked + .toggle-premium {
  background-image: linear-gradient(to right, rgba(40, 93, 166, 0.8), #285da6);
  border-color: rgba(40, 93, 166, 0.3);
  box-shadow: 0 2px 8px rgba(40, 93, 166, 0.25);
}

#concierge-toggle:checked + .toggle-premium::after {
  box-shadow: 0 1px 4px rgba(40, 93, 166, 0.3);
}

.toggle-premium:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#concierge-toggle:checked + .toggle-premium:hover {
  box-shadow: 0 2px 12px rgba(40, 93, 166, 0.35);
}

/* Focus state for accessibility */
.toggle-premium:focus-visible {
  outline: 2px solid rgba(40, 93, 166, 0.5);
  outline-offset: 2px;
}
