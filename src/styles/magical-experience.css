/* Magical Experience Styles */

/* Premium CTA Button */
.premium-cta-button {
  background: linear-gradient(135deg, #285DA6 0%, #1e4a8d 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 20px rgba(40, 93, 166, 0.3);
  transition: all 0.3s ease;
  transform: translateY(0);
}

.premium-cta-button:hover {
  background: linear-gradient(135deg, #1e4a8d 0%, #285DA6 100%);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25), 0 0 30px rgba(40, 93, 166, 0.4);
  transform: translateY(-2px);
}

.premium-cta-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2), 0 0 15px rgba(40, 93, 166, 0.3);
}

/* Ask Button Animation */
@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 93, 166, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(40, 93, 166, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 93, 166, 0);
  }
}

.search-button-premium.animate-pulse {
  animation: pulse-glow 1.5s infinite;
}

/* Typing Animation */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  background-color: currentColor;
  margin-left: 2px;
  animation: blink 1s step-end infinite;
}

/* Add a blinking cursor to the input field during typing */
.concierge-input.typing {
  caret-color: transparent; /* Hide the default cursor */
  position: relative;
}

.concierge-input.typing::after {
  content: '|';
  position: absolute;
  right: -4px;
  animation: blink 1s step-end infinite;
  color: #285DA6;
  font-weight: bold;
}
