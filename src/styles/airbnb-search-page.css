/* Airbnb-style Search Page */
.airbnb-search-page {
  padding: 24px 0;
}

/* Search Mode Toggle */
.search-mode-toggle {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;
}

/* Search Results Container */
.search-results-container {
  margin-top: 16px;
}

/* AI Search Mode */
.ai-search-results {
  display: flex;
  gap: 24px;
}

.conversation-container {
  flex: 0 0 40%;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(53, 102, 171, 0.1);
  overflow: hidden;
  height: calc(100vh - 200px);
  min-height: 600px;
}

.featured-hotel-container {
  flex: 0 0 60%;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(53, 102, 171, 0.1);
  padding: 24px;
  height: fit-content;
}

.featured-hotel-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #222;
  font-family: "Baskervville", serif;
}

.featured-hotel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.featured-hotel-cta {
  margin-top: 16px;
  padding: 16px;
  background-color: #f7f7f7;
  border-radius: 12px;
}

.featured-hotel-cta p {
  margin-bottom: 16px;
  font-size: 14px;
  color: #555;
}

.book-now-button {
  display: block;
  width: 100%;
  padding: 12px 24px;
  background: linear-gradient(
    135deg,
    #3566ab 0%,
    rgba(53, 102, 171, 0.85) 100%
  );
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.book-now-button:hover {
  background: linear-gradient(
    135deg,
    #3566ab 0%,
    rgba(53, 102, 171, 0.95) 100%
  );
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.2);
}

.no-featured-hotel {
  padding: 32px;
  text-align: center;
  color: #717171;
}

/* Regular Search Mode */
.regular-search-results {
  width: 100%;
}

.hotel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(53, 102, 171, 0.2);
  border-radius: 50%;
  border-top-color: #3566ab;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-container p {
  font-size: 16px;
  color: #555;
}

/* Error State */
.error-container {
  padding: 32px;
  text-align: center;
  color: #e53e3e;
}

/* No Results */
.no-results {
  padding: 32px;
  text-align: center;
}

.no-results h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #222;
}

.no-results p {
  font-size: 16px;
  color: #555;
  margin-bottom: 24px;
}

.featured-destinations {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

.featured-destination {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.featured-destination:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.featured-destination img {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.featured-destination h4 {
  padding: 12px 12px 4px;
  font-size: 16px;
  font-weight: 600;
  color: #222;
}

.featured-destination p {
  padding: 0 12px 12px;
  font-size: 14px;
  color: #555;
  margin: 0;
}

/* Hotel card styles removed - using HotelCard component */

/* Responsive */
@media (max-width: 992px) {
  .ai-search-results {
    flex-direction: column;
  }

  .conversation-container,
  .featured-hotel-container {
    flex: 0 0 100%;
  }

  .conversation-container {
    height: 500px;
    min-height: auto;
  }
}

@media (max-width: 768px) {
  .hotel-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .featured-destinations {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 576px) {
  .hotel-grid {
    grid-template-columns: 1fr;
  }

  .featured-destinations {
    grid-template-columns: 1fr 1fr;
  }
}
