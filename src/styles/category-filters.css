.category-filters-container {
  width: 100%;
  display: none !important; /* Hide the category filters */
  align-items: center;
  justify-content: space-between;
  padding: 16px 80px;
  border-bottom: 1px solid #ebebeb;
  background-color: white;
  position: relative;
}

.category-filters-scroll {
  display: flex;
  gap: 32px;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding-bottom: 4px;
  flex: 1;
}

.category-filters-scroll::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.category-filter-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  min-width: 56px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #717171;
  transition: color 0.2s;
  position: relative;
}

.category-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3566ab;
  transition: color 0.2s;
}

.category-name {
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  transition: color 0.2s;
}

.category-filter-item::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: transparent;
  transition: background-color 0.2s;
}

.category-filter-item.active {
  color: #3566ab;
}

.category-filter-item.active .category-icon,
.category-filter-item.active .category-name {
  color: #3566ab;
}

.category-filter-item.active::after {
  background-color: #3566ab;
}

.category-filter-item:hover {
  color: #3566ab;
}

.category-filter-item:hover .category-icon,
.category-filter-item:hover .category-name {
  color: #3566ab;
}

.filters-button-container {
  margin-left: 24px;
  border-left: 1px solid #ebebeb;
  padding-left: 24px;
}

.filters-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #dddddd;
  border-radius: 12px;
  background-color: white;
  font-size: 12px;
  font-weight: 600;
  color: #222222;
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.filters-button:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (max-width: 1200px) {
  .category-filters-container {
    padding: 16px 40px;
  }
}

@media (max-width: 992px) {
  .category-filters-container {
    padding: 16px 24px;
  }
}

@media (max-width: 768px) {
  .category-filters-container {
    padding: 16px 16px;
  }

  .category-filters-scroll {
    gap: 24px;
  }

  .filters-button-container {
    margin-left: 16px;
    padding-left: 16px;
  }
}
