/* Hero Section Styles */
.hero-container {
  background-color: var(--background);
  color: var(--foreground);
  min-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: url("/images/st-anton-powder-skiing.jpg") no-repeat center center;
  background-size: cover;
  background-position: center;
  border-radius: 0.5rem; /* Match rounded-lg from other hero sections */
  width: 100%;
  height: 100%;
}

.hero-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.5) 100%
  );
  z-index: 1;

  /* Add subtle animated snow effect */
  background-image: radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.15) 0%,
      transparent 1px
    ),
    radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 1px
    );
  background-size: 40px 40px, 80px 80px;
  animation: snowfall 20s linear infinite;
}

@keyframes snowfall {
  0% {
    background-position: 0 0, 0 0;
  }
  100% {
    background-position: 40px 40px, 80px 80px;
  }
}

.hero-content {
  width: 100%;
  max-width: 1440px;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  z-index: 10;
  position: relative;
}

/* Match container-custom margins at different breakpoints */
@media (min-width: 768px) {
  .hero-content {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

@media (min-width: 1024px) {
  .hero-content {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

@media (min-width: 1280px) {
  .hero-content {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 300;
  margin-bottom: 1.5rem;
  margin-top: 3rem;
  padding-top: 4rem; /* Increased padding for better positioning with transparent header */
  line-height: 1.2;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  font-family: "Funktional Grotesk", "Karla", sans-serif;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6), 0 0 1px rgba(0, 0, 0, 0.8);
}

.luxury-accent {
  background: linear-gradient(
    to right,
    #3566ab 0%,
    rgba(83, 142, 221, 0.9) 50%,
    #3566ab 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 300;
  position: relative;
  display: inline-block;
  text-shadow: none;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
}

.luxury-accent::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent 0%,
    rgba(255, 255, 255, 0.8) 50%,
    transparent 100%
  );
}

.hero-subtitle {
  font-size: 1.25rem;
  font-weight: 300;
  margin-bottom: 3rem;
  color: rgba(255, 255, 255, 0.95);
  max-width: 650px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  font-family: "Funktional Grotesk", "Baskervville", serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0 0 1px rgba(0, 0, 0, 0.7);
}

.search-container {
  max-width: 750px;
  width: 100%;
  margin: 0 auto 2.5rem;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.4));
  min-height: 60px; /* Add minimum height to prevent layout shifts */
  padding-bottom: 0; /* Remove excess padding */
}

/* AI Search Container */
.ai-search-container {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 1.5rem;
  padding: 0.75rem 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
  transform-origin: center;
}

.search-inner-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: flex-end;
}

.search-input-wrapper {
  display: flex;
  align-items: flex-start;
  width: 100%;
  flex: 1;
}

.textarea-container {
  flex: 1;
  width: 100%;
  margin-right: 0.75rem;
}

.ai-search-container:hover {
  background-color: white;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px) scale(1.01);
}

.ai-search-container:focus-within {
  background-color: white;
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.35);
  transform: translateY(-4px) scale(1.03);
  padding: 1rem 1.75rem;
}

.ai-search-container.is-typing {
  border: none;
}

.ai-shimmer {
  position: relative;
  overflow: hidden;
}

.ai-shimmer::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(var(--primary-rgb), 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: shimmer 4s infinite;
  transform: skewX(-20deg);
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 200%;
  }
}

.ai-mode-active {
  border-color: rgba(53, 102, 171, 0.3) !important;
  box-shadow: 0 8px 30px rgba(53, 102, 171, 0.15) !important;
}

/* Search input styles */

.ai-search-input {
  background: transparent;
  border: none;
  color: var(--foreground);
  font-size: 0.875rem;
  width: 100%;
  padding: 0.5rem 0;
  outline: none !important;
  font-family: "Baskervville", serif;
  resize: none;
  line-height: 1.5;
  min-height: 3rem; /* Room for 2 lines */
  max-height: 150px;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: none !important;
  overflow-y: auto;
  overflow-x: hidden;
}

.ai-search-container:focus-within .ai-search-input {
  font-size: 0.875rem;
  min-height: 3rem;
}

.ai-search-input::placeholder {
  color: color-mix(in srgb, var(--foreground) 50%, transparent);
  font-style: italic;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.ai-search-input:focus::placeholder {
  opacity: 0.5;
  color: #9a9a9a; /* Soft grey color for the placeholder when focused */
}

.ai-search-input:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
}

.ai-search-button {
  background: linear-gradient(
    135deg,
    #3566ab 0%,
    rgba(53, 102, 171, 0.85) 100%
  );
  border: none;
  height: 2.5rem;
  width: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  margin-left: auto;
  margin-right: 0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.25),
    inset 0 1px 1px rgba(255, 255, 255, 0.15);
  flex-shrink: 0;
  align-self: flex-end;
  margin-bottom: 0.25rem;
}

.send-icon {
  color: white;
  width: 18px;
  height: 18px;
  transform: translateX(-1px);
}

.ai-search-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.35),
    inset 0 1px 2px rgba(255, 255, 255, 0.25);
  filter: brightness(1.05);
}

.ai-search-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background-color: rgba(53, 102, 171, 0.7);
}

.ai-search-button.animate-pulse {
  animation: button-pulse 1.5s infinite;
}

@keyframes button-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(53, 102, 171, 0.5);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(53, 102, 171, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(53, 102, 171, 0);
  }
}

.ai-search-button.animate-pulse .magic-wand-icon {
  animation: wand-pulse 1.5s infinite;
}

@keyframes wand-pulse {
  0% {
    transform: rotate(0deg);
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.7));
  }
  50% {
    transform: rotate(15deg);
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.9));
  }
  100% {
    transform: rotate(0deg);
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.7));
  }
}

/* Search Suggestions */
.search-suggestions {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  width: 100%;
}

.suggestion-tabs {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.suggestion-tab {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 1);
  font-size: 0.7rem;
  font-weight: 700;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  font-family: "Karla", sans-serif;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
  padding: 0.3rem 0.8rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.suggestion-tab::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.5);
  transition: width 0.2s ease;
}

.suggestion-tab:hover {
  color: rgba(255, 255, 255, 1);
  background: rgba(0, 0, 0, 0.3);
  transform: translateY(-1px);
}

.suggestion-tab:hover::after {
  width: 70%;
}

.active-tab {
  color: rgba(255, 255, 255, 1);
  background: rgba(53, 102, 171, 0.8);
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.5);
  transform: translateY(-1px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  font-weight: 700;
}

.active-tab::after {
  width: 80%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.9),
    transparent
  );
  height: 2px;
}

.tab-icon {
  color: rgba(255, 255, 255, 0.9);
}

.suggestion-terms {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 0.5rem;
  animation: fadeIn 0.3s ease;
}

.suggestion-term {
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 0.4rem 0.85rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 1);
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: "Karla", sans-serif;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.02em;
}

.suggestion-term:hover {
  background-color: rgba(53, 102, 171, 0.7);
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.35);
  border-color: rgba(255, 255, 255, 0.35);
}

.suggestion-term:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Guide Container */
.guide-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: 650px;
  align-items: center;
  z-index: 10;
}

/* Guide Buttons */
.guide-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
}

.guide-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 500;
  font-family: "Karla", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  min-width: 160px;
}

.guide-me {
  background-color: white;
  color: #333;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.guide-me:hover {
  background-color: #f8f8f8;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.surprise-me {
  background-color: #3566ab;
  color: white;
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.3);
}

.surprise-me:hover {
  background-color: #2a5595;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(53, 102, 171, 0.4);
}

/* Options Container */
.options-container {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 650px;
  animation: fadeIn 0.3s ease;
  margin-bottom: 1rem;
  position: relative;
  z-index: 5;
  border: 1px solid rgba(53, 102, 171, 0.15);
  max-height: 350px; /* Slightly reduced height */
  overflow-y: auto; /* Add scrolling for overflow content */
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.25rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background-color: #f8f8f8;
  position: sticky;
  top: 0;
  z-index: 10;
  pointer-events: auto;
}

.options-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #333;
}

.header-close-button {
  background: none;
  border: none;
  color: #666;
  font-size: 1.75rem;
  line-height: 1;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.header-close-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

/* Header styling */

.options-section {
  padding: 0.75rem 1.25rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.options-section:last-child {
  border-bottom: none;
}

.options-section h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
}

.chip-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.4rem;
}

.chip-option {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.4rem 0.75rem;
  background-color: #f5f5f5;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  font-size: 0.8125rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chip-option:hover {
  background-color: #eee;
}

.chip-selected {
  background-color: rgba(53, 102, 171, 0.1);
  border-color: rgba(53, 102, 171, 0.3);
  color: #3566ab;
  font-weight: 500;
}

.chip-selected:hover {
  background-color: rgba(53, 102, 171, 0.15);
}

.chip-icon {
  font-size: 0.875rem;
}

.options-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  background-color: #f8f8f8;
}

.close-options-button {
  background-color: #3566ab;
  color: white;
  border: none;
  border-radius: 2rem;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(53, 102, 171, 0.3);
}

.close-options-button:hover {
  background-color: #2a5595;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(53, 102, 171, 0.4);
}

.close-options-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(53, 102, 171, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Selected Filters */
.selected-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.5rem 0;
  width: 100%;
  justify-content: center;
  position: relative;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  background-color: #3566ab;
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 2rem;
  font-size: 0.8125rem;
  box-shadow: 0 2px 4px rgba(53, 102, 171, 0.3);
  border: 1px solid #3566ab;
}

.tag-icon {
  margin-right: 0.4rem;
  font-size: 0.875rem;
}

.remove-tag {
  background: none;
  border: none;
  color: white;
  font-size: 1.125rem;
  line-height: 1;
  padding: 0 0 0 0.3rem;
  cursor: pointer;
  opacity: 0.7;
  margin-left: 0.2rem;
  font-weight: 300;
}

.remove-tag:hover {
  opacity: 1;
}

.edit-filters {
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #666;
  font-size: 0.875rem;
  padding: 0.4rem 0.75rem;
  border-radius: 2rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-filters:hover {
  background-color: #f5f5f5;
  color: #333;
}

/* Category Buttons */
.category-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 0.5rem;
  max-width: 100%;
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeIn 0.3s ease;
}

.category-button {
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  color: #333;
  font-size: 0.875rem;
  font-weight: 500;
  font-family: "Karla", sans-serif;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  height: 2.5rem;
}

.category-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(53, 102, 171, 0.05) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-button:hover {
  background-color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.category-button-selected {
  background-color: #3566ab;
  color: white;
  border-color: #3566ab;
  box-shadow: 0 2px 6px rgba(53, 102, 171, 0.3);
}

.category-button-selected:hover {
  background-color: #2a5595;
}

/* Subcategory Buttons */
.subcategory-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 1.25rem;
  animation: fadeIn 0.6s cubic-bezier(0.25, 1, 0.5, 1) forwards;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  padding: 0.5rem;
  position: relative;
}

.subcategory-buttons::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(53, 102, 171, 0.3),
    transparent
  );
  border-radius: 2px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.subcategory-button {
  background-color: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  padding: 0.45rem 1rem;
  color: #444;
  font-size: 0.8rem;
  font-weight: 500;
  font-family: "Karla", sans-serif;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  backdrop-filter: blur(5px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.subcategory-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(53, 102, 171, 0.03) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.subcategory-button:hover {
  background-color: rgba(255, 255, 255, 0.95);
  border-color: rgba(53, 102, 171, 0.25);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 5px 15px rgba(53, 102, 171, 0.12);
}

.subcategory-button:hover::after {
  opacity: 1;
}

.subcategory-button:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.08);
}

.subcategory-button-selected {
  background-color: white;
  border-color: rgba(53, 102, 171, 0.4);
  box-shadow: 0 5px 15px rgba(53, 102, 171, 0.15);
  transform: translateY(-2px);
  position: relative;
  color: #3566ab;
  font-weight: 600;
  animation: subSelectPulse 0.5s ease-out;
}

@keyframes subSelectPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(53, 102, 171, 0.4);
    transform: translateY(0);
  }
  50% {
    box-shadow: 0 0 8px 2px rgba(53, 102, 171, 0.2);
    transform: translateY(-3px) scale(1.03);
  }
  100% {
    box-shadow: 0 5px 15px rgba(53, 102, 171, 0.15);
    transform: translateY(-2px);
  }
}

.subcategory-button-selected::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(53, 102, 171, 0.3),
    rgba(53, 102, 171, 0.7),
    rgba(53, 102, 171, 0.3)
  );
  border-radius: 0 0 2px 2px;
}

.subcategory-button-selected::after {
  background: linear-gradient(
    135deg,
    rgba(53, 102, 171, 0.08) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  opacity: 1;
}

.category-icon {
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .search-container {
    max-width: 95%;
  }

  .ai-search-container {
    padding: 0.75rem 1rem;
  }

  .ai-search-container:focus-within {
    padding: 0.75rem 1rem;
  }

  .ai-search-input {
    font-size: 0.8125rem;
    min-height: 2.5rem;
  }

  .ai-search-container:focus-within .ai-search-input {
    font-size: 0.8125rem;
  }

  .ai-search-button {
    height: 2.25rem;
    width: 2.25rem;
  }

  .send-icon {
    width: 16px;
    height: 16px;
  }

  .search-icon-wrapper {
    margin-right: 0.5rem;
  }

  .textarea-container {
    margin-right: 0.5rem;
  }

  .search-suggestions {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    align-items: flex-start;
  }

  .suggestion-tabs {
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    justify-content: flex-start;
  }

  .suggestion-tab {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.25);
  }

  .suggestion-terms {
    gap: 0.4rem;
  }

  .suggestion-term {
    padding: 0.35rem 0.7rem;
    font-size: 0.7rem;
    background-color: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.25);
  }

  .guide-buttons {
    flex-direction: column;
    width: 100%;
    max-width: 300px;
  }

  .guide-button {
    width: 100%;
  }

  .options-container {
    border-radius: 0.75rem;
  }

  .chip-options {
    gap: 0.4rem;
  }

  .chip-option {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
  }
}

/* Hero section container - special handling for mobile */
.hero-section-container {
  position: relative;
}

@media (max-width: 768px) {
  /* Remove padding for hero section container in mobile view */
  .hero-section-container {
    padding-left: 0;
    padding-right: 0;
  }

  .hero-section-container .hero-container {
    border-radius: 0;
  }

  .hero-title {
    font-size: 2.5rem;
    font-weight: 300;
    font-family: "Funktional Grotesk", "Karla", sans-serif;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
  }

  .luxury-accent::after {
    bottom: -3px;
  }

  .hero-subtitle {
    font-size: 1rem;
    max-width: 90%;
  }

  /* Hero search container styles for mobile */
  .hero-search-container {
    width: 100%;
    max-width: 100%;
    margin-top: 1.5rem;
  }

  /* Make sure the expandable search is visible in hero section */
  .hero-content .expandable-search-wrapper {
    display: block !important;
    width: 100%;
    max-width: 100%;
  }

  .hero-content .expanded-search-wrapper {
    width: 100%;
    max-width: 100%;
  }

  .hero-content .airbnb-header-search {
    display: flex !important;
    width: 100%;
    max-width: 100%;
    flex-direction: column;
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 1rem;
  }

  .hero-content .search-section {
    width: 100%;
    padding: 10px;
    border-bottom: 1px solid #eee;
  }

  .hero-content .ai-search-input-section {
    width: 100%;
    padding: 10px;
  }

  .hero-content .ai-search-input {
    width: 100%;
    padding: 10px;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: white;
    font-size: 16px;
  }

  .hero-content .search-button {
    margin: 10px auto;
    width: 90%;
    border-radius: 24px;
    height: 40px;
  }

  .ai-search-container {
    padding: 0.6rem 1.25rem;
  }

  .ai-search-container:focus-within {
    padding: 0.6rem 1.5rem;
  }

  .chip-categories {
    gap: 1rem;
  }

  .chip-category {
    padding: 0.5rem 0;
  }

  .chip-category-title {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
  }

  .category-buttons {
    gap: 0.6rem;
  }

  .category-button {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
    font-weight: 300;
    letter-spacing: 0.05em;
    font-family: "Funktional Grotesk", "Karla", sans-serif;
    margin-top: 1rem;
    padding-top: 1rem;
  }

  .hero-subtitle {
    font-size: 0.95rem;
    margin-bottom: 2rem;
  }

  .ai-search-input {
    font-size: 1rem;
    min-height: 1.5rem;
    padding: 0.25rem 0;
  }

  .ai-search-container:focus-within .ai-search-input {
    font-size: 1.125rem;
    min-height: 1.5rem;
  }

  .ai-search-container:focus-within .ai-icon {
    top: 0;
    margin-top: 0.2rem;
  }

  .ai-search-button {
    height: 2.5rem;
    padding: 0 1rem;
  }

  .button-text {
    font-size: 0.8rem;
  }

  .search-suggestions {
    margin-top: 0.4rem;
    margin-bottom: 0.4rem;
    align-items: flex-start;
  }

  .suggestion-tabs {
    gap: 0.4rem;
    margin-bottom: 0.4rem;
    justify-content: flex-start;
  }

  .suggestion-tab {
    font-size: 0.6rem;
    padding: 0.25rem 0.5rem;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 1);
  }

  .active-tab {
    background: rgba(53, 102, 171, 0.9);
  }

  .suggestion-terms {
    gap: 0.35rem;
  }

  .suggestion-term {
    padding: 0.3rem 0.6rem;
    font-size: 0.65rem;
    border-radius: 0.75rem;
    background-color: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 1);
  }

  .chip-categories {
    gap: 0.75rem;
  }

  .chip-category {
    padding: 0.5rem 0;
  }

  .chip-category-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .category-buttons {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .category-button {
    width: auto;
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
}
