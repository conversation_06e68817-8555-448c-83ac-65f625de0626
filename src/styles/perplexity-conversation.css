/* Perplexity-style Conversation Component */

.perplexity-ai-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background-color: transparent;
  display: flex;
  flex-direction: column;
  padding-bottom: 80px; /* Space for the fixed input bar */
  padding-left: 32px;
  padding-right: 32px;
  margin-top: 200px; /* Increased margin to account for header height and ensure content is visible */
  min-height: calc(100vh - 168px - 24px); /* Minimum height to ensure it fills the viewport */
}

/* Conversation section */
.perplexity-conversation {
  border-bottom: 1px solid rgba(53, 102, 171, 0.08);
  margin-bottom: 32px;
  padding-bottom: 28px;
  scroll-margin-top: 200px; /* Increased to match the container's top margin */
  background-color: transparent;
  transition: all 0.3s ease;
  padding-top: 20px; /* Added padding at the top for better spacing */
}

.perplexity-conversation:hover {
  background-color: rgba(250, 250, 252, 0.7);
}

.perplexity-conversation:first-child {
  padding-top: 0; /* Remove padding for first conversation */
}

.perplexity-conversation:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* Conversation header */
.perplexity-conversation-header {
  font-family: 'Karla', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #1b1b1b;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(53, 102, 171, 0.08);
  line-height: 1.3;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.perplexity-conversation-header::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 20px;
  background: linear-gradient(to bottom, #3566ab, #4b7bc2);
  border-radius: 2px;
  margin-right: 4px;
}

/* Tabs for Answer, Images, Sources, Tasks */
.perplexity-tabs {
  display: flex;
  border-bottom: 1px solid rgba(53, 102, 171, 0.08);
  background-color: transparent;
  padding: 0;
  margin-bottom: 16px;
  position: relative;
}

.perplexity-tab {
  padding: 8px 16px;
  font-family: 'Karla', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
}

.perplexity-tab.active {
  color: #3566ab;
  font-weight: 600;
}

.perplexity-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #3566ab;
}

.perplexity-tab:hover:not(.active) {
  color: #3566ab;
}

/* Content area */
.perplexity-content {
  font-family: 'Karla', sans-serif;
  font-size: 15px;
  line-height: 1.6;
  color: #333;
  padding: 0 0 16px 0;
}

.perplexity-content p {
  margin-bottom: 14px;
}

.perplexity-content p:last-child {
  margin-bottom: 0;
}

/* Section headings */
.perplexity-section-heading {
  font-family: 'Karla', sans-serif;
  font-size: 17px;
  font-weight: 600;
  color: #1b1b1b;
  margin: 24px 0 12px;
  padding-bottom: 6px;
  position: relative;
  display: flex;
  align-items: center;
}

/* Hotel list */
.perplexity-hotel-list {
  list-style-type: none;
  padding-left: 0;
  margin: 16px 0;
}

.perplexity-hotel-list li {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 8px;
  background-color: rgba(53, 102, 171, 0.02);
  border: 1px solid rgba(53, 102, 171, 0.06);
  transition: all 0.2s ease;
}

.perplexity-hotel-list li:hover {
  background-color: rgba(53, 102, 171, 0.03);
  border-color: rgba(53, 102, 171, 0.1);
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.05);
}

.perplexity-hotel-list h3 {
  font-family: 'Karla', sans-serif;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 6px;
  color: #3566ab;
}

.perplexity-hotel-list p {
  margin-bottom: 8px;
  color: #555;
  line-height: 1.5;
  font-size: 14px;
}

/* Footer actions */
.perplexity-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid rgba(53, 102, 171, 0.05);
}

.perplexity-action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 14px;
  border-radius: 8px;
  background-color: rgba(53, 102, 171, 0.03);
  border: 1px solid rgba(53, 102, 171, 0.08);
  font-family: 'Karla', sans-serif;
  font-size: 13px;
  font-weight: 500;
  color: #3566ab;
  cursor: pointer;
  transition: all 0.2s ease;
}

.perplexity-action-button:hover {
  background-color: rgba(53, 102, 171, 0.05);
  border-color: rgba(53, 102, 171, 0.12);
}

/* Source citations */
.perplexity-sources {
  display: flex;
  gap: 6px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.perplexity-source {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 3px 8px;
  background-color: #f5f5f7;
  border-radius: 12px;
  font-family: 'Karla', sans-serif;
  font-size: 12px;
  color: #666;
  border: 1px solid rgba(0, 0, 0, 0.03);
  transition: all 0.2s ease;
}

.perplexity-source:hover {
  background-color: #eaeaec;
  border-color: rgba(53, 102, 171, 0.1);
  color: #3566ab;
}

.perplexity-source-icon {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 9px;
  color: #666;
}

/* Input area */
.perplexity-input-container {
  padding: 10px 32px;
  border-top: 1px solid rgba(53, 102, 171, 0.08);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.98);
}

.perplexity-input-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  pointer-events: none;
}

.perplexity-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: white;
  border-radius: 12px;
  padding: 8px 12px;
  transition: all 0.2s ease;
  border: 1px solid rgba(53, 102, 171, 0.12);
  max-width: 1000px;
  margin: 0 auto;
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.05);
  width: 100%;
}

.perplexity-input-wrapper:focus-within {
  background-color: white;
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.08);
  border-color: rgba(53, 102, 171, 0.2);
}

.perplexity-input {
  flex: 1;
  border: none;
  background: transparent;
  font-family: 'Karla', sans-serif;
  font-size: 15px;
  line-height: 1.5;
  resize: none;
  padding: 0;
  height: 24px;
  min-height: 24px;
  outline: none;
  color: #1b1b1b;
}

.perplexity-input::placeholder {
  color: #888;
  font-style: italic;
}

.perplexity-send-button {
  background: linear-gradient(135deg, #3566ab, #4b7bc2);
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(53, 102, 171, 0.15);
}

.perplexity-send-button:hover {
  background: linear-gradient(135deg, #2a5595, #3566ab);
  box-shadow: 0 3px 8px rgba(53, 102, 171, 0.2);
}

.perplexity-send-button:active {
  transform: scale(0.95);
}

.perplexity-send-button:disabled {
  background: linear-gradient(135deg, #c3c3c3, #d5d5d5);
  box-shadow: none;
  cursor: not-allowed;
}

/* Messages container */
.perplexity-messages {
  flex: 1;
  padding: 0 0 80px 0; /* Removed top padding since we're using margin-top on the container */
  display: flex;
  flex-direction: column;
  gap: 20px;
  background-color: transparent;
  width: 100%;
}

/* Loading state */
.perplexity-typing {
  display: flex;
  gap: 4px;
  padding: 8px 0;
}

.perplexity-typing-dot {
  width: 8px;
  height: 8px;
  background-color: #3566ab;
  border-radius: 50%;
  opacity: 0.6;
  animation: perplexity-typing 1.4s infinite ease-in-out both;
}

.perplexity-typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.perplexity-typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes perplexity-typing {
  0%, 80%, 100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .perplexity-messages {
    padding: 0 16px 70px 16px;
  }

  .perplexity-input-container {
    padding: 10px 16px;
  }

  .perplexity-conversation-header {
    font-size: 17px;
  }

  .perplexity-tab {
    padding: 8px 12px;
    font-size: 13px;
  }

  .perplexity-ai-container {
    padding-left: 16px;
    padding-right: 16px;
    margin-top: 180px; /* Slightly reduced top margin for mobile */
  }

  .perplexity-conversation {
    scroll-margin-top: 180px; /* Match the container's top margin for mobile */
  }
}
