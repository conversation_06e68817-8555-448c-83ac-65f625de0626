/* Luxurious language selector animations */
@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-down {
  animation: slide-in 0.2s ease-out forwards;
}

/* Hover animation for language items */
.language-item-hover {
  transition: all 0.3s ease;
}

.language-item-hover:hover {
  background-color: rgba(40, 93, 166, 0.05);
  padding-left: 1.25rem;
  color: #285DA6;
}

/* Subtle shimmer effect for selected language */
@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.language-selected {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(40, 93, 166, 0.05) 25%,
    rgba(255, 255, 255, 1) 50%,
    rgba(40, 93, 166, 0.05) 75%,
    rgba(255, 255, 255, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 3s infinite linear;
  border-left: 2px solid #285DA6;
}

/* Footer menu hover effect */
.footer-menu-item {
  transition: all 0.3s ease;
  display: inline-block;
}

.footer-menu-item:hover {
  transform: scale(1.05);
  text-shadow: 0 0 1px rgba(40, 93, 166, 0.2);
}

/* Header menu hover effect */
.header-menu-item {
  transition: all 0.3s ease;
  display: inline-block;
}

.header-menu-item:hover {
  transform: scale(1.05);
  text-shadow: 0 0 1px rgba(40, 93, 166, 0.2);
}
