/* Web Stories Viewer Styles */
.web-stories-viewer {
  position: relative;
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0;
  overflow: hidden;
}

.web-stories-controls {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding: 0 1rem;
  position: relative;
  z-index: 100;
}

.web-stories-nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(40, 93, 166, 0.1) 0%, rgba(40, 93, 166, 0.2) 100%);
  border: 1px solid rgba(40, 93, 166, 0.2);
  color: #285DA6;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 100; /* Ensure buttons are above other elements */
  -webkit-tap-highlight-color: rgba(0,0,0,0); /* Remove tap highlight on mobile */
  touch-action: manipulation; /* Improve touch behavior */
  position: relative; /* Ensure z-index works */
}

.web-stories-nav-button:hover {
  background: linear-gradient(135deg, rgba(40, 93, 166, 0.2) 0%, rgba(40, 93, 166, 0.3) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 93, 166, 0.15);
}

.web-stories-nav-button:active {
  transform: translateY(0);
}

.web-stories-container {
  position: relative;
  height: 500px;
  overflow: visible;
}

.web-story-card {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.6s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  will-change: transform, opacity;
}

.web-story-card.active {
  z-index: 10;
}

.web-story-card:hover {
  transform: translateY(-5px) scale(1.02) !important;
  box-shadow: 0 12px 40px rgba(40, 93, 166, 0.2);
}

.web-story-card-image {
  position: relative;
  width: 100%;
  height: 100%;
}

.web-story-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.7s ease;
}

.web-story-card:hover .web-story-card-image img {
  transform: scale(1.05);
}

.web-story-card-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, transparent 100%);
  z-index: 1;
}

.web-story-card-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2rem;
  z-index: 2;
  color: white;
}

.web-story-card-content h3 {
  font-family: 'Baskervville', serif;
  font-size: 1.75rem;
  font-weight: 400;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.web-story-card-content p {
  font-family: 'Karla', sans-serif;
  font-size: 1rem;
  margin: 0 0 1rem 0;
  max-width: 80%;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.web-story-card-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(40, 93, 166, 0.8);
  border-radius: 4px;
  font-family: 'Karla', sans-serif;
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.web-stories-indicators {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
  position: relative;
  z-index: 100;
  padding: 0.5rem 0;
}

.web-stories-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(40, 93, 166, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
  touch-action: manipulation;
  position: relative;
  z-index: 100;
}

.web-stories-indicator.active {
  width: 32px;
  border-radius: 6px;
  background-color: #285DA6;
}

/* Web Story Modal */
.web-story-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.web-story-modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  transition: all 0.3s ease;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
  touch-action: manipulation;
}

.web-story-modal-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.web-story-iframe {
  width: 100%;
  max-width: 414px; /* Standard mobile width */
  height: 100%;
  max-height: 90vh;
  border: none;
  border-radius: 8px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .web-stories-container {
    height: 400px;
  }

  .web-story-card-content h3 {
    font-size: 1.5rem;
  }

  .web-story-card-content p {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .web-stories-container {
    height: 350px;
  }

  .web-stories-controls {
    padding: 0 0.5rem;
    margin-bottom: 0.75rem;
  }

  .web-stories-nav-button {
    width: 48px;
    height: 48px;
  }

  .web-story-card-content {
    padding: 1.5rem;
  }

  .web-story-card-content h3 {
    font-size: 1.25rem;
  }

  .web-story-card-content p {
    font-size: 0.75rem;
    max-width: 100%;
  }

  .web-stories-indicators {
    gap: 1.25rem;
    padding: 0.75rem 0;
  }

  .web-stories-indicator {
    width: 14px;
    height: 14px;
  }

  .web-stories-indicator.active {
    width: 36px;
  }

  /* Add touch-friendly padding around clickable elements */
  .web-story-card {
    touch-action: manipulation;
  }
}

/* Premium luxury enhancements */
.web-story-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  z-index: 3;
  pointer-events: none;
}

.web-story-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #285DA6, rgba(40, 93, 166, 0.5));
  z-index: 4;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.web-story-card:hover::after {
  opacity: 1;
}
