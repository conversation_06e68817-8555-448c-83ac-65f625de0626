/* Wrapper for suggestions to separate from search */
.suggestions-wrapper {
  margin-top: 16px;
  width: 100%;
  max-width: 848px;
}

/* WhatsApp Meta AI style search suggestions - Horizontal Layout */
.search-suggestions-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding: 8px 0;
  background-color: transparent;
}

/* Row container with horizontal scroll */
.suggestions-row {
  display: flex;
  gap: 8px;
  width: 100%;
  overflow-x: auto;
  padding-bottom: 4px;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scroll-behavior: smooth;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.suggestions-row::-webkit-scrollbar {
  display: none;
}

/* Premium styled suggestion chips - smaller size */
.suggestion-chip {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(248, 249, 250, 0.9);
  border: 1px solid rgba(53, 102, 171, 0.1);
  border-radius: 16px;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  white-space: nowrap;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
}

.suggestion-chip:hover {
  background-color: rgba(240, 244, 249, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(53, 102, 171, 0.08);
  border-color: rgba(53, 102, 171, 0.2);
}

.suggestion-chip:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(53, 102, 171, 0.08);
}

.suggestion-icon {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
}

.suggestion-text {
  font-size: 13px;
  font-weight: 500;
  color: gray !important;
  line-height: 1.3;
  font-family: "Karla", sans-serif;
  letter-spacing: -0.01em;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .suggestions-wrapper {
    opacity: 0.9;
  }

  .search-suggestions-container {
    background-color: transparent;
  }

  .suggestion-chip {
    background-color: rgba(42, 42, 42, 0.8);
    border-color: rgba(53, 102, 171, 0.15);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  }

  .suggestion-chip:hover {
    background-color: rgba(51, 51, 51, 0.9);
    border-color: rgba(53, 102, 171, 0.3);
    box-shadow: 0 2px 5px rgba(53, 102, 171, 0.2);
  }

  .suggestion-text {
    color: #e4e6eb;
  }

  .suggestion-icon {
    color: #5d8dd6;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .suggestions-wrapper {
    margin-top: 12px;
  }

  .search-suggestions-container {
    padding: 4px 0;
    gap: 6px;
  }

  .suggestions-row {
    gap: 6px;
    padding-bottom: 2px;
  }

  .suggestion-chip {
    padding: 5px 10px;
    border-radius: 14px;
    gap: 6px;
  }

  .suggestion-icon {
    font-size: 14px;
    min-width: 16px;
  }

  .suggestion-text {
    font-size: 12px;
  }
}

/* Styles for the hero section */
.hero-search-container .suggestions-wrapper {
  margin-top: 12px;
  padding: 0 16px;
}

/* When in the header */
.header-wrapper .suggestions-wrapper {
  margin-top: 12px;
  padding: 0 16px;
  display: none !important; /* Hide suggestions in header */
}

/* Only show suggestions in hero section */
.hero-only-suggestions {
  display: block;
}

/* Container for AI search */
.ai-search-container {
  border-radius: 24px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Ensure the form has proper styling */
.ai-search-container .airbnb-header-search {
  border-radius: 24px;
  border: none;
}

/* Premium styling for the suggestions */
.hero-search-container .suggestion-chip,
.header-wrapper .suggestion-chip {
  background-color: rgba(248, 249, 250, 0.85);
  border: 1px solid rgba(53, 102, 171, 0.12);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
}

.hero-search-container .suggestion-chip:hover,
.header-wrapper .suggestion-chip:hover {
  background-color: rgba(240, 244, 249, 0.9);
  border-color: rgba(53, 102, 171, 0.2);
  box-shadow: 0 2px 5px rgba(53, 102, 171, 0.08);
}

/* Active state for suggestion chips */
.suggestion-chip:active {
  background-color: rgba(237, 242, 249, 0.95);
  border-color: rgba(53, 102, 171, 0.25);
  box-shadow: 0 1px 2px rgba(53, 102, 171, 0.1);
  transform: translateY(1px);
}

/* Brand color for icons to make them more premium */
.suggestion-icon {
  color: #3566ab;
}
