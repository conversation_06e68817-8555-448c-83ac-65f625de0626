/* Mobile Menu Styles */
.mobile-menu-open {
  overflow: hidden;
}

@media (max-width: 767px) {
  .mobile-menu-container {
    position: relative;
    z-index: 50;
  }
  
  /* Ensure the mobile menu is above other elements */
  .mobile-menu-panel {
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  }
  
  /* Smooth transitions for menu items */
  .mobile-menu-item {
    opacity: 0;
    transform: translateX(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
  }
  
  .mobile-menu-open .mobile-menu-item {
    opacity: 1;
    transform: translateX(0);
  }
  
  /* Staggered animation for menu items */
  .mobile-menu-item:nth-child(1) { transition-delay: 0.1s; }
  .mobile-menu-item:nth-child(2) { transition-delay: 0.15s; }
  .mobile-menu-item:nth-child(3) { transition-delay: 0.2s; }
  .mobile-menu-item:nth-child(4) { transition-delay: 0.25s; }
  .mobile-menu-item:nth-child(5) { transition-delay: 0.3s; }
}

/* Hamburger button animation */
.hamburger-line {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Ensure the mobile menu doesn't conflict with other fixed elements */
.mobile-menu-overlay {
  backdrop-filter: blur(4px);
}
