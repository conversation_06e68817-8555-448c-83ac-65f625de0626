// @ts-check
import { defineConfig } from "astro/config";
import react from "@astrojs/react";
import tailwind from "@astrojs/tailwind";
import basicSsl from "@vitejs/plugin-basic-ssl";
import vercel from "@astrojs/vercel";

// Always use server output mode for compatibility with Vercel adapter

export default defineConfig({
  output: "server",
  // Add redirects for old URLs
  redirects: {
    "/home": "/",
  },
  integrations: [react(), tailwind()],
  vite: {
    // Add SSL plugin for local development
    plugins: [basicSsl()],
    server: {
      https: true,
    },
  },
  adapter: vercel({
    imageService: true,
    webAnalytics: {
      enabled: true,
    },
    maxDuration: 300,
  }),
});
