<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Perfect Piste Web Stories</title>
  <style>
    body {
      font-family: 'Baskervville', serif;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
      color: #333;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }

    h1 {
      font-size: 2.5rem;
      color: #285DA6;
      margin-bottom: 1.5rem;
      text-align: center;
    }

    p {
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 2rem;
      text-align: center;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }

    .stories-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 2rem;
    }

    .story-card {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      transition: all 0.3s ease;
      background-color: white;
    }

    .story-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 40px rgba(40, 93, 166, 0.2);
    }

    .story-image {
      height: 400px;
      overflow: hidden;
    }

    .story-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.7s ease;
    }

    .story-card:hover .story-image img {
      transform: scale(1.05);
    }

    .story-content {
      padding: 1.5rem;
    }

    .story-title {
      font-size: 1.5rem;
      margin: 0 0 0.5rem 0;
      color: #285DA6;
    }

    .story-description {
      font-size: 1rem;
      color: #666;
      margin: 0 0 1rem 0;
    }

    .story-link {
      display: inline-block;
      padding: 0.75rem 1.5rem;
      background-color: #285DA6;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      transition: all 0.3s ease;
    }

    .story-link:hover {
      background-color: #1e4a8d;
      transform: translateY(-2px);
    }

    @media (max-width: 768px) {
      .stories-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      }

      .story-image {
        height: 300px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Perfect Piste Web Stories</h1>
    <p>Explore our collection of immersive Web Stories showcasing luxury ski experiences, exclusive alpine retreats, and premium mountain adventures.</p>

    <div class="stories-grid">
      <div class="story-card">
        <div class="story-image">
          <img src="https://images.unsplash.com/photo-1548777123-e216912df7d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" alt="Exclusive Alpine Retreats">
        </div>
        <div class="story-content">
          <h2 class="story-title">Exclusive Alpine Retreats</h2>
          <p class="story-description">Discover the most luxurious chalets in the Swiss Alps</p>
          <a href="/stories/exclusive-alpine-retreats/index.html" class="story-link">View Story</a>
        </div>
      </div>

      <div class="story-card">
        <div class="story-image">
          <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" alt="Luxury Après-Ski">
        </div>
        <div class="story-content">
          <h2 class="story-title">Luxury Après-Ski</h2>
          <p class="story-description">The finest dining experiences after a day on the slopes</p>
          <a href="/stories/luxury-apres-ski/index.html" class="story-link">View Story</a>
        </div>
      </div>

      <div class="story-card">
        <div class="story-image">
          <img src="https://images.unsplash.com/photo-1551524559-8af4e6624178?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" alt="Private Powder">
        </div>
        <div class="story-content">
          <h2 class="story-title">Private Powder</h2>
          <p class="story-description">Exclusive access to untouched slopes and hidden gems</p>
          <a href="/stories/sample-web-story-template.html" class="story-link">View Story</a>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
