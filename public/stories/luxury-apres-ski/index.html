<!DOCTYPE html>
<html amp lang="en">
  <head>
    <meta charset="utf-8" />
    <script async src="https://cdn.ampproject.org/v0.js"></script>
    <script async custom-element="amp-story" src="https://cdn.ampproject.org/v0/amp-story-1.0.js"></script>
    <script async custom-element="amp-video" src="https://cdn.ampproject.org/v0/amp-video-0.1.js"></script>

    <title>Luxury Après-Ski Experiences - Perfect Piste</title>
    <meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1" />
    <link rel="canonical" href="https://yourwebsite.com/stories/luxury-apres-ski/" />

    <style amp-boilerplate>body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}</style><noscript><style amp-boilerplate>body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}</style></noscript>

    <style amp-custom>
      amp-story {
        font-family: 'Baskervville', serif;
        color: #fff;
      }

      amp-story-page {
        background-color: #000;
      }

      h1 {
        font-weight: 400;
        font-size: 2.5rem;
        line-height: 1.2;
        margin: 0 0 0.5rem;
      }

      h2 {
        font-weight: 400;
        font-size: 1.5rem;
        line-height: 1.2;
        margin: 0 0 0.5rem;
      }

      p {
        font-family: 'Karla', sans-serif;
        margin: 0;
        font-size: 1rem;
        line-height: 1.5;
      }

      .overlay {
        background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.5) 50%, rgba(0,0,0,0.9) 100%);
        padding: 2rem;
      }

      .button {
        display: inline-block;
        padding: 0.75rem 1.5rem;
        background-color: #285DA6;
        color: white;
        font-family: 'Karla', sans-serif;
        font-weight: bold;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border-radius: 4px;
        text-decoration: none;
      }
    </style>
  </head>
  <body>
    <amp-story
      standalone
      title="Luxury Après-Ski Experiences"
      publisher="Perfect Piste"
      publisher-logo-src="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
      poster-portrait-src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=720&q=80"
    >
      <!-- Cover page -->
      <amp-story-page id="cover">
        <amp-story-grid-layer template="fill">
          <amp-img
            src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1080&q=80"
            width="720"
            height="1280"
            layout="responsive"
            animate-in="fade-in"
            animate-in-duration="1s"
          ></amp-img>
        </amp-story-grid-layer>
        <amp-story-grid-layer template="vertical" class="overlay">
          <div class="overlay-content">
            <h1 animate-in="fly-in-bottom" animate-in-duration="1s" animate-in-delay="0.4s">
              Luxury Après-Ski Experiences
            </h1>
            <p animate-in="fly-in-bottom" animate-in-duration="1s" animate-in-delay="0.6s">
              The finest dining and entertainment after a day on the slopes
            </p>
          </div>
        </amp-story-grid-layer>
      </amp-story-page>

      <!-- Additional pages would go here -->

      <!-- Final CTA page -->
      <amp-story-page id="cta">
        <amp-story-grid-layer template="fill">
          <amp-img
            src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1080&q=80"
            width="720"
            height="1280"
            layout="responsive"
            animate-in="fade-in"
            animate-in-duration="1s"
          ></amp-img>
        </amp-story-grid-layer>
        <amp-story-grid-layer template="vertical" class="overlay">
          <div class="overlay-content">
            <h2 animate-in="fly-in-bottom" animate-in-duration="1s" animate-in-delay="0.4s">
              Discover Exclusive Dining Experiences
            </h2>
            <p animate-in="fly-in-bottom" animate-in-duration="1s" animate-in-delay="0.6s">
              Let us arrange your perfect après-ski experience
            </p>
            <a
              href="/experiences"
              class="button"
              animate-in="fly-in-bottom"
              animate-in-duration="1s"
              animate-in-delay="0.8s"
            >
              Explore Experiences
            </a>
          </div>
        </amp-story-grid-layer>
      </amp-story-page>

      <!-- Story bookend -->
      <amp-story-bookend layout="nodisplay">
        <script type="application/json">
          {
            "bookendVersion": "v1.0",
            "shareProviders": [
              "facebook",
              "twitter",
              "email"
            ],
            "components": [
              {
                "type": "heading",
                "text": "Discover More Luxury Experiences"
              },
              {
                "type": "small",
                "title": "Exclusive Alpine Retreats",
                "url": "/stories/exclusive-alpine-retreats/index.html",
                "image": "https://images.unsplash.com/photo-**********-e216912df7d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
              },
              {
                "type": "cta-link",
                "links": [
                  {
                    "text": "Explore All Experiences",
                    "url": "/experiences"
                  }
                ]
              }
            ]
          }
        </script>
      </amp-story-bookend>
    </amp-story>
  </body>
</html>
