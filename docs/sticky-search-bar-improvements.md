# Sticky Search Bar Improvements

## Overview
This document outlines the necessary changes to enhance the sticky search bar component, making it more modern, premium, and consistent with the overall theme.

## Visual Design Changes

### 1. Typography
- Update label font to use `font-karla`
- Add proper letter spacing with `tracking-wider`
- Use uppercase for labels
- Ensure consistent font sizes across all text elements

```tsx
// Example implementation
<Label 
  htmlFor="concierge-mode" 
  className="text-xs font-karla tracking-wider uppercase text-foreground/80"
>
  Ask concierge
</Label>
```

### 2. Container Styling
- Add layered backdrop blur effect
- Implement premium border styling
- Add subtle shadow effects
- Enhance hover states

```tsx
<div className="rounded-2xl border border-border/60 bg-card/95 backdrop-blur-lg shadow-lg hover:shadow-glow-soft transition-all duration-300">
  {/* Content */}
</div>
```

### 3. Toggle Switch Styling
- Update colors to match theme
- Add smooth transitions
- Enhance interactive states

```tsx
<Switch
  id="concierge-mode"
  checked={isAIMode}
  onCheckedChange={setIsAIMode}
  className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-input transition-colors duration-300"
/>
```

## Animation Improvements

### 1. Entry/Exit Animations
```css
.search-bar-enter {
  opacity: 0;
  transform: translateY(10px);
}

.search-bar-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}
```

### 2. Hover Effects
```css
.search-bar-hover {
  transition: all 300ms ease-out;
}

.search-bar-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(var(--primary-rgb), 0.15);
}
```

## Component Structure Updates

### 1. Main Container
```tsx
<div
  style={{
    bottom: `${cookieConsentHeight + (isMobile ? 12 : 24)}px`,
  }}
  className={`
    fixed 
    left-1/2 
    transform 
    -translate-x-1/2 
    w-[95%] sm:w-[90%] md:w-[85%] lg:w-[75%] xl:w-[65%] 
    z-50 
    transition-all 
    duration-500 
    ease-out
    ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-5"}
  `}
>
```

### 2. Toggle Container
```tsx
<div className="flex justify-end mb-3">
  <div className="flex items-center space-x-2 bg-card/95 backdrop-blur-lg border border-border/60 rounded-full px-4 py-2">
    {/* Toggle content */}
  </div>
</div>
```

## Responsive Design

### 1. Width Adjustments
- Mobile (default): 95%
- SM: 90%
- MD: 85%
- LG: 75%
- XL: 65%

### 2. Spacing Adjustments
- Mobile: 12px from bottom
- Desktop: 24px from bottom

## Implementation Steps

1. **Update Component Structure**
   - Reorganize component hierarchy
   - Add new container elements
   - Implement proper spacing

2. **Add New Styles**
   - Create new CSS classes
   - Update existing styles
   - Add animation classes

3. **Enhance Interactions**
   - Implement hover effects
   - Add transition animations
   - Update toggle behavior

4. **Optimize Performance**
   - Use CSS transforms for animations
   - Implement proper transition timing
   - Optimize backdrop-filter usage

## Testing Checklist

- [ ] Verify typography consistency
- [ ] Test hover animations
- [ ] Check responsive behavior
- [ ] Verify transition smoothness
- [ ] Test backdrop blur performance
- [ ] Verify toggle switch functionality
- [ ] Check shadow effects
- [ ] Test on different screen sizes
- [ ] Verify accessibility
- [ ] Test with different theme colors

## Browser Compatibility

Ensure functionality works in:
- Chrome
- Firefox
- Safari
- Edge
- Mobile browsers

## Accessibility Considerations

1. **Color Contrast**
   - Ensure sufficient contrast for text
   - Verify visibility of toggle states

2. **Focus States**
   - Add visible focus indicators
   - Maintain keyboard navigation

3. **ARIA Labels**
   - Add proper aria-labels
   - Include role attributes

## Future Enhancements

1. **Animation Options**
   - Configurable animation timing
   - Different animation styles
   - Reduced motion support

2. **Theme Integration**
   - Dynamic color adaptation
   - Custom theme support
   - Dark mode optimization

3. **Performance**
   - Lazy loading improvements
   - Animation optimization
   - Reduced CSS bundle

## Dependencies

- TailwindCSS
- Radix UI (for Switch component)
- React
- CSS Modules (optional)

## Notes

- All color values should use theme variables
- Animations should respect user preferences
- Mobile-first approach for all styles
- Maintain consistent spacing scale