# Header Improvements Documentation

## Overview
This document outlines the necessary changes and improvements to the header component of the Perfect Piste website.

## Components to Modify/Create

### 1. Header Component (`src/components/layout/Header.astro`)
- Remove border-bottom
- Reorganize right section to include language selector, wishlist, and cart
- Maintain responsive design

### 2. Language Selector (`src/components/layout/LanguageSelector.tsx`)
#### Design Changes:
- Add globe icon and flag in default view
- Improve dropdown styling
- Add smooth transitions

#### Features:
- Support for multiple languages (EN, FR, DE initially)
- Persistent language selection using localStorage
- Accessible dropdown menu
- Integration with Storyblok for translations (future implementation)

#### Implementation Details:
```typescript
interface Language {
  code: string;
  label: string;
  flag: string;
}

const languages: Language[] = [
  { code: 'en', label: 'English', flag: '🇬🇧' },
  { code: 'fr', label: 'Français', flag: '🇫🇷' },
  { code: 'de', label: 'Deuts<PERSON>', flag: '🇩🇪' },
];
```

#### Storyblok Integration Notes:
- Language selector will handle language switching
- Content translations will be managed through Storyblok
- Implementation will be done in a separate phase
- Current implementation should be structured to easily accommodate Storyblok integration

### 3. Wishlist Feature
#### New Components:
1. `WishlistIcon.tsx` - Header icon component
2. `WishlistContext.tsx` - Global state management
3. `useWishlist.ts` - Custom hook for wishlist operations

#### Storage:
- Use localStorage for persistence
- Structure:
```typescript
interface WishlistItem {
  id: string;
  name: string;
  image: string;
  location: string;
  addedAt: string;
}
```

#### Features:
- Add/remove hotels from wishlist
- Persistent storage
- Count indicator on icon
- Tooltip on hover

### 4. Shopping Cart Feature
#### New Components:
1. `CartIcon.tsx` - Header icon component
2. `CartContext.tsx` - Global state management
3. `useCart.ts` - Custom hook for cart operations

#### Storage:
- Use localStorage for persistence
- Structure:
```typescript
interface CartItem {
  id: string;
  roomType: string;
  hotelName: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  price: number;
  addedAt: string;
}
```

#### Features:
- Add/remove rooms from cart
- Persistent storage
- Count indicator on icon
- Tooltip on hover

## Required CSS Changes

### New Tailwind Classes
Add to `tailwind.config.js`:
```javascript
module.exports = {
  theme: {
    extend: {
      animation: {
        'slide-down': 'slide-down 0.2s ease-out',
      },
      keyframes: {
        'slide-down': {
          '0%': { transform: 'translateY(-10px)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 },
        },
      },
    },
  },
}
```

## Implementation Steps

1. **Header Updates**
   - Update header component layout
   - Add new icon components
   - Ensure responsive design

2. **Language Selector**
   - Implement new design
   - Add language persistence
   - Add animations

3. **Wishlist Feature**
   - Create context and hooks
   - Implement storage logic
   - Add to hotel cards

4. **Cart Feature**
   - Create context and hooks
   - Implement storage logic
   - Add to room booking components

## Mobile Considerations

- Update mobile menu to include wishlist and cart
- Ensure proper spacing in mobile view
- Add touch-friendly interactions

## Accessibility Requirements

- Proper ARIA labels
- Keyboard navigation
- Focus management
- Screen reader friendly

## Testing Checklist

- [ ] Language selection persistence
- [ ] Wishlist add/remove functionality
- [ ] Cart add/remove functionality
- [ ] Mobile responsiveness
- [ ] Keyboard navigation
- [ ] Screen reader testing
- [ ] Cross-browser testing

## Future Enhancements

1. User Authentication Integration
   - Sync wishlist with user account
   - Sync cart with user account

2. Additional Languages
   - Add more language options
   - Implement proper i18n

3. Enhanced Cart Features
   - Save for later
   - Price calculations
   - Special offers integration
