# My Account Section Design Specification

## 1. Authentication Pages

### Login Page (`/auth/login`)
- Clean, centered card layout
- Email/password input fields
- "Remember me" checkbox
- Forgot password link
- Login button (full width)
- Social login buttons (optional)
- "Sign up" link for new users
- Error message handling
- Loading states for form submission

### Sign Up Page (`/auth/signup`)
- Matching style to login page
- Form fields:
  - First name
  - Last name
  - Email
  - Password
  - Confirm password
- Terms and conditions checkbox
- Newsletter signup option
- Sign up button (full width)
- "Login" link for existing users

## 2. Account Dashboard Layout

### Header Section
```html
- User welcome message
- Quick stats (e.g., upcoming trips)
- Profile completion progress bar
- Notification bell icon with counter
```

### Navigation Sidebar
```html
- User avatar and name
- Menu items:
  - Dashboard (home icon)
  - My Bookings (calendar icon)
  - Wishlist (heart icon)
  - Profile (user icon)
  - Settings (gear icon)
- Logout button at bottom
```

### Mobile Navigation
- Hamburger menu for sidebar
- Bottom navigation bar with key actions
- Responsive transitions

## 3. Main Dashboard Page (`/account`)

### Overview Section
```html
- Welcome message with user's name
- Time-based greeting (Good morning/afternoon/evening)
- Quick action buttons:
  - Book a Stay
  - View Bookings
  - Edit Profile
  - Contact Support
```

### Upcoming Trips Section
```html
- Card layout for each booking
- Property image
- Check-in/out dates
- Location
- Booking status
- Quick actions (view details, modify)
```

### Recent Activity
- Timeline layout
- Latest bookings
- Wishlist additions
- Profile updates

## 4. Bookings Page (`/account/bookings`)

### Booking List View
```html
- Tabs for:
  - Upcoming
  - Past
  - Cancelled
- Search/filter options
- Sorting options (date, price)
- Booking cards with:
  - Property image
  - Dates
  - Location
  - Price
  - Status badge
  - Action buttons
```

### Booking Details Modal
- Large property image
- Booking details
- Check-in instructions
- Cancellation policy
- Support contact
- Action buttons

## 5. Profile Page (`/account/profile`)

### Personal Information Section
```html
- Profile photo upload/edit
- Form fields:
  - First name
  - Last name
  - Email
  - Phone
  - Date of birth
  - Address
- Save changes button
```

### Preferences Section
- Language selection
- Currency preference
- Newsletter preferences
- Notification settings

## 6. Settings Page (`/account/settings`)

### Account Settings
```html
- Password change form
- Email preferences
- Privacy settings
- Delete account option
```

### Notification Preferences
- Email notifications toggle
- Push notifications toggle
- Marketing preferences
- Custom notification settings

## 7. Wishlist Page (`/account/wishlist`)

### Grid Layout
```html
- Property cards with:
  - Large image
  - Property name
  - Location
  - Price
  - Remove button
  - Book now button
```

## 8. Common UI Elements

### Buttons
```html
Primary Button:
- Blue background (#285DA6)
- White text
- Hover effect
- Loading state

Secondary Button:
- White background
- Blue border
- Blue text
- Hover effect
```

### Forms
```html
Input Fields:
- Floating labels
- Validation states
- Error messages
- Helper text
- Icon support

Dropdowns:
- Custom styling
- Search functionality
- Multiple selection
```

### Cards
```html
- Consistent shadow
- Rounded corners
- Hover effects
- Responsive scaling
```

### Status Badges
```html
- Confirmed (Green)
- Pending (Yellow)
- Cancelled (Red)
- Completed (Blue)
```

## 9. Responsive Breakpoints

```css
- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px
```

## 10. Color Scheme
```css
- Primary: #285DA6
- Secondary: #F8F9FA
- Success: #10B981
- Warning: #F59E0B
- Error: #EF4444
- Text: #1F2937
- Background: #FFFFFF
```

## 11. Typography
```css
- Headings: Baskervville
- Body: Karla
- Sizes:
  - h1: 2.5rem
  - h2: 2rem
  - h3: 1.75rem
  - body: 1rem
```

## 12. Animations
- Smooth page transitions
- Loading spinners
- Form submission feedback
- Modal transitions
- Toast notifications