/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}",
    "./public/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}",
    "./*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}",
    "./node_modules/@astrojs/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}",
  ],
  darkMode: ["class"],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "var(--border)",
        input: "var(--input)",
        ring: "var(--ring)",
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: {
          DEFAULT: "var(--primary)",
          foreground: "var(--primary-foreground)",
          90: "color-mix(in srgb, var(--primary) 90%, transparent)",
          80: "color-mix(in srgb, var(--primary) 80%, transparent)",
          70: "color-mix(in srgb, var(--primary) 70%, transparent)",
          60: "color-mix(in srgb, var(--primary) 60%, transparent)",
          50: "color-mix(in srgb, var(--primary) 50%, transparent)",
          40: "color-mix(in srgb, var(--primary) 40%, transparent)",
          30: "color-mix(in srgb, var(--primary) 30%, transparent)",
          20: "color-mix(in srgb, var(--primary) 20%, transparent)",
          10: "color-mix(in srgb, var(--primary) 10%, transparent)",
        },
        secondary: {
          DEFAULT: "var(--secondary)",
          foreground: "var(--secondary-foreground)",
        },
        destructive: {
          DEFAULT: "var(--destructive)",
          foreground: "var(--destructive-foreground)",
        },
        muted: {
          DEFAULT: "var(--muted)",
          foreground: "var(--muted-foreground)",
        },
        accent: {
          DEFAULT: "var(--accent)",
          foreground: "var(--accent-foreground)",
          90: "color-mix(in srgb, var(--accent) 90%, transparent)",
          80: "color-mix(in srgb, var(--accent) 80%, transparent)",
          70: "color-mix(in srgb, var(--accent) 70%, transparent)",
          60: "color-mix(in srgb, var(--accent) 60%, transparent)",
          50: "color-mix(in srgb, var(--accent) 50%, transparent)",
          40: "color-mix(in srgb, var(--accent) 40%, transparent)",
          30: "color-mix(in srgb, var(--accent) 30%, transparent)",
          20: "color-mix(in srgb, var(--accent) 20%, transparent)",
          10: "color-mix(in srgb, var(--accent) 10%, transparent)",
        },
        popover: {
          DEFAULT: "var(--popover)",
          foreground: "var(--popover-foreground)",
        },
        card: {
          DEFAULT: "var(--card)",
          foreground: "var(--card-foreground)",
        },
      },
      fontFamily: {
        baskervville: ["Baskervville", "serif"],
        karla: ["Karla", "sans-serif"],
        "funktional-grotesk": ["Funktional Grotesk", "Karla", "sans-serif"],
      },
      fontVariantNumeric: {
        'lining': 'lining-nums',
        'oldstyle': 'oldstyle-nums',
        'proportional': 'proportional-nums',
        'tabular': 'tabular-nums',
        'diagonal-fractions': 'diagonal-fractions',
        'stacked-fractions': 'stacked-fractions',
        'ordinal': 'ordinal',
        'slashed-zero': 'slashed-zero',
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 1px)",
        sm: "calc(var(--radius) - 2px)",
        DEFAULT: "var(--radius)",
      },
      transitionProperty: {
        colors:
          "background-color, border-color, color, fill, stroke, var(--background), var(--foreground), var(--primary), var(--secondary), var(--accent)",
        all: "all",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "fade-in": {
          "0%": {
            opacity: "0",
          },
          "100%": {
            opacity: "1",
          },
        },
        "fadeIn": {
          "0%": {
            opacity: "0",
            backdropFilter: "blur(0px)",
          },
          "100%": {
            opacity: "1",
            backdropFilter: "blur(4px)",
          },
        },
        "scaleIn": {
          "0%": {
            opacity: "0",
            transform: "scale(0.95)",
          },
          "100%": {
            opacity: "1",
            transform: "scale(1)",
          },
        },
        "fade-in-up": {
          "0%": {
            opacity: "0",
            transform: "translateY(20px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "slide-up": {
          "0%": {
            transform: "translateY(100%)",
          },
          "100%": {
            transform: "translateY(0)",
          },
        },
        "slide-down": {
          "0%": { transform: "translateY(-10px)", opacity: 0 },
          "100%": { transform: "translateY(0)", opacity: 1 },
        },
        "pan-image": {
          "0%": {
            transform: "scale(1.05) translate(0, 0)",
          },
          "33%": {
            transform: "scale(1.05) translate(-1%, -1%)",
          },
          "66%": {
            transform: "scale(1.05) translate(1%, 1%)",
          },
          "100%": {
            transform: "scale(1.05) translate(0, 0)",
          },
        },
        float: {
          "0%": {
            transform: "translateY(0px)",
          },
          "50%": {
            transform: "translateY(-10px)",
          },
          "100%": {
            transform: "translateY(0px)",
          },
        },
        fadeIn: {
          "0%": {
            opacity: "0",
            transform: "translateY(10px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in": "fade-in 0.5s ease-out forwards",
        "fade-in-up": "fade-in-up 0.7s ease-out forwards",
        "slide-up": "slide-up 0.5s ease-out forwards",
        "slide-down": "slide-down 0.2s ease-out",
        "pan-image": "pan-image 30s ease-in-out infinite",
        float: "float 6s ease-in-out infinite",
        fadeIn: "fadeIn 0.5s ease-in-out forwards",
        scaleIn: "scaleIn 0.3s ease-out forwards",
      },
      boxShadow: {
        glow: "0 0 15px rgba(14, 113, 179, 0.3)",
        "glow-lg": "0 0 30px rgba(14, 113, 179, 0.4)",
      },
      textColor: {
        foreground: "var(--foreground)",
      },
      opacity: {
        70: "0.7",
      },
      padding: {
        24: "0 3rem",
      },
      maxWidth: {
        "1440px": "1440px",
      },
      zIndex: {
        100: "100",
      },
    },
  },
  plugins: [],
};
